package com.ruoyi.talent.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.enums.PersonalIdSourceEnum;
import com.ruoyi.talentbase.domain.vo.ResumeInfoVo;
import com.ruoyi.talentbase.domain.vo.TbPersonalGetInfoVo;
import com.ruoyi.talentbase.service.ITbPersonalInfoService;
import com.ruoyi.talentbase.service.ITbpPositionBaseService;
import com.ruoyi.talentbase.service.ITbpResumeEvaluateService;
import com.ruoyi.talentbase.utils.TalentAgentApiUtil;
import io.swagger.annotations.Api;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 简历评估信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-18
 */
@Api(tags = "简历评估信息")
@RestController
@RequestMapping("/talentbase/evaluate")
public class TbpResumeEvaluateController extends BaseController {
    @Autowired
    private ITbpResumeEvaluateService tbpResumeEvaluateService;
    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;
    @Autowired
    private ITbpPositionBaseService tbpPositionBaseService;
    @Autowired
    private TalentAgentApiUtil talentAgentApiUtil;

    /**
     * 获取简历评估信息详细信息
     */
    @GetMapping(value = "/{talentId}")
    public AjaxResult getInfo(@PathVariable("talentId") Long talentId) {
        TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(talentId);
        TbpResumeEvaluate evaluate = tbpResumeEvaluateService.selectTbpResumeEvaluateByTalentId(talentId);
        if (evaluate == null && personalInfo != null) {
            return AjaxResult.success("未评价", personalInfo.getEvaluateStatus());
        }
        if (evaluate != null) {
            evaluate.setWorkExperienceWeight(20);
            evaluate.setJobHoppingRateWeight(20);
            evaluate.setSalaryRangeWeight(20);
            evaluate.setEducationWeight(20);
        }
        return success(evaluate);
    }

    /**
     * 重新生成简历评价
     */
    @GetMapping("/{talentId}/reEvaluate")
    public AjaxResult reEvaluate(@PathVariable("talentId") Long talentId) {
        tbpResumeEvaluateService.reEvaluate(talentId);
        return AjaxResult.success("重新评价成功");
    }
}