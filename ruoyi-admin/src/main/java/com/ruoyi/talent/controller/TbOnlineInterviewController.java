package com.ruoyi.talent.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.SysConfig;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.entity.domain.talent.TbInterviewJobPaper;
import com.ruoyi.common.entity.domain.talent.TbOnlineInterview;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewAddDTO;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewSyncDTO;
import com.ruoyi.talentbase.python.InterviewConfigRequest;
import com.ruoyi.talentbase.domain.enums.FiledFlagEnum;
import com.ruoyi.common.entity.domain.talent.InterviewStatusEnum;
import com.ruoyi.talentbase.domain.enums.StorageDisplayEnum;
import com.ruoyi.talentbase.domain.enums.TalentSourceEnum;
import com.ruoyi.talentbase.python.PythonApiUtil;
import com.ruoyi.talentbase.schedule.InterviewExpirationScheduler;
import com.ruoyi.talentbase.service.*;
import com.ruoyi.talentbase.utils.TPInfoUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/talentbase/onlineInterview")
public class TbOnlineInterviewController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(TbOnlineInterviewController.class);

    @Autowired
    private ITbOnlineInterviewService service;
    @Autowired
    private ITbOfflineInterviewService offlineInterviewService;

    @Autowired
    private ITbTaskPersonalInfoService taskPersonalInfoService;

    @Autowired
    private ITbPersonalInfoService personalInfoService;

    @Autowired
    private ITbInterviewJobPaperService jobPaperService;

    @Autowired
    private ITbInterviewJobPaperQuestionService jobPaperQuestionService;

    @Autowired
    private PythonApiUtil pythonApiUtil;

    @Autowired
    private IOnlineInterviewSyncService onlineInterviewSyncService;

    @Autowired
    private ISysDictDataService sysDictDataService;

    @Autowired
    private InterviewExpirationScheduler interviewExpirationScheduler;

    @Autowired
    private ITbAnalysePersonalInfoService analysePersonalInfoService;

    @Autowired
    private ITbUserInfoService userInfoService;

    @Autowired
    private ITbUserUndergoService userUndergoService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private ISysConfigService sysConfigService;

    @GetMapping("/list")
    public TableDataInfo list(OnlineInterviewQueryDTO queryDTO) {
        startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<TbOnlineInterview> list = service.selectTbOnlineInterviewList(queryDTO);

        // 计算邀请链接过期时间点: 邀请时间 + 岗位试卷有效天数(validDays)
        if (list != null && !list.isEmpty()) {
            for (TbOnlineInterview interview : list) {
                if (interview == null || interview.getInviteTime() == null || interview.getJobPaperId() == null) {
                    continue;
                }
                if (StringUtils.isNotEmpty(interview.getEducation())) {
                    interview.setEducationName(sysDictDataService.selectDictLabel("education", interview.getEducation()));
                }
                TbInterviewJobPaper jobPaper = jobPaperService.selectTbInterviewJobPaperById(interview.getJobPaperId());
                if (jobPaper != null && jobPaper.getValidDays() != null) {
                    interview.setInterviewJobPaper(jobPaper);
                    if (interview.getInterviewStatus() == InterviewStatusEnum.EXPIRED.getCode()) {
                        interview.setInviteExpireTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getUpdateTime()));
                    } else {
                        Date expireDate = DateUtils.addDays(interview.getInviteTime(), jobPaper.getValidDays());
                        interview.setInviteExpireTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", expireDate));
                    }
                    // 设置部门名称
                    if (jobPaper.getDeptId() != null) {
                        SysDept dept = sysDeptService.selectDeptById(jobPaper.getDeptId());
                        if (dept != null) {
                            interview.setDeptName(dept.getDeptName());
                        }
                    }
                }
                String domain = sysConfigService.selectConfigByKey("hr.interview.url");
                interview.setInviteUrl(domain + interview.getInviteUrl());
            }
        }

        return getDataTable(list);
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(service.selectTbOnlineInterviewById(id));
    }

    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody OnlineInterviewAddDTO dto) {
        if (dto.getJobPaperId() == null) {
            return AjaxResult.error("岗位试卷ID不能为空");
        }
        if (dto.getPersons() == null || dto.getPersons().isEmpty()) {
            return AjaxResult.error("面试人员列表不能为空");
        }

        String username = SecurityUtils.getUsername();
        String nickname = SecurityUtils.getNickname();
        List<TbOnlineInterview> addedInterviews = new ArrayList<>();

        for (OnlineInterviewAddDTO.InterviewPerson person : dto.getPersons()) {
            TbOnlineInterview interview = new TbOnlineInterview();

            // 根据来源类型获取人员信息
            if (person.getSourceType().equals(TalentSourceEnum.TALENT_POOL.getCode())) {
                // 人才库
                TbPersonalInfo personalInfo = personalInfoService.selectTbPersonalInfoById(person.getSourceId());
                if (personalInfo == null) {
                    return AjaxResult.error("未找到人才库人员信息，ID: " + person.getSourceId());
                }
                // 设置人才库信息
                interview.setAvatarUrl(personalInfo.getAvatar());
                interview.setName(personalInfo.getUserName());
                interview.setGender(personalInfo.getSex());
                interview.setEducation(personalInfo.getEducation());
                if (StringUtils.isNotEmpty(personalInfo.getEducation())) {
                    interview.setEducationName(sysDictDataService.selectDictLabel("education", personalInfo.getEducation()));
                }
                interview.setAge(personalInfo.getAge());
                interview.setJobIntention(personalInfo.getJobIntent());
                interview.setTalentSource(personalInfo.getRecruitmentChannel());
                interview.setOnlineInterviewSource(TalentSourceEnum.TALENT_POOL.getCode());
                interview.setOnlineInterviewSourceId(person.getSourceId());
            } else if (person.getSourceType().equals(TalentSourceEnum.TASK_POOL.getCode())) {
                // 寻才库
                TbTaskPersonalInfo taskPersonalInfo = taskPersonalInfoService.selectTbTaskPersonalInfoById(person.getSourceId());
                if (taskPersonalInfo == null) {
                    return AjaxResult.error("未找到寻才库人员信息，ID: " + person.getSourceId());
                }
                // 设置寻才库信息
                interview.setAvatarUrl(taskPersonalInfo.getAvatar());
                interview.setName(taskPersonalInfo.getUserName());
                interview.setGender(taskPersonalInfo.getSex());
                interview.setEducation(taskPersonalInfo.getEducation());

                if (StringUtils.isNotEmpty(taskPersonalInfo.getEducation())) {
                    interview.setEducationName(sysDictDataService.selectDictLabel("education", taskPersonalInfo.getEducation()));

                }
                interview.setAge(taskPersonalInfo.getAge());
                interview.setJobIntention(taskPersonalInfo.getJobIntent());
                interview.setTalentSource(taskPersonalInfo.getRecruitmentChannel());
                interview.setOnlineInterviewSource(TalentSourceEnum.TASK_POOL.getCode());
                interview.setOnlineInterviewSourceId(person.getSourceId());
            } else if (person.getSourceType().equals(TalentSourceEnum.RESUME_POOL.getCode())) {
                // 简历库
                TbAnalysePersonalInfo analysePersonalInfo = analysePersonalInfoService.selectTbAnalysePersonalInfoById(person.getSourceId());
                if (analysePersonalInfo == null) {
                    return AjaxResult.error("未找到简历库人员信息，ID: " + person.getSourceId());
                }
                // 设置简历库信息
                interview.setAvatarUrl(analysePersonalInfo.getAvatar());
                interview.setName(analysePersonalInfo.getUserName());
                interview.setGender(analysePersonalInfo.getSex());
                interview.setEducation(analysePersonalInfo.getEducation());
                if (StringUtils.isNotEmpty(analysePersonalInfo.getEducation())) {
                    interview.setEducationName(sysDictDataService.selectDictLabel("education", analysePersonalInfo.getEducation()));
                }
                interview.setAge(analysePersonalInfo.getAge());
                interview.setJobIntention(analysePersonalInfo.getJobIntent());
                interview.setTalentSource(analysePersonalInfo.getRecruitmentChannel());
                interview.setOnlineInterviewSource(TalentSourceEnum.RESUME_POOL.getCode());
                interview.setOnlineInterviewSourceId(person.getSourceId());
            } else {
                return AjaxResult.error("无效的来源类型: " + person.getSourceType());
            }

            // 设置通用字段
            interview.setId(null);
            interview.setAvatarRedDot(0);
            interview.setJobPaperId(dto.getJobPaperId());
            TbInterviewJobPaper interviewJobPaper = jobPaperService.selectTbInterviewJobPaperById(dto.getJobPaperId());
            interview.setInterviewJobPaper(interviewJobPaper);
            interview.setOperator(nickname);
            interview.setOperatorId(SecurityUtils.getUserId());
            interview.setInterviewStatus(InterviewStatusEnum.NOT_STARTED.getCode()); // 使用枚举设置初始状态
            interview.setCreateBy(username);
            Date now = DateUtils.getNowDate();
            interview.setCreateTime(now);
            // 设置邀请相关字段
            interview.setInviteTime(now);
            interview.setInviteUrl(UUID.randomUUID().toString());
            // 生成6位邀请码（数字+字母，区分大小写）
            String inviteCode = generateInviteCode();
            interview.setInviteCode(inviteCode);

            service.insertTbOnlineInterview(interview);
            TbInterviewJobPaper jobPaper = jobPaperService.selectTbInterviewJobPaperById(interview.getJobPaperId());
            if (jobPaper != null && jobPaper.getValidDays() != null) {
                interview.setInterviewJobPaper(jobPaper);
                Date expireDate = DateUtils.addDays(interview.getInviteTime(), jobPaper.getValidDays());
                interview.setInviteExpireTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", expireDate));
            }
            addedInterviews.add(interview);
        }

        // 同步新增的面试记录到外网
        List<OnlineInterviewSyncDTO> syncList = new ArrayList<>();
        String domain = sysConfigService.selectConfigByKey("hr.interview.url");
        for (TbOnlineInterview interview : addedInterviews) {
            OnlineInterviewSyncDTO syncDTO = onlineInterviewSyncService.getSyncData(interview.getId());
            if (syncDTO != null) {
                syncList.add(syncDTO);
            }
            interview.setInviteUrl(domain + interview.getInviteUrl());
        }
        if (!syncList.isEmpty()) {
            pythonApiUtil.submitOnlineInterviewSyncList(syncList);
        }

        // 设置过期检查任务
        for (TbOnlineInterview interview : addedInterviews) {
            interviewExpirationScheduler.scheduleInterviewExpiration(interview.getId(), interview.getInviteTime(), interview.getJobPaperId());
        }

        return AjaxResult.success(addedInterviews);
    }

    /**
     * 生成6位邀请码（数字+字母，区分大小写）
     */
    private String generateInviteCode() {
        String chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        StringBuilder code = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 6; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }
        return code.toString();
    }

    @PutMapping
    public AjaxResult edit(@RequestBody TbOnlineInterview interview) {
        return toAjax(service.updateTbOnlineInterview(interview));
    }

    @DeleteMapping("/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult remove(@PathVariable Long[] ids) {
        // 取消过期检查任务
        for (Long id : ids) {
            interviewExpirationScheduler.cancelExpirationCheck(id);
            // 检查线上线下面试、人才库、档案库信息是否存在
            TbOnlineInterview interview = service.selectTbOnlineInterviewById(id);
            // 查询是否是人才库面试
            if (interview.getOnlineInterviewSource().equals(TalentSourceEnum.TALENT_POOL.getCode())) {
                Long sourceId = interview.getOnlineInterviewSourceId();
                List<TbOnlineInterview> interviews = service.selectInterviewListByPerId(sourceId);
                List<TbOfflineInterview> offlineInterviews = offlineInterviewService.selectInterviewListByPerId(sourceId);
                TbPersonalInfo personalInfo = personalInfoService.selectTbPersonalInfoById(sourceId);
                if (interviews != null && interviews.size() == 1 && offlineInterviews.isEmpty()
                        && personalInfo != null
                        && personalInfo.getStorageDisplay().equals(StorageDisplayEnum.NOT_STORAGE_DISPLAY.getCode())
                        && personalInfo.getFiledFlag().equals(FiledFlagEnum.NOT_FILED_FLAG.getCode())){
                    personalInfoService.removeAll(personalInfo.getId());
                    // 删除档案相关数据
                    userInfoService.deleteTbUserInfoByPersonalIds(new Long[]{personalInfo.getId()});
                    // 删除履历相关数据
                    userUndergoService.deleteByPInfoId(personalInfo.getId());
                }
            }
        }

        // 根据本地面试ID获取对应的 inviteUrl 列表
        String[] inviteUrls = Arrays.stream(ids)
                .map(id -> {
                    TbOnlineInterview interview = service.selectTbOnlineInterviewById(id);
                    return interview != null ? interview.getInviteUrl() : null;
                })
                .filter(url -> StringUtils.isNotBlank(url))
                .toArray(String[] :: new);

        // 先删除外网数据
        boolean externalResult = pythonApiUtil.deleteOnlineInterviews(inviteUrls);
        if (!externalResult) {
            return AjaxResult.error("删除外网面试记录失败");
        }
        int row = service.deleteTbOnlineInterviewByIds(ids);
        if (row > 0){
            // 删除待办信息
            for (Long id : ids) {
                TPInfoUtils.removeOnlineInterviewTodo(id);
            }
        }
        // 删除本地数据
        return toAjax(row);
    }

    @PutMapping("/batchEdit")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult batchEdit(@RequestParam Long newJobPaperId, @RequestParam Long[] interviewIds) {
        if (newJobPaperId == null) {
            return AjaxResult.error("新岗位试卷ID不能为空");
        }
        if (interviewIds == null || interviewIds.length == 0) {
            return AjaxResult.error("面试记录ID数组不能为空");
        }

        String username = SecurityUtils.getUsername();
        String nickname = SecurityUtils.getNickname();
        Long oldJobPaperId = null;
        List<TbOnlineInterview> updatedInterviews = new ArrayList<>();

        for (Long interviewId : interviewIds) {
            // 1. 查询原面试记录
            TbOnlineInterview interview = service.selectTbOnlineInterviewById(interviewId);
            if (interview == null) {
                return AjaxResult.error("未找到面试记录，ID: " + interviewId);
            }

            // 记录原岗位试卷ID
            if (oldJobPaperId == null) {
                oldJobPaperId = interview.getJobPaperId();
            }

            // 2. 根据来源类型获取最新人员信息
            if (interview.getOnlineInterviewSource().equals(TalentSourceEnum.TALENT_POOL.getCode())) {
                // 人才库
                TbPersonalInfo personalInfo = personalInfoService.selectTbPersonalInfoById(interview.getOnlineInterviewSourceId());
                if (personalInfo == null) {
                    return AjaxResult.error("未找到人才库人员信息，ID: " + interview.getOnlineInterviewSourceId());
                }
                // 更新人才库信息
                interview.setAvatarUrl(personalInfo.getAvatar());
                interview.setName(personalInfo.getUserName());
                interview.setGender(personalInfo.getSex());
                interview.setEducation(personalInfo.getEducation());
                interview.setAge(personalInfo.getAge());
                interview.setJobIntention(personalInfo.getJobIntent());
            } else if (interview.getOnlineInterviewSource().equals(TalentSourceEnum.TASK_POOL.getCode())) {
                // 寻才库
                TbTaskPersonalInfo taskPersonalInfo = taskPersonalInfoService.selectTbTaskPersonalInfoById(interview.getOnlineInterviewSourceId());
                if (taskPersonalInfo == null) {
                    return AjaxResult.error("未找到寻才库人员信息，ID: " + interview.getOnlineInterviewSourceId());
                }
                // 更新寻才库信息
                interview.setAvatarUrl(taskPersonalInfo.getAvatar());
                interview.setName(taskPersonalInfo.getUserName());
                interview.setGender(taskPersonalInfo.getSex());
                interview.setEducation(taskPersonalInfo.getEducation());
                interview.setAge(taskPersonalInfo.getAge());
                interview.setJobIntention(taskPersonalInfo.getJobIntent());
            } else if (interview.getOnlineInterviewSource().equals(TalentSourceEnum.RESUME_POOL.getCode())) {
                // 简历库
                TbAnalysePersonalInfo analysePersonalInfo = analysePersonalInfoService.selectTbAnalysePersonalInfoById(interview.getOnlineInterviewSourceId());
                if (analysePersonalInfo == null) {
                    return AjaxResult.error("未找到简历库人员信息，ID: " + interview.getOnlineInterviewSourceId());
                }
                // 更新简历库信息
                interview.setAvatarUrl(analysePersonalInfo.getAvatar());
                interview.setName(analysePersonalInfo.getUserName());
                interview.setGender(analysePersonalInfo.getSex());
                interview.setEducation(analysePersonalInfo.getEducation());
                interview.setAge(analysePersonalInfo.getAge());
                interview.setJobIntention(analysePersonalInfo.getJobIntent());
            }

            // 3. 更新记录信息
            interview.setJobPaperId(newJobPaperId);
            interview.setOperator(nickname); // 更新操作人
            interview.setOperatorId(SecurityUtils.getUserId()); // 更新操作人Id
            interview.setUpdateBy(username);
            interview.setUpdateTime(DateUtils.getNowDate());

            // 更新记录
            service.updateTbOnlineInterview(interview);
            updatedInterviews.add(interview);
        }

        // 同步更新后的面试记录到外网
        List<OnlineInterviewSyncDTO> syncList = new ArrayList<>();
        for (TbOnlineInterview interview : updatedInterviews) {
            OnlineInterviewSyncDTO syncDTO = onlineInterviewSyncService.getSyncData(interview.getId());
            if (syncDTO != null) {
                syncList.add(syncDTO);
            }
        }
        if (!syncList.isEmpty()) {
            pythonApiUtil.submitOnlineInterviewSyncList(syncList);
        }

        // 4. 检查原岗位试卷是否还有其他面试记录使用
        if (oldJobPaperId != null) {
            OnlineInterviewQueryDTO queryDTO = new OnlineInterviewQueryDTO();
            queryDTO.setJobPaperId(oldJobPaperId);
            List<TbOnlineInterview> remainingInterviews = service.selectTbOnlineInterviewList(queryDTO);

            // 如果没有其他面试记录使用该试卷，则删除试卷及其问题
            if (remainingInterviews.isEmpty()) {
                // 直接删除试卷下的所有问题
                jobPaperQuestionService.deleteTbInterviewJobPaperQuestionByJobPaperId(oldJobPaperId);
                // 删除试卷
                jobPaperService.deleteTbInterviewJobPaperById(oldJobPaperId);
            }
        }

        // 设置过期检查任务
        for (TbOnlineInterview interview : updatedInterviews) {
            interviewExpirationScheduler.scheduleInterviewExpiration(interview.getId(), interview.getInviteTime(), interview.getJobPaperId());
        }

        return AjaxResult.success();
    }

    @GetMapping("/personDetail/{id}")
    public AjaxResult getPersonDetail(@PathVariable("id") Long id) {
        TbOnlineInterview interview = service.selectTbOnlineInterviewById(id);
        if (interview == null) {
            return AjaxResult.error("未找到面试记录");
        }

        // 更新红点状态为已读
        interview.setAvatarRedDot(0);
        interview.setUpdateBy(SecurityUtils.getUsername());
        interview.setUpdateTime(DateUtils.getNowDate());
        service.updateTbOnlineInterview(interview);

        // 根据来源类型获取最新人员信息
        if (interview.getOnlineInterviewSource().equals(TalentSourceEnum.TALENT_POOL.getCode())) {
            // 人才库
            TbPersonalInfo personalInfo = personalInfoService.selectTbPersonalInfoById(interview.getOnlineInterviewSourceId());
            if (personalInfo == null) {
                return AjaxResult.error("未找到人才库人员信息，ID: " + interview.getOnlineInterviewSourceId());
            }
            // 更新人才库信息
            interview.setAvatarUrl(personalInfo.getAvatar());
            interview.setName(personalInfo.getUserName());
            interview.setGender(personalInfo.getSex());
            interview.setEducation(personalInfo.getEducation());
            interview.setAge(personalInfo.getAge());
            interview.setJobIntention(personalInfo.getJobIntent());
        } else if (interview.getOnlineInterviewSource().equals(TalentSourceEnum.TASK_POOL.getCode())) {
            // 寻才库
            TbTaskPersonalInfo taskPersonalInfo = taskPersonalInfoService.selectTbTaskPersonalInfoById(interview.getOnlineInterviewSourceId());
            if (taskPersonalInfo == null) {
                return AjaxResult.error("未找到寻才库人员信息，ID: " + interview.getOnlineInterviewSourceId());
            }
            // 更新寻才库信息
            interview.setAvatarUrl(taskPersonalInfo.getAvatar());
            interview.setName(taskPersonalInfo.getUserName());
            interview.setGender(taskPersonalInfo.getSex());
            interview.setEducation(taskPersonalInfo.getEducation());
            interview.setAge(taskPersonalInfo.getAge());
            interview.setJobIntention(taskPersonalInfo.getJobIntent());
        } else if (interview.getOnlineInterviewSource().equals(TalentSourceEnum.RESUME_POOL.getCode())) {
            // 简历库
            TbAnalysePersonalInfo analysePersonalInfo = analysePersonalInfoService.selectTbAnalysePersonalInfoById(interview.getOnlineInterviewSourceId());
            if (analysePersonalInfo == null) {
                return AjaxResult.error("未找到简历库人员信息，ID: " + interview.getOnlineInterviewSourceId());
            }
            // 更新简历库信息
            interview.setAvatarUrl(analysePersonalInfo.getAvatar());
            interview.setName(analysePersonalInfo.getUserName());
            interview.setGender(analysePersonalInfo.getSex());
            interview.setEducation(analysePersonalInfo.getEducation());
            interview.setAge(analysePersonalInfo.getAge());
            interview.setJobIntention(analysePersonalInfo.getJobIntent());
        }

        return AjaxResult.success(interview);
    }

    /**
     * 查询线上面试配置信息
     * @return 开场话术和应聘者解答信息
     */
    @GetMapping("/config")
    public AjaxResult getOnlineInterviewConfig() {
        try {
            Map<String, String> configMap = new HashMap<>();
            
            // 查询开场话术配置
            String openingValue = sysConfigService.selectConfigByKey("talentbase.online.info.opening");
            configMap.put("opening", openingValue != null ? openingValue : "");
            
            // 查询应聘者解答信息配置
            String companyValue = sysConfigService.selectConfigByKey("talentbase.online.info.company");
            configMap.put("company", companyValue != null ? companyValue : "");
            
            return AjaxResult.success(configMap);
        } catch (Exception e) {
            return AjaxResult.error("查询线上面试配置失败：" + e.getMessage());
        }
    }

    /**
     * 更新线上面试配置信息
     * @param configMap 包含 opening 和 company 的配置信息
     * @return 更新结果
     */
    @PutMapping("/config")
    public AjaxResult updateOnlineInterviewConfig(@RequestBody Map<String, String> configMap) {
        try {
            String username = SecurityUtils.getUsername();
            Date now = DateUtils.getNowDate();
            boolean localUpdateSuccess = true;
            String openingText = null;
            String companyInfo = null;
            
            // 更新开场话术
            if (configMap.containsKey("opening")) {
                openingText = configMap.get("opening");
                // 先查询现有配置
                SysConfig openingConfig = new SysConfig();
                openingConfig.setConfigKey("talentbase.online.info.opening");
                List<SysConfig> openingConfigs = sysConfigService.selectConfigList(openingConfig);
                
                if (!openingConfigs.isEmpty()) {
                    // 更新现有配置
                    openingConfig = openingConfigs.get(0);
                    openingConfig.setConfigValue(openingText);
                    openingConfig.setUpdateBy(username);
                    openingConfig.setUpdateTime(now);
                    int result = sysConfigService.updateConfig(openingConfig);
                    if (result <= 0) {
                        localUpdateSuccess = false;
                    }
                } else {
                    // 创建新配置
                    openingConfig = new SysConfig();
                    openingConfig.setConfigName("线上面试开场话术");
                    openingConfig.setConfigKey("talentbase.online.info.opening");
                    openingConfig.setConfigValue(openingText);
                    openingConfig.setConfigType("Y");
                    openingConfig.setCreateBy(username);
                    openingConfig.setCreateTime(now);
                    int result = sysConfigService.insertConfig(openingConfig);
                    if (result <= 0) {
                        localUpdateSuccess = false;
                    }
                }
            }
            
            // 更新应聘者解答信息
            // 如果configMap中包含company键，使用传递的值（包括空字符串和null值）
            // 如果configMap中不包含company键，则更新为空字符串
            if (configMap.containsKey("company")) {
                companyInfo = configMap.get("company");
                // 如果值为null，转换为空字符串
                if (companyInfo == null) {
                    companyInfo = "";
                }
            } else {
                // 前端不传递company参数时，设置为空字符串
                companyInfo = "";
            }
            
            // 先查询现有配置
            SysConfig companyConfig = new SysConfig();
            companyConfig.setConfigKey("talentbase.online.info.company");
            List<SysConfig> companyConfigs = sysConfigService.selectConfigList(companyConfig);
            
            if (!companyConfigs.isEmpty()) {
                // 更新现有配置
                companyConfig = companyConfigs.get(0);
                companyConfig.setConfigValue(companyInfo);
                companyConfig.setUpdateBy(username);
                companyConfig.setUpdateTime(now);
                int result = sysConfigService.updateConfig(companyConfig);
                if (result <= 0) {
                    localUpdateSuccess = false;
                }
            } else {
                // 创建新配置
                companyConfig = new SysConfig();
                companyConfig.setConfigName("线上面试应聘者解答信息");
                companyConfig.setConfigKey("talentbase.online.info.company");
                companyConfig.setConfigValue(companyInfo);
                companyConfig.setConfigType("Y");
                companyConfig.setCreateBy(username);
                companyConfig.setCreateTime(now);
                int result = sysConfigService.insertConfig(companyConfig);
                if (result <= 0) {
                    localUpdateSuccess = false;
                }
            }
            
            // 检查本地更新是否成功
            if (!localUpdateSuccess) {
                return AjaxResult.error("本地配置更新失败");
            }
            
            // 调用Python服务接口更新配置
            // 由于company配置总是会被更新，所以总是需要调用Python接口
            if (openingText == null) {
                // 如果只更新了company，需要获取opening的当前值
                openingText = sysConfigService.selectConfigByKey("talentbase.online.info.opening");
                if (openingText == null) {
                    openingText = "";
                }
            }
            
            // 调用Python服务接口
            InterviewConfigRequest configRequest = new InterviewConfigRequest(openingText, companyInfo);
            boolean pythonUpdateSuccess = pythonApiUtil.configInterview(configRequest);
            if (!pythonUpdateSuccess) {
                log.warn("Python服务配置更新失败，但本地配置已更新成功");
                return AjaxResult.success("本地配置更新成功，但Python服务配置更新失败，请检查Python服务状态");
            }
            
            return AjaxResult.success("线上面试配置更新成功");
        } catch (Exception e) {
            log.error("更新线上面试配置失败", e);
            return AjaxResult.error("更新线上面试配置失败：" + e.getMessage());
        }
    }
}