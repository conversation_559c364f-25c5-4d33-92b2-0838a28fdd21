package com.ruoyi.talent.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.PdInfoType;
import com.ruoyi.common.entity.domain.PdTaskInfo;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.entity.domain.TPInfoEventEnum;
import com.ruoyi.common.entity.domain.system.LoginUser;
import com.ruoyi.common.entity.domain.talent.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.security.utils.UserRedisUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.talentbase.domain.vo.PdTaskInfoVo;
import com.ruoyi.talentbase.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 待办任务信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@RestController
@RequestMapping("/talentbase/taskTodo")
public class PdTaskInfoController extends BaseController {
    @Autowired
    private IPdTaskInfoService pdTaskInfoService;
    @Autowired
    private ITbOnlineInterviewService tbOnlineInterviewService;
    @Autowired
    private ITbInterviewJobPaperService jobPaperService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ITbTaskPostingService tbTaskPostingService;
    @Autowired
    private ITbAnalyseTaskService tbAnalyseTaskService;

    private static final String LOBOR_CONTRACT_READ = "laborContractRead";


    /**
     * 查询待办任务信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PdTaskInfo pdTaskInfo) {
        startPage();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();
        pdTaskInfo.setUserId(userId);
        Map<String, Object> params = loginUser.getUserAdditionalParams();
        if (params != null) {
            pdTaskInfo.setShowRenewalFlag((Integer) params.get(LOBOR_CONTRACT_READ));
        }
        List<PdTaskInfoVo> list = pdTaskInfoService.selectPdTaskInfoVoList(pdTaskInfo);
        for (PdTaskInfoVo taskInfo : list) {
            TPInfoEventEnum eventEnum = TPInfoEventEnum.fromCode(taskInfo.getEventType());
            if (eventEnum == TPInfoEventEnum.KNOWLEDGE) {
                continue;
            }
            Long eventId = Long.valueOf(taskInfo.getEventId());
            switch (eventEnum) {
                case ONLINE_INTERVIEW:
                    // 应聘职位|拼接部门
                    TbOnlineInterview interview = tbOnlineInterviewService.selectTbOnlineInterviewById(eventId);
                    if (interview != null) {
                        TbInterviewJobPaper interviewJobPaper = jobPaperService.selectTbInterviewJobPaperById(interview.getJobPaperId());
                        taskInfo.setEventDesc(interviewJobPaper.getJobName());
                        // 设置部门名称
                        if (interviewJobPaper.getDeptId() != null) {
                            SysDept dept = sysDeptService.selectDeptById(interviewJobPaper.getDeptId());
                            if (dept != null) {
                                interview.setDeptName(dept.getDeptName());
                                taskInfo.setEventDesc(interviewJobPaper.getJobName() + "|" + interview.getDeptName());
                            }
                        }
                        taskInfo.setTbOnlineInterview(interview);
                    }
                    break;
                case TALENT_TASK_PROGRESS:
                    TbTaskPosting taskPosting = tbTaskPostingService.selectTbTaskPostingById(eventId);
                    taskInfo.setTbTaskPosting(taskPosting);
                    break;
                case RESUME_ANALYSIS_PROGRESS:
                    TbAnalyseTask analyseTask = tbAnalyseTaskService.selectTbAnalyseTaskById(eventId);
                    taskInfo.setTbAnalyseTask(analyseTask);
                    break;
                default:
                    break;
            }
        }
        return getDataTable(list);
    }


    /**
     * 获取待办和任务数量
     */
    @GetMapping("/tabCount")
    public AjaxResult tabCount() {
        // 查询任务数量
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();
        PdTaskInfo pdTaskInfo = new PdTaskInfo();
        pdTaskInfo.setUserId(userId);
        pdTaskInfo.setType(PdInfoType.TODO.ordinal());
        Map<String, Object> params = loginUser.getUserAdditionalParams();
        if (params != null) {
            pdTaskInfo.setShowRenewalFlag((Integer) params.get(LOBOR_CONTRACT_READ));
        }
        List<PdTaskInfoVo> list = pdTaskInfoService.selectPdTaskInfoVoList(pdTaskInfo);
        // 查看任务数量
        Integer taskCount = pdTaskInfoService.selectCountByUserId(userId, PdInfoType.TASK.ordinal());
        Map<String, Integer> map = new HashMap<>();
        map.put("taskCount", taskCount);
        map.put("todoCount", list.size());
        // 返回
        return AjaxResult.success(map);
    }


    /**
     * 删除待办任务信息
     */
    @Log(title = "待办任务信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        if (ids != null && ids.length == 1) {
            Long id = ids[0];
            PdTaskInfo taskInfo = pdTaskInfoService.selectPdTaskInfoById(id);
            if (taskInfo == null) {
                return AjaxResult.error("当前信息不存在");
            }
            if (taskInfo.getType() == 1 && taskInfo.getStatus() == TaskStatusEnum.IN_PROGRESS.getCode()) {
                return AjaxResult.error("当前任务状态不允许删除");
            }
            if (taskInfo.getEventType() == TPInfoEventEnum.CONTRACT_EXPIRED.getCode()) {
                // 更新用户缓存数据，增加合同已读标记
                LoginUser loginUser = SecurityUtils.getLoginUser();
                Map<String, Object> params = loginUser.getUserAdditionalParams();
                if (params == null) {
                    params = new HashMap<>();
                }
                params.put(LOBOR_CONTRACT_READ, taskInfo.getEventType());
                loginUser.setUserAdditionalParams(params);
                UserRedisUtils.resUserRedisInfo(loginUser);
                return AjaxResult.success();
            }
            return toAjax(pdTaskInfoService.deletePdTaskInfoById(id));
        }
        return AjaxResult.error("当前接口不支持批量操作");
    }

}