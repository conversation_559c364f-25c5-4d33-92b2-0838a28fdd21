package com.ruoyi.talent.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.talentbase.domain.TbEducationInfo;
import com.ruoyi.talentbase.service.ITbEducationInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 教育信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/talentbase/educationInfo")
public class TbEducationInfoController extends BaseController
{
    @Autowired
    private ITbEducationInfoService tbEducationInfoService;

    /**
     * 查询教育信息列表
     */
    @RequiresPermissions("talent:info:list")
    @GetMapping("/list")
    public TableDataInfo list(TbEducationInfo tbEducationInfo)
    {
        startPage();
        List<TbEducationInfo> list = tbEducationInfoService.selectTbEducationInfoList(tbEducationInfo);
        return getDataTable(list);
    }

    /**
     * 导出教育信息列表
     */
    @RequiresPermissions("talent:info:export")
    @Log(title = "教育信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbEducationInfo tbEducationInfo)
    {
        List<TbEducationInfo> list = tbEducationInfoService.selectTbEducationInfoList(tbEducationInfo);
        ExcelUtil<TbEducationInfo> util = new ExcelUtil<TbEducationInfo>(TbEducationInfo.class);
        util.exportExcel(response, list, "教育信息数据");
    }

    /**
     * 获取教育信息详细信息
     */
    @RequiresPermissions("talent:info:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tbEducationInfoService.selectTbEducationInfoById(id));
    }

    /**
     * 新增教育信息
     */
    @RequiresPermissions("talent:info:add")
    @Log(title = "教育信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbEducationInfo tbEducationInfo)
    {
        return toAjax(tbEducationInfoService.insertTbEducationInfo(tbEducationInfo));
    }

    /**
     * 修改教育信息
     */
    @RequiresPermissions("talent:info:edit")
    @Log(title = "教育信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbEducationInfo tbEducationInfo)
    {
        return toAjax(tbEducationInfoService.updateTbEducationInfo(tbEducationInfo));
    }

    /**
     * 删除教育信息
     */
    @RequiresPermissions("talent:info:remove")
    @Log(title = "教育信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbEducationInfoService.deleteTbEducationInfoByIds(ids));
    }
}
