package com.ruoyi.talent.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.entity.domain.talent.TbInterviewJobPaper;
import com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion;
import com.ruoyi.talentbase.domain.dto.InterviewJobPaperWithQuestionsDTO;
import com.ruoyi.talentbase.service.ITbInterviewJobPaperQuestionService;
import com.ruoyi.talentbase.service.ITbInterviewJobPaperService;
import com.ruoyi.talentbase.service.ITbOnlineInterviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/talentbase/interviewJobPaper")
public class TbInterviewJobPaperController extends BaseController {

    @Autowired
    private ITbInterviewJobPaperService service;

    @Autowired
    private ITbInterviewJobPaperQuestionService questionService;

    @Autowired
    private ITbOnlineInterviewService tbOnlineInterviewService;

    @GetMapping("/list")
    public TableDataInfo list(TbInterviewJobPaper query) {
        startPage();
        List<TbInterviewJobPaper> list = service.selectTbInterviewJobPaperList(query);
        return getDataTable(list);
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(service.selectTbInterviewJobPaperById(id));
    }

    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody InterviewJobPaperWithQuestionsDTO dto) {
        TbInterviewJobPaper paper = dto.getJobPaper();
        paper.setId(null);
        // 设置创建信息
        String username = SecurityUtils.getUsername();
        paper.setCreateBy(username);
        paper.setCreateTime(DateUtils.getNowDate());
        
        int result = service.insertTbInterviewJobPaper(paper);
        if (result > 0 && dto.getQuestions() != null && !dto.getQuestions().isEmpty()) {
            Long paperId = paper.getId();
            for (TbInterviewJobPaperQuestion q : dto.getQuestions()) {
                q.setId(null);
                q.setJobPaperId(paperId);
                // 设置创建信息
                q.setCreateBy(username);
                q.setCreateTime(DateUtils.getNowDate());
                questionService.insertTbInterviewJobPaperQuestion(q);
            }
        }
        return AjaxResult.success(paper.getId());
    }

    @PutMapping
    public AjaxResult edit(@RequestBody TbInterviewJobPaper paper) {
        return toAjax(service.updateTbInterviewJobPaper(paper));
    }

    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deleteTbInterviewJobPaperByIds(ids));
    }

    @GetMapping("/latestByJobName")
    public AjaxResult getLatestByJobName(@RequestParam(value = "jobName", required = true) String jobName) {
        try {
            TbInterviewJobPaper query = new TbInterviewJobPaper();
            query.setJobName(jobName);
            List<TbInterviewJobPaper> papers = service.selectTbInterviewJobPaperList(query);
            if (papers == null || papers.isEmpty()) {
                return AjaxResult.success();
            }
            // 假设最新一条为createTime最大
            TbInterviewJobPaper latest = papers.stream()
                .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                .findFirst().get();
            TbInterviewJobPaperQuestion questionQuery = new TbInterviewJobPaperQuestion();
            questionQuery.setJobPaperId(latest.getId());
            List<TbInterviewJobPaperQuestion> questions = questionService.selectTbInterviewJobPaperQuestionList(questionQuery);
            InterviewJobPaperWithQuestionsDTO dto = new InterviewJobPaperWithQuestionsDTO();
            dto.setJobPaper(latest);
            dto.setQuestions(questions);
            return AjaxResult.success(dto);
        } catch (Exception e) {
            return AjaxResult.error("获取岗位试卷失败：" + e.getMessage());
        }
    }
}