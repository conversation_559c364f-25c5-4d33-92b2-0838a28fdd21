package com.ruoyi.talent.controller;


import com.ruoyi.common.core.config.RuoYiConfig;
import com.ruoyi.common.core.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页
 *
 * <AUTHOR>
 */
@Api(tags = "首页访问提示语API")
@RestController
@RequestMapping("/talentbase")
public class TalentIndexController
{
    /** 系统基础配置 */
    @Autowired
    private RuoYiConfig ruoyiConfig;

    /**
     * 访问首页，提示语
     */
    @ApiOperation(value = "访问首页，提示语")
    @RequestMapping(value = "/versionInfo", produces = "application/json;charset=utf-8")
    public String index()
    {
        return StringUtils.format("欢迎使用{}微服务，当前版本：v{}，master", ruoyiConfig.getName(), ruoyiConfig.getVersion());
    }
}
