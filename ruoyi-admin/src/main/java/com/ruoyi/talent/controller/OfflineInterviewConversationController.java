package com.ruoyi.talent.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.talentbase.domain.OfflineInterviewAudioFile;
import com.ruoyi.talentbase.domain.OfflineInterviewConversation;
import com.ruoyi.talentbase.domain.OfflineInterviewConversationAi;
import com.ruoyi.talentbase.domain.OfflineInterviewConversationSetting;
import com.ruoyi.talentbase.domain.vo.OfflineInterviewConversationVO;
import com.ruoyi.talentbase.service.IOfflineInterviewAudioFileService;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationAiService;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationService;
import com.ruoyi.talentbase.service.IOfflineInterviewConversationSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 线下面试会话Controller
 */
@RestController
@RequestMapping("/talentbase/conversation")
public class OfflineInterviewConversationController extends BaseController {
    @Autowired
    private IOfflineInterviewConversationService conversationService;

    @Autowired
    private IOfflineInterviewConversationAiService conversationAiService;

    @Autowired
    private IOfflineInterviewConversationSettingService conversationSettingService;

    @Autowired
    private IOfflineInterviewAudioFileService audioFileService;

    /**
     * 分页查询线下面试会话记录
     */
    @GetMapping("/list")
    public TableDataInfo list(OfflineInterviewConversation conversation) {
        startPage();
        List<OfflineInterviewConversationVO> list = conversationService.selectOfflineInterviewConversationVOList(conversation);
        return getDataTable(list);
    }

    /**
     * 批量新增线下面试会话
     */
    @PostMapping("/batch")
    public AjaxResult batchInsert(@RequestBody List<OfflineInterviewConversation> conversationList) {
        return toAjax(conversationService.batchInsert(conversationList));
    }

    /**
     * 批量新增线下面试会话AI分析记录
     */
    @PostMapping("/ai/batch")
    public AjaxResult batchInsertAi(@RequestBody List<OfflineInterviewConversationAi> aiList) {
        return toAjax(conversationAiService.batchInsert(aiList));
    }

    /**
     * 批量新增线下面试会话配置
     */
    @PostMapping("/setting/batch")
    public AjaxResult batchInsertSetting(@RequestBody List<OfflineInterviewConversationSetting> settingList) {
        return toAjax(conversationSettingService.batchInsert(settingList));
    }

    /**
     * 批量更新线下面试会话配置
     */
    @PostMapping("/setting/batch/update")
    public AjaxResult batchUpdateSetting(@RequestBody List<OfflineInterviewConversationSetting> settingList) {
        return toAjax(conversationSettingService.batchUpdate(settingList));
    }

    /**
     * 新增线下面试会话录音文件
     */
    @PostMapping("/audio")
    public AjaxResult insertAudioFile(@RequestBody OfflineInterviewAudioFile audioFile) {
        String url = audioFile.getAudioFile();
        if (url != null && !url.isEmpty()) {
            url = url.replaceFirst("^http://", "https://");
            url = url.replaceFirst("^(https://[^/]+?):\\d+(/|$)", "$1$2");
            audioFile.setAudioFile(url);
        }
        return toAjax(audioFileService.insert(audioFile));
    }
} 