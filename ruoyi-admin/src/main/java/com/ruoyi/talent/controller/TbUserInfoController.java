package com.ruoyi.talent.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.entity.utils.DictUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoDto;
import com.ruoyi.talentbase.domain.dto.TbUserInfoDto;
import com.ruoyi.talentbase.domain.enums.*;
import com.ruoyi.talentbase.domain.vo.TbUserInfoVo;
import com.ruoyi.talentbase.service.*;
import com.ruoyi.talentbase.utils.ChangeDetectionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.talentbase.domain.enums.EmploymentEnum.RESIGN;

/**
 * <AUTHOR>
 */
@Api(tags = "用户档案信息")
@RestController
@RequestMapping("/talentbase/userInfo")
@Slf4j
public class TbUserInfoController extends BaseController {

    @Autowired
    private ITbUserInfoService tbUserInfoService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ITbContractInfoService tbContractInfoService;

    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;

    @Autowired
    private ITbPersonalFileService tbPersonalFileService;

    @Autowired
    private ITbUserUndergoService tbUserUndergoService;

    @Autowired
    private ITbOfflineInterviewService tbOfflineInterviewService;

    @Autowired
    private ITbOnlineInterviewService iTbOnlineInterviewService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private ITbTaskPersonalInfoService tbTaskPersonalInfoService;

    @Autowired
    private ITbAnalysePersonalInfoService analysePersonalInfoService;

    @Autowired
    private IPersonnelChangeService personnelChangeService;

    // 手动录入
    private static final String MANUAL_ENTRY = "手动录入";


    /**
     * 获取档案信息列表
     *
     * @return 200 成功
     * 500 失败
     */
    @GetMapping("/list")
    public TableDataInfo list(TbUserInfoDto userInfo) {
        startPage();
        userInfo.setFiledFlag(FiledFlagEnum.FILED_FLAG.getCode());
        List<TbUserInfoVo> list = tbUserInfoService.selectTbUserInfoVoList(userInfo);
        for (TbUserInfoVo tbUserInfoVo : list) {
            if (tbUserInfoVo.getDeptId() != null) {
                SysDept sysDept = sysDeptService.selectDeptById(tbUserInfoVo.getDeptId());
                if (sysDept != null) {
                    tbUserInfoVo.setDeptName(sysDept.getDeptName());
                }
            }
        }
        return getDataTable(list);
    }

    /**
     * 获取档案信息
     */
    @ApiOperation(value = "获取档案信息")
    @GetMapping(value = "/{basePersonalId}")
    public AjaxResult getInfo(@PathVariable("basePersonalId") Long basePersonalId) {
        TbUserInfo tbUserInfo = tbUserInfoService.selectByBasePersonalId(basePersonalId);
        if (tbUserInfo != null) {
            TbPersonalInfo tbPersonalInfo = tbPersonalInfoService.selectTbPersonalInfoById(basePersonalId);
            if (tbPersonalInfo != null) {
                tbUserInfo.setEmploymentStatus(tbPersonalInfo.getEmploymentStatus());
                tbUserInfo.setEmploymentStatusName(tbPersonalInfo.getEmploymentStatusName());
            }
            TbContractInfo contractInfo = new TbContractInfo();
            contractInfo.setUserId(tbUserInfo.getId());
            // 查询合同信息
            List<TbContractInfo> infoList = tbContractInfoService.selectList(contractInfo);
            tbUserInfo.setTbContractInfoList(infoList);
            // 查询文件信息
            TbPersonalFile personalFile = new TbPersonalFile();
            personalFile.setPersonalId(basePersonalId);
            personalFile.setUndergoId(tbUserInfo.getUndergoId());
            personalFile.setFileSource(FileSourceEnum.ENTRY_INFO.getCode());
            List<TbPersonalFile> personalFiles = tbPersonalFileService.selectTbPersonalFileList(personalFile);
            tbUserInfo.setPersonalFileList(personalFiles);
            // 查询部门名称
            if (tbUserInfo.getDeptId() != null) {
                SysDept sysDept = sysDeptService.selectDeptById(tbUserInfo.getDeptId());
                if (sysDept != null) {
                    tbUserInfo.setDeptName(sysDept.getDeptName());
                }
            }
        }
        return AjaxResult.success(tbUserInfo);
    }

    // 档案直接新增入职
    @ApiOperation(value = "新增入职")
    @PostMapping("/entry")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult entry(@RequestBody TbPersonalInfoDto infoDto) {
        infoDto.setRecruitmentChannel(MANUAL_ENTRY);
        int row = tbPersonalInfoService.insertTbPersonalInfoDto(infoDto);
        if (row > 0) {
            // 插入入职信息
            TbUserInfo userInfo = infoDto.getUserInfo();
            userInfo.setBasePersonalId(infoDto.getId());
            add(userInfo);
            // 更新合同待办信息
            tbUserInfoService.countExpiredContracts();
            return success();
        }
        return error();
    }


    /**
     * 新增档案信息
     */
    @ApiOperation(value = "入职")
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody TbUserInfo userInfo) {
        Long personalId = userInfo.getBasePersonalId();
        if (personalId == null) {
            return AjaxResult.error("请设置个人信息");
        }
        // 拼接组级部门信息
        SysDept sysDept = sysDeptService.selectDeptById(userInfo.getDeptId());
        if (sysDept != null) {
            userInfo.setDepts(sysDept.getAncestors() + "," + userInfo.getDeptId());
        }

        // 查询历史档案信息
        TbUserInfo tbUserInfo = tbUserInfoService.selectByBasePersonalId(personalId);
        boolean isReEntry = tbUserInfo != null;

        // 如果是重新入职，记录变更信息
        if (isReEntry) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");

            // 获取历史工作状态
            TbPersonalInfo oldPersonalInfo = tbPersonalInfoService.selectTbPersonalInfoById(personalId);
            String oldEmploymentStatus = oldPersonalInfo != null ? oldPersonalInfo.getEmploymentStatus() : null;
            // 转换为汉字描述
            String oldEmploymentStatusDesc = convertEmploymentStatusToDesc(oldEmploymentStatus);

            // 获取历史合同信息
            TbContractInfo oldContractQuery = new TbContractInfo();
            oldContractQuery.setUserId(tbUserInfo.getId());
            List<TbContractInfo> oldContracts = tbContractInfoService.selectList(oldContractQuery);
            String oldContractInfo = null;
            if (oldContracts != null && !oldContracts.isEmpty()) {
                oldContractInfo = oldContracts.stream()
                        .map(c -> String.format("%s（%s-%s）",
                                c.getContractTypeName() != null ? c.getContractTypeName() : "未知类型",
                                c.getStartTime() != null ? sdf.format(c.getStartTime()) : "",
                                c.getEndTime() != null ? sdf.format(c.getEndTime()) : ""))
                        .collect(Collectors.joining("; "));
            }

            // 获取历史文件信息
            TbPersonalFile oldFileQuery = new TbPersonalFile();
            oldFileQuery.setPersonalId(personalId);
            oldFileQuery.setUndergoId(tbUserInfo.getUndergoId());
            oldFileQuery.setFileSource(FileSourceEnum.ENTRY_INFO.getCode());
            List<TbPersonalFile> oldFiles = tbPersonalFileService.selectTbPersonalFileList(oldFileQuery);
            String oldFileInfo = null;
            if (oldFiles != null && !oldFiles.isEmpty()) {
                oldFileInfo = oldFiles.stream()
                        .map(f -> f.getFileName() != null ? f.getFileName() : "未知文件")
                        .collect(Collectors.joining("; "));
            }

            // 转换新工作状态为汉字描述
            String newEmploymentStatusDesc = convertEmploymentStatusToDesc(userInfo.getEmploymentStatus());

            // 使用变更检测工具类检测变更（包括工作状态、合同、文件）
            String oldDeptName = getDeptNameById(tbUserInfo.getDeptId());
            String newDeptName = getDeptNameById(userInfo.getDeptId());
            List<ChangeRecord> changes = ChangeDetectionUtils.detectChanges(
                    oldDeptName, newDeptName,
                    tbUserInfo.getPosition(), userInfo.getPosition(),
                    tbUserInfo.getPositionLevel(), userInfo.getPositionLevel(),
                    oldEmploymentStatusDesc, newEmploymentStatusDesc,
                    null, null, // 合同参数传null
                    null, null,
                    tbUserInfo.getEntryTime(), userInfo.getEntryTime()// 文件参数传null
            );
            // 合同明细变更
            List<TbContractInfo> newContracts = userInfo.getTbContractInfoList() != null ? userInfo.getTbContractInfoList() : new ArrayList<>();
            changes.addAll(getContractChangeRecords(oldContracts, newContracts));
            // 文件明细变更
            List<TbPersonalFile> newFiles = userInfo.getPersonalFileList() != null ? userInfo.getPersonalFileList() : new ArrayList<>();
            changes.addAll(getFileChangeRecords(oldFiles, newFiles));
            if (!changes.isEmpty()) {
                String operator = SecurityUtils.getUsername();
                String batchId = String.valueOf(System.currentTimeMillis());
                for (ChangeRecord change : changes) {
                    change.setBatchId(batchId);
                }
                personnelChangeService.recordChanges(personalId, changes, operator);
            }
            tbUserInfoService.deleteTbUserInfoByPersonalIds(new Long[]{personalId});
        }

        // 定义事件
        Long uId = addUserUndergo(userInfo, FileSourceEnum.ENTRY_INFO,
                isReEntry ? "重新入职" : "入职");
        addFile(userInfo, uId, FileSourceEnum.ENTRY_INFO);
        // 绑定经历事件ID信息
        userInfo.setUndergoId(uId);

        try {
            tbUserInfoService.insertTbUserInfo(userInfo);
            List<TbContractInfo> infoList = userInfo.getTbContractInfoList();
            if (infoList != null && !infoList.isEmpty()) {
                // 先删除该用户的旧合同信息，避免重复
                tbContractInfoService.deleteByUserInfoId(userInfo.getId());
                infoList.forEach(info -> {
                    info.setId(null); // 确保ID为空，让数据库自动生成
                    info.setCreateTime(userInfo.getCreateTime());
                    info.setUserId(userInfo.getId());
                });
                tbContractInfoService.saveBatch(infoList);
            }
            // 更新人员状态
            TbPersonalInfo personalInfo = new TbPersonalInfo();
            personalInfo.setId(personalId);
            // 建档状态
            personalInfo.setFiledFlag(1);
            personalInfo.setEmploymentStatus(userInfo.getEmploymentStatus());
            personalInfo.setCreateArchivesTime(DateUtils.getNowDate());
            tbPersonalInfoService.updateFlagById(personalInfo);
            // 结束面试
            TbPersonalInfo tbPersonalInfo = tbPersonalInfoService.selectTbPersonalInfoById(personalId);
            if (tbPersonalInfo.getTaskPersonalId() != null) {
                // 根据来源类型处理不同的逻辑
                if (tbPersonalInfo.getPersonalIdSource() != null && tbPersonalInfo.getPersonalIdSource().equals(PersonalIdSourceEnum.TASK_POOL.getCode())) {
                    // 寻才库来源
                    TbTaskPersonalInfo updateInfo = new TbTaskPersonalInfo();
                    updateInfo.setId(tbPersonalInfo.getTaskPersonalId());
                    updateInfo.setFiledFlag(FiledFlagEnum.FILED_FLAG.getCode());
                    tbTaskPersonalInfoService.updateTbTaskPersonalInfo(updateInfo);
                    // 结束面试
                    tbOfflineInterviewService.expireOfflineInterviewsBySource(TalentSourceEnum.TASK_POOL.getCode(), tbPersonalInfo.getTaskPersonalId());
                    iTbOnlineInterviewService.expireOnlineInterviewsBySource(TalentSourceEnum.TASK_POOL.getCode(), tbPersonalInfo.getTaskPersonalId());
                } else if (tbPersonalInfo.getPersonalIdSource() != null && tbPersonalInfo.getPersonalIdSource().equals(PersonalIdSourceEnum.RESUME_POOL.getCode())) {
                    // 简历库来源
                    TbAnalysePersonalInfo updateInfo = new TbAnalysePersonalInfo();
                    updateInfo.setId(tbPersonalInfo.getTaskPersonalId());
                    updateInfo.setFiledFlag(FiledFlagEnum.FILED_FLAG.getCode());
                    analysePersonalInfoService.updateTbAnalysePersonalInfo(updateInfo);
                    // 结束面试
                    tbOfflineInterviewService.expireOfflineInterviewsBySource(TalentSourceEnum.RESUME_POOL.getCode(), tbPersonalInfo.getTaskPersonalId());
                    iTbOnlineInterviewService.expireOnlineInterviewsBySource(TalentSourceEnum.RESUME_POOL.getCode(), tbPersonalInfo.getTaskPersonalId());
                }
            }
            tbOfflineInterviewService.expireOfflineInterviewsBySource(TalentSourceEnum.TALENT_POOL.getCode(), tbPersonalInfo.getId());
            iTbOnlineInterviewService.expireOnlineInterviewsBySource(TalentSourceEnum.TALENT_POOL.getCode(), tbPersonalInfo.getId());
            // 更新合同待办信息
            tbUserInfoService.countExpiredContracts();
        } catch (Exception e) {
            log.error("用户入职失败，personalId: {}, error: ", personalId, e);
            return AjaxResult.error("入职失败: " + e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 修改档案信息
     */
    @ApiOperation(value = "修改档案信息")
    @PutMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult edit(@RequestBody TbUserInfo userInfo) {
        Long personalId = userInfo.getBasePersonalId();
        if (personalId == null) {
            return AjaxResult.error("请设置个人信息");
        }
        TbUserInfo tbUserInfo = tbUserInfoService.selectTbUserInfoById(userInfo.getId());
        if (tbUserInfo == null) {
            return AjaxResult.error("查询失败,未能获取到相关信息");
        }

        // 获取当前工作状态
        TbPersonalInfo currentPersonalInfo = tbPersonalInfoService.selectTbPersonalInfoById(personalId);
        String currentEmploymentStatus = currentPersonalInfo != null ? currentPersonalInfo.getEmploymentStatus() : null;
        // 转换为汉字描述
        String currentEmploymentStatusDesc = convertEmploymentStatusToDesc(currentEmploymentStatus);

        // 获取当前合同信息
        TbContractInfo currentContractQuery = new TbContractInfo();
        currentContractQuery.setUserId(tbUserInfo.getId());
        List<TbContractInfo> currentContracts = tbContractInfoService.selectList(currentContractQuery);
        String currentContractInfo = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        if (currentContracts != null && !currentContracts.isEmpty()) {
            currentContractInfo = currentContracts.stream()
                    .map(c -> String.format("%s（%s-%s）",
                            c.getContractTypeName() != null ? c.getContractTypeName() : "未知类型",
                            c.getStartTime() != null ? sdf.format(c.getStartTime()) : "",
                            c.getEndTime() != null ? sdf.format(c.getEndTime()) : ""))
                    .collect(Collectors.joining("; "));
        }

        // 获取当前文件信息
        TbPersonalFile currentFileQuery = new TbPersonalFile();
        currentFileQuery.setPersonalId(personalId);
        currentFileQuery.setUndergoId(tbUserInfo.getUndergoId());
        currentFileQuery.setFileSource(FileSourceEnum.ENTRY_INFO.getCode());
        List<TbPersonalFile> currentFiles = tbPersonalFileService.selectTbPersonalFileList(currentFileQuery);
        String currentFileInfo = null;
        if (currentFiles != null && !currentFiles.isEmpty()) {
            currentFileInfo = currentFiles.stream()
                    .map(f -> f.getFileName() != null ? f.getFileName() : "未知文件")
                    .collect(Collectors.joining("; "));
        }

        // 转换新工作状态为汉字描述
        String newEmploymentStatusDesc = convertEmploymentStatusToDesc(userInfo.getEmploymentStatus());

        // 使用变更检测工具类检测变更（包括工作状态、合同、文件）
        String oldDeptName = getDeptNameById(tbUserInfo.getDeptId());
        String newDeptName = getDeptNameById(userInfo.getDeptId());
        List<ChangeRecord> changes = ChangeDetectionUtils.detectChanges(
                oldDeptName, newDeptName,
                tbUserInfo.getPosition(), userInfo.getPosition(),
                tbUserInfo.getPositionLevel(), userInfo.getPositionLevel(),
                currentEmploymentStatusDesc, newEmploymentStatusDesc,
                null, null, // 合同参数传null
                null, null,  // 文件参数传null
                tbUserInfo.getEntryTime(), userInfo.getEntryTime()
        );
        // 合同明细变更
        List<TbContractInfo> newContracts = userInfo.getTbContractInfoList() != null ? userInfo.getTbContractInfoList() : new ArrayList<>();
        changes.addAll(getContractChangeRecords(currentContracts, newContracts));

        // 文件明细变更
        List<TbPersonalFile> oldFiles = tbPersonalFileService.selectTbPersonalFileList(currentFileQuery);
        List<TbPersonalFile> newFiles = userInfo.getPersonalFileList() != null ? userInfo.getPersonalFileList() : new ArrayList<>();
        changes.addAll(getFileChangeRecords(oldFiles, newFiles));

        // 记录变更
        if (!changes.isEmpty()) {
            String operator = SecurityUtils.getUsername();
            String batchId = String.valueOf(System.currentTimeMillis());
            for (ChangeRecord change : changes) {
                change.setBatchId(batchId);
            }
            personnelChangeService.recordChanges(personalId, changes, operator);
        }

        // 拼接组级部门信息
        SysDept sysDept = sysDeptService.selectDeptById(userInfo.getDeptId());
        if (sysDept != null) {
            userInfo.setDepts(sysDept.getAncestors() + "," + userInfo.getDeptId());
        }
        int row = tbUserInfoService.updateTbUserInfo(userInfo);
        if (row > 0) {
            // 更新文件信息
            tbPersonalFileService.deleteAndInsert(userInfo.getPersonalFileList(), personalId, userInfo.getUndergoId(), FileSourceEnum.ENTRY_INFO);
            // 合同信息
            tbContractInfoService.deleteByUserInfoId(userInfo.getId());
            List<TbContractInfo> infoList = userInfo.getTbContractInfoList();
            if (infoList != null && !infoList.isEmpty()) {
                infoList.forEach(info -> {
                    info.setId(null); // 确保ID为空，让数据库自动生成
                    info.setCreateTime(userInfo.getCreateTime());
                    info.setUserId(userInfo.getId());
                });
                tbContractInfoService.saveBatch(infoList);
            }
            // 履历备注信息更新
            if (StringUtils.isNotBlank(userInfo.getRemark())) {
                TbUserUndergo userUndergo = new TbUserUndergo();
                userUndergo.setId(userInfo.getUndergoId());
                userUndergo.setRemark(userInfo.getRemark());
                tbUserUndergoService.updateById(userUndergo);
            }
            // 更新人才信息的更新时间
            TbPersonalInfo personalInfo = new TbPersonalInfo();
            personalInfo.setId(personalId);
            personalInfo.setUpdateTime(DateUtils.getNowDate());
            personalInfo.setEmploymentStatus(userInfo.getEmploymentStatus());
            tbPersonalInfoService.updateFlagById(personalInfo);
            // 更新待办信息
            tbUserInfoService.countExpiredContracts();
        }
        return toAjax(row);
    }

    /**
     * 离职
     */
    @ApiOperation(value = "离职")
    @PostMapping("/leave")
    public AjaxResult leave(@RequestBody TbUserInfo userInfo) {
        if (userInfo.getBasePersonalId() == null) {
            return AjaxResult.error("请选择要离职的人员");
        }
        // 先写入经历信息
        Long uId = addUserUndergo(userInfo, FileSourceEnum.LEAVE_INFO, "离职");
        addFile(userInfo, uId, FileSourceEnum.LEAVE_INFO);
        userInfo.setUndergoId(uId);
        int row = tbUserInfoService.updateTbUserLeaveInfo(userInfo);
        if (row > 0) {
            Long personalId = userInfo.getBasePersonalId();
            TbPersonalInfo personalInfo = new TbPersonalInfo();
            personalInfo.setId(personalId);
            personalInfo.setEmploymentStatus(RESIGN.getCode());
            tbPersonalInfoService.updateFlagById(personalInfo);
            // 离职待办删除数据
            tbUserInfoService.countExpiredContracts(1);
        }
        return toAjax(row);
    }

    @GetMapping("/getTab/{basePersonalId}")
    public AjaxResult getTab(@PathVariable("basePersonalId") Long basePersonalId) {
        TbUserInfo tbUserInfo = tbUserInfoService.selectByBasePersonalId(basePersonalId);
        if (tbUserInfo != null) {
            // 查询部门名称
            if (tbUserInfo.getDeptId() != null) {
                SysDept sysDept = sysDeptService.selectDeptById(tbUserInfo.getDeptId());
                if (sysDept != null) {
                    tbUserInfo.setDeptName(sysDept.getDeptName());
                }
            }
            if (tbUserInfo.getEntryTime() != null) {
                // 计算入职年限
                Integer day = DateUtils.daysBetween(tbUserInfo.getEntryTime(), DateUtils.getNowDate());
                // 计算入职年限是否能被整除
                if (day != null) {
                    int result = (int) (day / 365);
                    tbUserInfo.setEntryYear(result);
                }
            }
            // 前端判断是否有data
            return AjaxResult.success(tbUserInfo);
        }
        return AjaxResult.success();
    }

    /**
     * 删除人员档案信息
     */
    @DeleteMapping("/{ids}")
    @Transactional
    public AjaxResult remove(@PathVariable Long[] ids) {
        // 更新标识的ids
        List<Long> idList = new ArrayList<>();
        for (Long id : ids) {
            TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(id);
            if (personalInfo == null) {
                throw new RuntimeException("查询失败,未能获取到相关信息");
            }

            if (personalInfo.getTaskPersonalId() != null) {
                // 根据来源类型处理不同的逻辑
                if (personalInfo.getPersonalIdSource() != null && personalInfo.getPersonalIdSource().equals(PersonalIdSourceEnum.TASK_POOL.getCode())) {
                    // 寻才库来源
                    TbTaskPersonalInfo taskPersonalInfo = new TbTaskPersonalInfo();
                    taskPersonalInfo.setId(personalInfo.getTaskPersonalId());
                    taskPersonalInfo.setFiledFlag(FiledFlagEnum.NOT_FILED_FLAG.getCode());
                    tbTaskPersonalInfoService.updateById(taskPersonalInfo);
                } else if (personalInfo.getPersonalIdSource() != null && personalInfo.getPersonalIdSource().equals(PersonalIdSourceEnum.RESUME_POOL.getCode())) {
                    // 简历库来源
                    TbAnalysePersonalInfo analysePersonalInfo = new TbAnalysePersonalInfo();
                    analysePersonalInfo.setId(personalInfo.getTaskPersonalId());
                    analysePersonalInfo.setFiledFlag(FiledFlagEnum.NOT_FILED_FLAG.getCode());
                    analysePersonalInfoService.updateTbAnalysePersonalInfo(analysePersonalInfo);
                }
            }
            // 如果未展示,直接删除所有信息
            if (personalInfo.getStorageDisplay().equals(StorageDisplayEnum.NOT_STORAGE_DISPLAY.getCode())) {
                // 删除人才库相关数据
                tbPersonalInfoService.removeAll(personalInfo.getId());
                // 删除档案相关数据
                tbUserInfoService.deleteTbUserInfoByPersonalIds(new Long[]{personalInfo.getId()});
                // 删除履历相关数据
                tbUserUndergoService.deleteByPInfoId(personalInfo.getId());
                // 删除线下面试相关数据
                tbOfflineInterviewService.deleteByPInfoId(personalInfo.getId(), TalentSourceEnum.TALENT_POOL);
                // 删除线上面试相关数据
                iTbOnlineInterviewService.deleteByPInfoId(personalInfo.getId(), TalentSourceEnum.TALENT_POOL);
                continue;
            }
            idList.add(id);
        }
        TbPersonalInfo personalInfo = new TbPersonalInfo();
        personalInfo.setFiledFlag(0);
        if (!idList.isEmpty()) {
            return toAjax(tbPersonalInfoService.updateFlagByIds(personalInfo, idList.toArray(new Long[0])));
        }
        return AjaxResult.success();
    }

    /**
     * 文件新增
     */
    private void addFile(TbUserInfo userInfo, Long uId, FileSourceEnum fileSourceEnum) {
        List<TbPersonalFile> personalFileList = userInfo.getPersonalFileList();
        if (personalFileList != null && !personalFileList.isEmpty()) {
            tbPersonalFileService.insertBatch(personalFileList, userInfo.getBasePersonalId(), uId, fileSourceEnum);
        }
    }


    // 无原因经历插入
    private Long addUserUndergo(TbUserInfo userInfo, FileSourceEnum fileSourceEnum, String affairsName) {
        return addUserUndergo(userInfo, fileSourceEnum, affairsName, null);
    }

    /**
     * 写入经历信息
     */
    private Long addUserUndergo(TbUserInfo userInfo, FileSourceEnum fileSourceEnum, String affairsName, String content) {
        // 写入经历信息
        TbUserUndergo userUndergo = new TbUserUndergo();
        userUndergo.setPersonalId(userInfo.getBasePersonalId());
        userUndergo.setAffairsName(affairsName);
        userUndergo.setFileCode(fileSourceEnum.getCode());
        if (fileSourceEnum == FileSourceEnum.LEAVE_INFO) {
            userUndergo.setRemark(userInfo.getLeaveReason());
            userUndergo.setOperateTime(userInfo.getFormalLeaveTime());
        } else if (fileSourceEnum == FileSourceEnum.ENTRY_INFO) {
            userUndergo.setRemark(userInfo.getRemark());
            userUndergo.setOperateTime(userInfo.getEntryTime());
        } else {
            userUndergo.setRemark(content);
            userUndergo.setOperateTime(DateUtils.resetting(DateUtils.getNowDate()));
        }
        userUndergo.setCreateTime(DateUtils.getNowDate());
        userUndergo.setCreateBy(userInfo.getCreateBy());
        tbUserUndergoService.insertTbUserUndergo(userUndergo);
        return userUndergo.getId();
    }

    /**
     * 根据知识库ID获取difyDatasetId
     *
     * @param deptId 知识库ID
     * @return difyDatasetId
     */
    @InnerAuth
    @GetMapping("/getUserCount/{deptId}")
    public Integer getUserCount(@PathVariable("deptId") Long deptId) {
        return tbUserInfoService.getUserCount(deptId);
    }

    /**
     * 转换工作状态代码为汉字描述
     *
     * @param statusCode 工作状态代码
     * @return 汉字描述，如果代码无效则返回原代码
     */
    private String convertEmploymentStatusToDesc(String statusCode) {
        if (StringUtils.isEmpty(statusCode)) {
            return "无";
        }
        EmploymentEnum status = EmploymentEnum.getByCode(statusCode);
        return status != null ? status.getDesc() : statusCode;
    }

    private String contractToString(TbContractInfo c) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        // 通过字典获取合同类型汉字描述
        String contractTypeDesc = DictUtils.getDictLabel("contract_type", c.getContractType() != null ? c.getContractType().toString() : null);
        return (contractTypeDesc != null ? contractTypeDesc : "未知类型") +
                "（" +
                (c.getStartTime() != null ? sdf.format(c.getStartTime()) : "") +
                "-" +
                (c.getEndTime() != null ? sdf.format(c.getEndTime()) : "") +
                "）" +
                (StringUtils.isNotEmpty(c.getRemark()) ? " 备注:" + c.getRemark() : "");
    }

    private boolean contractEquals(TbContractInfo a, TbContractInfo b) {
        if (a == b) return true;
        if (a == null || b == null) return false;
        return Objects.equals(a.getContractType(), b.getContractType()) &&
                Objects.equals(a.getStartTime(), b.getStartTime()) &&
                Objects.equals(a.getEndTime(), b.getEndTime()) &&
                Objects.equals(a.getRemark(), b.getRemark());
    }

    private List<ChangeRecord> getContractChangeRecords(List<TbContractInfo> oldContracts, List<TbContractInfo> newContracts) {
        Map<Long, TbContractInfo> oldMap = oldContracts.stream()
                .filter(c -> c.getId() != null)
                .collect(Collectors.toMap(TbContractInfo :: getId, c -> c, (a, b) -> a));

        List<ChangeRecord> contractChanges = new ArrayList<>();
        for (TbContractInfo nc : newContracts) {
            if (nc.getId() == null) {
                // 新增（id为null的合同）
                contractChanges.add(new ChangeRecord(ChangeTypeEnum.CONTRACT, "无", contractToString(nc), "add"));
            } else if (!oldMap.containsKey(nc.getId())) {
                // 新增
                contractChanges.add(new ChangeRecord(ChangeTypeEnum.CONTRACT, "无", contractToString(nc), "add"));
            } else if (!contractEquals(oldMap.get(nc.getId()), nc)) {
                // 编辑
                contractChanges.add(new ChangeRecord(ChangeTypeEnum.CONTRACT, contractToString(oldMap.get(nc.getId())), contractToString(nc), "edit"));
            }
        }
        for (Long id : oldMap.keySet()) {
            boolean exists = newContracts.stream().anyMatch(nc -> id.equals(nc.getId()));
            if (!exists) {
                contractChanges.add(new ChangeRecord(ChangeTypeEnum.CONTRACT, contractToString(oldMap.get(id)), "无", "delete"));
            }
        }
        return contractChanges;
    }

    private List<ChangeRecord> getFileChangeRecords(List<TbPersonalFile> oldFiles, List<TbPersonalFile> newFiles) {
        Set<String> oldUrls = oldFiles.stream()
                .map(TbPersonalFile :: getFileUrl)
                .filter(StringUtils :: isNotEmpty)
                .collect(Collectors.toSet());
        Set<String> newUrls = newFiles.stream()
                .map(TbPersonalFile :: getFileUrl)
                .filter(StringUtils :: isNotEmpty)
                .collect(Collectors.toSet());

        List<ChangeRecord> fileChanges = new ArrayList<>();
        // 新增
        for (TbPersonalFile nf : newFiles) {
            String url = nf.getFileUrl();
            if (StringUtils.isNotEmpty(url) && !oldUrls.contains(url)) {
                fileChanges.add(new ChangeRecord(ChangeTypeEnum.FILE, "无", fileToString(nf), "add"));
            }
        }
        // 删除
        for (TbPersonalFile of : oldFiles) {
            String url = of.getFileUrl();
            if (StringUtils.isNotEmpty(url) && !newUrls.contains(url)) {
                fileChanges.add(new ChangeRecord(ChangeTypeEnum.FILE, fileToString(of), "无", "delete"));
            }
        }
        return fileChanges;
    }

    private String fileToString(TbPersonalFile f) {
        return (f.getFileName() != null && !f.getFileName().trim().isEmpty()) ? f.getFileName() : "无";
    }

    private String getDeptNameById(Long deptId) {
        if (deptId == null) return "无";
        SysDept dept = sysDeptService.selectDeptById(deptId);
        if (dept != null && StringUtils.isNotEmpty(dept.getDeptName())) {
            return dept.getDeptName();
        }
        return deptId.toString();
    }
}
