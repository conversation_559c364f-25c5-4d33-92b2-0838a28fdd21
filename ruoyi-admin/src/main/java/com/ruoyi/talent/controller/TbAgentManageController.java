package com.ruoyi.talent.controller;

import com.ruoyi.common.core.enums.JobPostingStatus;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import com.ruoyi.common.entity.domain.talent.TbTaskPosting;
import com.ruoyi.common.entity.utils.DictUtils;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.TbTaskPersonalInfoDto;
import com.ruoyi.talentbase.domain.vo.TbTaskPostingVo;
import com.ruoyi.talentbase.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 【Agent服务接口】Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/talentbase/agentManage")
public class TbAgentManageController extends BaseController {
    @Autowired
    private ITbTaskPersonalInfoService tbPersonalInfoService;
    @Autowired
    private ITbTaskPostingService tbJobPostingService;
    @Autowired
    private ITbTaskWorkExperienceService tbWorkExperienceService;
    @Autowired
    private ITbTaskEducationInfoService tbEducationInfoService;
    @Autowired
    private ITbTaskProjectExperienceService projectExperienceService;

    /**
     * 新增【请填写功能名称】
     */
    @Transactional
    @PostMapping
    public AjaxResult add(@RequestBody TbTaskPersonalInfoDto dto) {
        Long jobId = dto.getJobId();
        // 查询任务详情
        TbTaskPosting tbJobPosting = tbJobPostingService.selectTbTaskPostingById(jobId);
        if (tbJobPosting == null) {
            return error("任务不存在");
        }
        if (Objects.equals(tbJobPosting.getStatus(), JobPostingStatus.FAILED.getCode())) {
            return error("任务已失败，不能新增");
        }
        TbTaskPersonalInfo tbPersonalInfo = new TbTaskPersonalInfo();
        BeanUtils.copyProperties(dto, tbPersonalInfo);
        tbPersonalInfo.setAvatar(null);
        if (tbPersonalInfo.getWorkStatus() == null) {
            tbPersonalInfo.setWorkStatus("离职");
        }
        Map<String, String> map = DictUtils.getDictCacheMap(SysDictDataEnum.SYS_USER_SEX.getName());
        if (map != null && !StringUtils.isEmpty(dto.getSex())) {
            tbPersonalInfo.setSex(map.get(dto.getSex()));
        } else {
            tbPersonalInfo.setSex("2");
        }
        List<String> skillList = dto.getSkillList();
        String skill = StringUtils.join(skillList, ",");
        tbPersonalInfo.setSkills(skill);
        tbPersonalInfo.setTalentPoolStatus(0);
        tbJobPosting.setFinishCount(tbJobPosting.getFinishCount() + 1);
        // 如果人数达标就更新为已完成
        if (Objects.equals(tbJobPosting.getFinishCount(), tbJobPosting.getScreeningCount())) {
            tbJobPosting.setStatus(JobPostingStatus.COMPLETED.getCode());
        }
        // 转为code存入数据库
        Map<String, String> channels = DictUtils.getDictByValueMap(SysDictDataEnum.RECRUITMENT_CHANNELS.getName());
        if (channels != null && tbJobPosting.getChannel() != null) {
            String channel = tbJobPosting.getChannel().toString();
            tbPersonalInfo.setRecruitmentChannel(channels.get(channel));
        }
        Map<String, String> education = DictUtils.getDictCacheMap(SysDictDataEnum.EDUCATION.getName());
        if (education != null && tbPersonalInfo.getEducation() != null) {
            tbPersonalInfo.setEducation(education.get(tbPersonalInfo.getEducation()));
        }
        tbJobPostingService.updateTbTaskPosting(tbJobPosting);
        int row = tbPersonalInfoService.insertTbTaskPersonalInfo(tbPersonalInfo);
        if (row > 0) {
            List<TbTaskWorkExperience> experienceList = dto.getTbWorkExperienceList();
            if (experienceList != null && !experienceList.isEmpty()) {
                for (TbTaskWorkExperience tbWorkExperience : experienceList) {
                    tbWorkExperience.setPersonalId(tbPersonalInfo.getId());
                    tbWorkExperienceService.insertTbTaskWorkExperience(tbWorkExperience);
                }
            }
            List<TbTaskEducationInfo> taskEducationInfos = dto.getTbEducationInfoList();
            if (taskEducationInfos != null && !taskEducationInfos.isEmpty()) {
                for (TbTaskEducationInfo tbEducationInfo : taskEducationInfos) {
                    tbEducationInfo.setPersonalId(tbPersonalInfo.getId());
                    tbEducationInfoService.insertTbTaskEducationInfo(tbEducationInfo);
                }
            }

            // 保存项目经历
            List<TbTaskProjectExperience> projectExperienceList = dto.getTbProjectExperienceList();
            if (projectExperienceList != null && !projectExperienceList.isEmpty()) {
                for (TbTaskProjectExperience projectExp : projectExperienceList) {
                    projectExp.setPersonalId(tbPersonalInfo.getId());
                    projectExperienceService.insertTbTaskProjectExperience(projectExp);
                }
            }
        }
        return toAjax(row);
    }

    /**
     * Agent调用
     * 获取任务信息
     */
    @GetMapping("/getPostingInfo/{id}")
    public AjaxResult getPostingInfo(@PathVariable Long id) {
        TbTaskPosting tbJobPosting = tbJobPostingService.selectTbTaskPostingById(id);
        TbTaskPostingVo tbJobPostingVo = new TbTaskPostingVo();
        BeanUtils.copyProperties(tbJobPosting, tbJobPostingVo);
        Map<String, String> map = DictUtils.getDictCacheMap(SysDictDataEnum.EDUCATION.getName());
        tbJobPostingVo.setEducationMap(map);
        return AjaxResult.success(tbJobPostingVo);
    }
}
