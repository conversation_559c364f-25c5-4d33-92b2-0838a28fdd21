package com.ruoyi.talent.controller;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.talentbase.domain.TbPersonalFile;
import com.ruoyi.talentbase.domain.TbUserUndergo;
import com.ruoyi.talentbase.domain.enums.ChangeTypeEnum;
import com.ruoyi.talentbase.domain.enums.FileSourceEnum;
import com.ruoyi.talentbase.domain.vo.TbUserUndergoBatchVo;
import com.ruoyi.talentbase.service.ITbPersonalFileService;
import com.ruoyi.talentbase.service.ITbUserUndergoService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 员工经历信息Controller
 *
 * <AUTHOR>
 */
@Api(tags = "员工经历信息")
@RestController
@RequestMapping("/talentbase/undergo")
public class TbUserUndergoController extends BaseController {
    @Autowired
    private ITbUserUndergoService tbUserUndergoService;
    @Autowired
    private ITbPersonalFileService tbPersonalFileService;

    /**
     * 查询员工经历信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbUserUndergo tbUserUndergo) {
//        startPage();
        List<TbUserUndergo> list = tbUserUndergoService.selectTbUserUndergoList(tbUserUndergo);
        
        // 处理文件信息和变更类型描述
        for (TbUserUndergo undergo : list) {
            Integer fileCode = undergo.getFileCode();
            if (fileCode == null) {
                continue;
            }
            TbPersonalFile personalFile = new TbPersonalFile();
            personalFile.setFileSource(fileCode);
            personalFile.setPersonalId(undergo.getPersonalId());
            personalFile.setUndergoId(undergo.getId());
            List<TbPersonalFile> files = tbPersonalFileService.selectTbPersonalFileList(personalFile);
            if (files != null && !files.isEmpty()) {
                undergo.setPersonalFileList(files);
            }
            
            // 设置变更类型描述
            if (StringUtils.isNotEmpty(undergo.getChangeType())) {
                ChangeTypeEnum changeType = ChangeTypeEnum.getByCode(undergo.getChangeType());
                if (changeType != null) {
                    undergo.setChangeTypeDesc(changeType.getDesc());
                }
            }
        }
        
        // 按批次ID分组
        Map<String, List<TbUserUndergo>> batchMap = list.stream()
            .collect(Collectors.groupingBy(undergo -> {
                if (StringUtils.isNotEmpty(undergo.getBatchId())) {
                    return undergo.getBatchId();
                } else {
                    // 为没有批次ID的记录临时生成一个，使用ID和时间戳组合
                    String tempBatchId = "temp_" + undergo.getId() + "_" + System.currentTimeMillis();
                    undergo.setBatchId(tempBatchId);
                    return tempBatchId;
                }
            }));
        
        // 转换为批次VO列表
        List<TbUserUndergoBatchVo> batchList = new ArrayList<>();
        for (Map.Entry<String, List<TbUserUndergo>> entry : batchMap.entrySet()) {
            String batchId = entry.getKey();
            List<TbUserUndergo> undergoList = entry.getValue();
            
            if (!undergoList.isEmpty()) {
                TbUserUndergo firstUndergo = undergoList.get(0);
                TbUserUndergoBatchVo batchVo = new TbUserUndergoBatchVo(
                    firstUndergo.getOperateTime(),
                    firstUndergo.getCreateTime(),
                    batchId,
                    undergoList
                );
                batchList.add(batchVo);
            }
        }
        
        // 按操作时间倒序排序，再按创建时间倒序排序
        batchList.sort((a, b) -> {
            // 首先比较操作时间
            if (a.getOperateTime() == null && b.getOperateTime() == null) {
                // 如果操作时间都为空，则比较创建时间
                if (a.getCreateTime() == null && b.getCreateTime() == null) {
                    return 0;
                }
                if (a.getCreateTime() == null) {
                    return 1;
                }
                if (b.getCreateTime() == null) {
                    return -1;
                }
                return b.getCreateTime().compareTo(a.getCreateTime());
            }
            if (a.getOperateTime() == null) {
                return 1;
            }
            if (b.getOperateTime() == null) {
                return -1;
            }
            
            int operateTimeCompare = b.getOperateTime().compareTo(a.getOperateTime());
            if (operateTimeCompare != 0) {
                return operateTimeCompare;
            }
            
            // 如果操作时间相同，则比较创建时间
            if (a.getCreateTime() == null && b.getCreateTime() == null) {
                return 0;
            }
            if (a.getCreateTime() == null) {
                return 1;
            }
            if (b.getCreateTime() == null) {
                return -1;
            }
            return b.getCreateTime().compareTo(a.getCreateTime());
        });
        
        return getDataTable(batchList);
    }

    /**
     * 获取员工经历信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TbUserUndergo undergo = tbUserUndergoService.selectTbUserUndergoById(id);
        if (undergo != null && undergo.getFileCode() != null) {
            TbPersonalFile personalFile = new TbPersonalFile();
            personalFile.setFileSource(undergo.getFileCode());
            personalFile.setPersonalId(undergo.getPersonalId());
            personalFile.setUndergoId(undergo.getId());
            List<TbPersonalFile> files = tbPersonalFileService.selectTbPersonalFileList(personalFile);
            if (files != null && !files.isEmpty()) {
                undergo.setPersonalFileList(files);
            }
        }
        
        // 设置变更类型描述
        if (StringUtils.isNotEmpty(undergo.getChangeType())) {
            ChangeTypeEnum changeType = ChangeTypeEnum.getByCode(undergo.getChangeType());
            if (changeType != null) {
                undergo.setChangeTypeDesc(changeType.getDesc());
            }
        }
        
        return success(undergo);
    }

    /**
     * 新增员工经历信息
     */
    @PostMapping
    public AjaxResult add(@RequestBody TbUserUndergo tbUserUndergo) {
        tbUserUndergo.setFileCode(FileSourceEnum.EXPERIENCE_INFO.getCode());
        tbUserUndergo.setCreateTime(new Date());
        int row = tbUserUndergoService.insertTbUserUndergo(tbUserUndergo);
        if (row > 0) {
            List<TbPersonalFile> fileList = tbUserUndergo.getPersonalFileList();
            tbPersonalFileService.insertBatch(fileList, tbUserUndergo.getPersonalId(), tbUserUndergo.getId(), FileSourceEnum.EXPERIENCE_INFO);
        }
        return toAjax(row);
    }

    /**
     * 修改员工经历信息
     */
    @PutMapping
    public AjaxResult edit(@RequestBody TbUserUndergo tbUserUndergo) {
        int row = tbUserUndergoService.updateTbUserUndergo(tbUserUndergo);
        if (row > 0) {
            List<TbPersonalFile> fileList = tbUserUndergo.getPersonalFileList();
            tbPersonalFileService.deleteAndInsert(fileList, tbUserUndergo.getPersonalId(), tbUserUndergo.getId(), FileSourceEnum.EXPERIENCE_INFO);
        }
        return toAjax(row);
    }

    /**
     * 删除员工经历信息
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        int row = tbUserUndergoService.deleteTbUserUndergoByIds(ids);
        if (row > 0) {
            // 删除手动添加的履历文件
            tbPersonalFileService.deleteByUndergoId(ids);
        }
        return toAjax(row);
    }
}