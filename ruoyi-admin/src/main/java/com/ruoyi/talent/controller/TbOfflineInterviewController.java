package com.ruoyi.talent.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.OfflineInterviewQueryDTO;
import com.ruoyi.common.entity.domain.talent.InterviewStatusEnum;
import com.ruoyi.talentbase.domain.enums.TalentSourceEnum;
import com.ruoyi.talentbase.domain.vo.OfflineInterviewPersonDetailVO;
import com.ruoyi.talentbase.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/talentbase/offlineInterview")
public class TbOfflineInterviewController extends BaseController {

    @Autowired
    private ITbOfflineInterviewService service;

    @Autowired
    private ITbTaskPersonalInfoService taskPersonalInfoService;

    @Autowired
    private ITbPersonalInfoService personalInfoService;

    @Autowired
    private ITbOfflineInterviewEvaluationService evaluationService;

    @Autowired
    private IOfflineInterviewConversationService conversationService;

    @Autowired
    private IOfflineInterviewConversationSettingService conversationSettingService;

    @Autowired
    private ITbAnalysePersonalInfoService analysePersonalInfoService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private IOfflineInterviewAudioFileService audioFileService;

    @Autowired
    private IOfflineInterviewConversationAiService conversationAiService;

    @Autowired
    private AsyncInterviewEvaluationService asyncInterviewEvaluationService;

    @GetMapping("/list")
    public TableDataInfo list(OfflineInterviewQueryDTO queryDTO) {
        startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<TbOfflineInterview> list = service.selectTbOfflineInterviewList(queryDTO);
        
        // 设置部门名称
        if (list != null && !list.isEmpty()) {
            for (TbOfflineInterview interview : list) {
                // 设置部门名称
                if (interview.getDeptId() != null) {
                    SysDept dept = sysDeptService.selectDeptById(interview.getDeptId());
                    if (dept != null) {
                        interview.setDeptName(dept.getDeptName());
                    }
                }
                
                // 设置AI评分
                if (interview.getId() != null) {
                    TbOfflineInterviewEvaluation aiEvaluation = evaluationService.selectAiEvaluationByOfflineInterviewId(interview.getId());
                    if (aiEvaluation != null && aiEvaluation.getScore() != null) {
                        interview.setAiScore(aiEvaluation.getScore());
                    }
                }
            }
        }
        
        return getDataTable(list);
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TbOfflineInterview interview = service.selectTbOfflineInterviewById(id);
        if (interview != null) {
            // 设置部门名称
            if (interview.getDeptId() != null) {
                SysDept dept = sysDeptService.selectDeptById(interview.getDeptId());
                if (dept != null) {
                    interview.setDeptName(dept.getDeptName());
                }
            }
            
            // 设置AI评分
            TbOfflineInterviewEvaluation aiEvaluation = evaluationService.selectAiEvaluationByOfflineInterviewId(interview.getId());
            if (aiEvaluation != null && aiEvaluation.getScore() != null) {
                interview.setAiScore(aiEvaluation.getScore());
            }
        }
        return AjaxResult.success(interview);
    }

    @PostMapping
    public AjaxResult add(@RequestBody TbOfflineInterview interview) {
        if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.TALENT_POOL.getCode())) {
            // 来源于人才表
            TbPersonalInfo info = personalInfoService.selectTbPersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的人才信息");
            }
            interview.setName(info.getUserName());
            interview.setGender(info.getSex());
            interview.setAge(info.getAge());
            interview.setEducation(info.getEducation());
            interview.setAvatarUrl(info.getAvatar());
            interview.setTalentSource(info.getRecruitmentChannel());
        } else if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.TASK_POOL.getCode())) {
            // 来源于寻才表
            TbTaskPersonalInfo info = taskPersonalInfoService.selectTbTaskPersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的寻才信息");
            }
            interview.setName(info.getUserName());
            interview.setGender(info.getSex());
            interview.setAge(info.getAge());
            interview.setEducation(info.getEducation());
            interview.setAvatarUrl(info.getAvatar());
            interview.setTalentSource(info.getRecruitmentChannel());
        } else if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.RESUME_POOL.getCode())) {
            // 来源于简历库
            TbAnalysePersonalInfo info = analysePersonalInfoService.selectTbAnalysePersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的简历库信息");
            }
            interview.setName(info.getUserName());
            interview.setGender(info.getSex());
            interview.setAge(info.getAge());
            interview.setEducation(info.getEducation());
            interview.setAvatarUrl(info.getAvatar());
            interview.setTalentSource(info.getRecruitmentChannel());
        } else {
            return AjaxResult.error("线下面试来源类型不正确");
        }
        // 拼接组级部门信息
        SysDept sysDept = sysDeptService.selectDeptById(interview.getDeptId());
        if (sysDept != null) {
            interview.setDepts(sysDept.getAncestors() + "," + interview.getDeptId());
        }

        int rows = service.insertTbOfflineInterview(interview);
        if (rows > 0) {
            return AjaxResult.success("新增成功", interview.getId());
        }
        return AjaxResult.error("新增失败");
    }

    @PutMapping
    public AjaxResult edit(@RequestBody TbOfflineInterview interview) {
        return toAjax(service.updateTbOfflineInterview(interview));
    }

    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deleteTbOfflineInterviewByIds(ids));
    }

    @PostMapping("/start/{id}")
    public AjaxResult startInterview(@PathVariable Long id, @RequestParam(defaultValue = "false") Boolean restart) {
        TbOfflineInterview interview = service.selectTbOfflineInterviewById(id);
        if (interview == null) {
            return AjaxResult.error("未找到面试记录");
        }

        // 获取当前登录用户
        String currentUserNickname = SecurityUtils.getNickname();
        String currentUserUsername = SecurityUtils.getUsername();
        
        // 校验当前用户是否正在其他面试中（排除当前面试）
        List<TbOfflineInterview> inProgressInterviews = service.selectInProgressInterviewsByOperator(currentUserNickname, currentUserUsername, id);
        if (inProgressInterviews != null && !inProgressInterviews.isEmpty()) {
            return AjaxResult.error("您正在面试中，请先完成当前面试后再开始新的面试");
        }

        // 校验面试状态
        if (InterviewStatusEnum.IN_PROGRESS.getCode() == interview.getInterviewStatus()) {
            if (restart) {
                // 如果是重新开始，清理之前的数据并重置时间
                cleanInterviewData(interview.getId());
                interview.setInterviewTime(DateUtils.getNowDate());
            }
            // 如果是继续面试（restart=false），直接允许继续，不做任何阻止
        } else if (InterviewStatusEnum.COMPLETED.getCode() == interview.getInterviewStatus()) {
            // 已完成的面试不允许重新开始或继续面试
            return AjaxResult.error("面试已结束，不能重新开始或继续面试");
        } else if (InterviewStatusEnum.EXPIRED.getCode() == interview.getInterviewStatus()) {
            // 已过期的面试不允许重新开始或继续面试
            return AjaxResult.error("面试已过期，不能重新开始或继续面试");
        } else {
            // 面试状态为未开始，设置开始时间
            interview.setInterviewTime(DateUtils.getNowDate());
        }

        interview.setInterviewStatus(InterviewStatusEnum.IN_PROGRESS.getCode());
        interview.setOperator(SecurityUtils.getNickname());
        interview.setUpdateBy(SecurityUtils.getUsername());
        interview.setUpdateTime(DateUtils.getNowDate());
        interview.setInterviewEndTime(null); // 清空结束时间
        
        int result = service.updateTbOfflineInterview(interview);
        return toAjax(result);
    }

    /**
     * 清理面试相关数据（用于重新开始面试）
     */
    private void cleanInterviewData(Long interviewId) {
        try {
            // 删除之前的评价记录
            evaluationService.deleteTbOfflineInterviewEvaluationByOfflineInterviewId(interviewId);
            
            // 删除之前的会话记录
            conversationService.deleteByOfflineInterviewId(interviewId);
            
            // 删除之前的会话配置
            conversationSettingService.deleteByOfflineInterviewId(interviewId);
            
            // 删除之前的音频文件记录
            audioFileService.deleteByOfflineInterviewId(interviewId);
            
            // 删除之前的AI会话记录
            conversationAiService.deleteByOfflineInterviewId(interviewId);
            
            log.info("清理面试数据成功，面试ID：{}", interviewId);
        } catch (Exception e) {
            log.error("清理面试数据失败，面试ID：{}", interviewId, e);
        }
    }

    @PostMapping("/finish/{id}")
    public AjaxResult finishInterview(@PathVariable Long id) {
        TbOfflineInterview interview = service.selectTbOfflineInterviewById(id);
        if (interview == null) {
            return AjaxResult.error("未找到面试记录");
        }

        // 检查面试状态，如果已经完成则直接返回成功
        if (InterviewStatusEnum.COMPLETED.getCode() == interview.getInterviewStatus()) {
            log.info("面试ID：{} 已经完成，无需重复操作", id);
            return AjaxResult.success("面试已完成");
        }

        // 检查面试状态，如果不是进行中状态，则不允许结束
        if (InterviewStatusEnum.IN_PROGRESS.getCode() != interview.getInterviewStatus()) {
            return AjaxResult.error("只有进行中的面试才能结束");
        }

        // 先更新面试状态和结束时间
        interview.setInterviewStatus(InterviewStatusEnum.COMPLETED.getCode());
        interview.setInterviewEndTime(DateUtils.getNowDate());
        interview.setUpdateTime(DateUtils.getNowDate());
        
        int result = service.updateTbOfflineInterview(interview);

        if (result > 0) {
            log.info("面试ID：{} 结束成功，开始异步生成AI评价", id);
            // 更新成功后再异步处理AI评价生成
            asyncInterviewEvaluationService.generateAIEvaluation(interview);
        } else {
            log.error("面试ID：{} 结束失败", id);
        }

        return toAjax(result);
    }

    @PostMapping("/retryAIEvaluation/{id}")
    public AjaxResult retryAIEvaluation(@PathVariable Long id) {
        TbOfflineInterview interview = service.selectTbOfflineInterviewById(id);
        if (interview == null) {
            return AjaxResult.error("未找到面试记录");
        }

        // 检查面试状态
        if (InterviewStatusEnum.COMPLETED.getCode() != interview.getInterviewStatus()) {
            return AjaxResult.error("只有已完成的面试才能重试AI评价生成");
        }

        // 异步重试生成AI评价
        asyncInterviewEvaluationService.retryGenerateAIEvaluation(id);
        
        return AjaxResult.success("AI评价重试生成已启动");
    }

    @GetMapping("/personDetail/{id}")
    public AjaxResult getPersonDetail(@PathVariable("id") Long id) {
        TbOfflineInterview interview = service.selectTbOfflineInterviewById(id);
        if (interview == null) {
            return AjaxResult.error("未找到面试记录");
        }

        // 更新红点状态为已读
        interview.setAvatarRedDot(0);
        interview.setUpdateBy(SecurityUtils.getUsername());
        interview.setUpdateTime(DateUtils.getNowDate());
        service.updateTbOfflineInterview(interview);

        OfflineInterviewPersonDetailVO vo = new OfflineInterviewPersonDetailVO();
        if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.TALENT_POOL.getCode())) {
            TbPersonalInfo info = personalInfoService.selectTbPersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的人才信息");
            }
            vo.setName(info.getUserName());
            vo.setGender(info.getSex());
            vo.setAge(info.getAge());
            vo.setEducation(info.getEducationName());
            vo.setJobIntention(interview.getJobIntention());
            vo.setTalentSource(info.getRecruitmentChannel());
            vo.setSalaryExpectation(info.getSalaryExpectation());
            vo.setCurrentAddress(info.getCurrentAddress());
            vo.setAvatarUrl(info.getAvatar());
            // vo.setResumeAttachment(info.getResumeAttachment());
        } else if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.TASK_POOL.getCode())) {
            TbTaskPersonalInfo info = taskPersonalInfoService.selectTbTaskPersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的寻才信息");
            }
            vo.setName(info.getUserName());
            vo.setGender(info.getSex());
            vo.setAge(info.getAge());
            vo.setEducation(info.getEducationName());
            vo.setJobIntention(interview.getJobIntention());
            vo.setTalentSource(info.getRecruitmentChannel());
            vo.setSalaryExpectation(info.getSalaryExpectation());
            vo.setCurrentAddress(info.getCurrentAddress());
            vo.setAvatarUrl(info.getAvatar());
            // vo.setResumeAttachment(info.getResumeAttachment());
        } else if (interview.getOfflineInterviewSource().equals(TalentSourceEnum.RESUME_POOL.getCode())) {
            // 来源于简历库
            TbAnalysePersonalInfo info = analysePersonalInfoService.selectTbAnalysePersonalInfoById(interview.getOfflineInterviewSourceId());
            if (info == null) {
                return AjaxResult.error("未找到对应的简历库信息");
            }
            vo.setName(info.getUserName());
            vo.setGender(info.getSex());
            vo.setAge(info.getAge());
            vo.setEducation(info.getEducationName());
            vo.setJobIntention(interview.getJobIntention());
            vo.setTalentSource(info.getRecruitmentChannel());
            vo.setSalaryExpectation(info.getSalaryExpectation());
            vo.setCurrentAddress(info.getCurrentAddress());
            vo.setAvatarUrl(info.getAvatar());
            // vo.setResumeAttachment(info.getResumeAttachment());
        } else {
            return AjaxResult.error("线下面试来源类型不正确");
        }
        return AjaxResult.success(vo);
    }

    /**
     * 根据来源类型和来源ID查询未开始的面试记录
     *
     * @param queryDTO 查询条件
     * @return 未开始的面试记录列表
     */
    @GetMapping("/notStartedList")
    public AjaxResult getNotStartedList(OfflineInterviewQueryDTO queryDTO) {
        // 设置查询条件：只查询未开始状态的面试
        queryDTO.setInterviewStatus(InterviewStatusEnum.NOT_STARTED.getCode());

        // 验证必要参数
        Integer interviewSource = queryDTO.getOfflineInterviewSource();
        if (interviewSource == null) {
            return AjaxResult.error("面试来源类型不能为空");
        }
        Long interviewSourceId = queryDTO.getOfflineInterviewSourceId();
        if (interviewSourceId == null) {
            return AjaxResult.error("面试来源ID不能为空");
        }

        // 查询未开始的面试记录
        List<TbOfflineInterview> list = service.selectTbOfflineInterviewList(queryDTO);
        if (list == null) {
            list = new ArrayList<>();
        }
        // 查询其他关联的面试记录
        if (interviewSource.equals(TalentSourceEnum.TALENT_POOL.getCode())) {
            TbPersonalInfo personalInfo = personalInfoService.selectTbPersonalInfoById(interviewSourceId);
            if (personalInfo != null && personalInfo.getTaskPersonalId() != null) {
                Integer idSource = personalInfo.getPersonalIdSource();
                queryDTO.setOfflineInterviewSourceId(personalInfo.getTaskPersonalId());
                queryDTO.setOfflineInterviewSource(idSource);
                list.addAll(service.selectTbOfflineInterviewList(queryDTO));
            }
        } else {
            TbPersonalInfo personalInfo = personalInfoService.selectTbPersonalInfoByType(interviewSourceId, interviewSource);
            if (personalInfo != null) {
                list.addAll(service.selectInterviewListByPerId(personalInfo.getId()));
            }
        }
        
        // 设置部门名称
        if (!list.isEmpty()) {
            for (TbOfflineInterview interview : list) {
                // 设置部门名称
                if (interview.getDeptId() != null) {
                    SysDept dept = sysDeptService.selectDeptById(interview.getDeptId());
                    if (dept != null) {
                        interview.setDeptName(dept.getDeptName());
                    }
                }
                
                // 设置AI评分
                if (interview.getId() != null) {
                    TbOfflineInterviewEvaluation aiEvaluation = evaluationService.selectAiEvaluationByOfflineInterviewId(interview.getId());
                    if (aiEvaluation != null && aiEvaluation.getScore() != null) {
                        interview.setAiScore(aiEvaluation.getScore());
                    }
                }
            }
        }
        
        return AjaxResult.success(list);
    }
}