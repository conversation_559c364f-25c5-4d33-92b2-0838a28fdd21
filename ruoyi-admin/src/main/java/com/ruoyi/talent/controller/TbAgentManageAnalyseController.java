package com.ruoyi.talent.controller;

import com.ruoyi.common.core.utils.PinyinUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import com.ruoyi.common.entity.domain.talent.TbAnalysePersonalFile;
import com.ruoyi.common.entity.domain.talent.TbAnalyseTask;
import com.ruoyi.common.entity.utils.DictUtils;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.TbAnalysePersonalInfoDto;
import com.ruoyi.talentbase.domain.enums.ParseStatusEnum;
import com.ruoyi.talentbase.domain.vo.TbAnalyseFileStateCount;
import com.ruoyi.talentbase.domain.vo.TbAnalyseTaskAgentVO;
import com.ruoyi.talentbase.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 【Agent服务接口】简历分析相关 Controller
 */
@RestController
@RequestMapping("/talentbase/agentManageAnalyse")
public class TbAgentManageAnalyseController extends BaseController {

    @Autowired
    private ITbAnalysePersonalInfoService personalInfoService;
    @Autowired
    private ITbAnalyseTaskService analyseTaskService;
    @Autowired
    private ITbAnalyseWorkExperienceService workExperienceService;
    @Autowired
    private ITbAnalyseEducationInfoService educationInfoService;
    @Autowired
    private ITbAnalyseProjectExperienceService projectExperienceService;
    @Autowired
    private ITbAnalysePersonalFileService personalFileService;

    /**
     * Agent 解析完成后新增个人简历信息
     */
    @Transactional
    @PostMapping
    public AjaxResult add(@RequestBody TbAnalysePersonalInfoDto dto) {
        Long taskId = dto.getJobId();
        TbAnalyseTask analyseTask = analyseTaskService.selectTbAnalyseTaskById(taskId);
        if (analyseTask == null) {
            return error("任务不存在");
        }
        // 基础信息复制
        TbAnalysePersonalInfo info = new TbAnalysePersonalInfo();
        BeanUtils.copyProperties(dto, info);
        info.setRecruitmentChannel("手动录入");
        info.setAvatar(null);
        // 生成姓名拼音
        if (StringUtils.isNotEmpty(dto.getUserName())) {
            String pinyin = PinyinUtils.getPinyin(dto.getUserName());
            info.setPinyin(pinyin);
        }
        if ("男".equals(dto.getSex())) {
            info.setSex("0");
        } else if ("女".equals(dto.getSex())) {
            info.setSex("1");
        } else {
            info.setSex("2");
        }
        List<String> skillList = dto.getSkillList();
        if (skillList != null && !skillList.isEmpty()) {
            info.setSkills(StringUtils.join(skillList, ","));
        }
        info.setTalentPoolStatus(0);
        info.setFiledFlag(0);

        // 根据简历评分更新优秀和良好数量
        if (dto.getTotalScore() != null) {
            if (dto.getTotalScore() >= 80) {
                // 评分大于等于80，优秀
                if (analyseTask.getResumeExcellentCount() == null) {
                    analyseTask.setResumeExcellentCount(1);
                } else {
                    analyseTask.setResumeExcellentCount(analyseTask.getResumeExcellentCount() + 1);
                }
            } else {
                // 评分小于80，良好
                if (analyseTask.getResumeGoodCount() == null) {
                    analyseTask.setResumeGoodCount(1);
                } else {
                    analyseTask.setResumeGoodCount(analyseTask.getResumeGoodCount() + 1);
                }
            }
        }
        analyseTaskService.updateTbAnalyseTask(analyseTask);

        Map<String, String> education = DictUtils.getDictCacheMap(SysDictDataEnum.EDUCATION.getName());
        if (education != null && info.getEducation() != null) {
            info.setEducation(education.get(info.getEducation()));
        }
        int row = personalInfoService.insertTbAnalysePersonalInfo(info);
        if (row > 0) {
            // 保存工作经历
            List<TbAnalyseWorkExperience> experienceList = dto.getTbWorkExperienceList();
            if (experienceList != null && !experienceList.isEmpty()) {
                for (TbAnalyseWorkExperience exp : experienceList) {
                    exp.setPersonalId(info.getId());
                    workExperienceService.insertTbAnalyseWorkExperience(exp);
                }
            }

            // 保存教育信息
            List<TbAnalyseEducationInfo> educationInfos = dto.getTbEducationInfoList();
            if (educationInfos != null && !educationInfos.isEmpty()) {
                for (TbAnalyseEducationInfo edu : educationInfos) {
                    edu.setPersonalId(info.getId());
                    educationInfoService.insertTbAnalyseEducationInfo(edu);
                }
            }

            // 保存项目经历
            List<TbAnalyseProjectExperience> projectExperienceList = dto.getTbProjectExperienceList();
            if (projectExperienceList != null && !projectExperienceList.isEmpty()) {
                for (TbAnalyseProjectExperience projectExp : projectExperienceList) {
                    projectExp.setPersonalId(info.getId());
                    projectExperienceService.insertTbAnalyseProjectExperience(projectExp);
                }
            }

            // 更新文件关联信息
            TbAnalysePersonalFile updateFile = new TbAnalysePersonalFile();
            updateFile.setId(dto.getFileId());
            updateFile.setTaskId(info.getJobId());
            updateFile.setPersonalId(info.getId());
            personalFileService.updateTbAnalysePersonalFile(updateFile);
        }
        return toAjax(row);
    }

    /**
     * Agent调用
     * 获取简历分析任务信息
     */
    @GetMapping("/getTaskInfo/{id}")
    public AjaxResult getTaskInfo(@PathVariable Long id) {
        TbAnalyseTask task = analyseTaskService.selectTbAnalyseTaskById(id);
        if (task == null) {
            return error("任务不存在");
        }
        TbAnalyseTaskAgentVO vo = new TbAnalyseTaskAgentVO();
        BeanUtils.copyProperties(task, vo);
        Map<String, String> educationMap = DictUtils.getDictCacheMap(SysDictDataEnum.EDUCATION.getName());
        vo.setEducationMap(educationMap);
        return AjaxResult.success(vo);
    }

    @PutMapping("/updateFileStatus/{id}/{status}")
    public AjaxResult updateFileStatus(@PathVariable Long id, @PathVariable Integer status) {
        if (!ParseStatusEnum.isValid(status)) {
            return error("非法的状态码");
        }

        // 先查询文件信息，获取taskId和当前状态
        TbAnalysePersonalFile existingFile = personalFileService.selectTbAnalysePersonalFileById(id);
        if (existingFile == null) {
            return error("文件不存在");
        }
        // 更新文件状态
        TbAnalysePersonalFile file = new TbAnalysePersonalFile();
        file.setId(id);
        file.setStatus(status);
        int row = personalFileService.updateTbAnalysePersonalFile(file);
        if (row > 0) {
            // 更新任务状态和文件数量
            TbAnalyseFileStateCount count = personalFileService.selectCount(existingFile.getTaskId());
            if (count != null) {
                count.setId(existingFile.getTaskId());
                analyseTaskService.updateTaskCount(count);
            }
        }
        return toAjax(row);
    }
}