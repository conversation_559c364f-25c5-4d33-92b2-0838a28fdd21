package com.ruoyi.talent.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.PdInfoType;
import com.ruoyi.common.entity.domain.PdTaskInfo;
import com.ruoyi.common.entity.domain.TPInfoEventEnum;
import com.ruoyi.common.entity.domain.TodoTaskDto;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeBase;
import com.ruoyi.knowledgebase.service.IKbbKnowledgeBaseService;
import com.ruoyi.talentbase.service.IPdTaskInfoService;
import com.ruoyi.talentbase.utils.TPInfoUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 待办任务信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@RestController
@RequestMapping("/talentbase/agentTodo")
public class PdTaskInfoManageController extends BaseController {
    @Autowired
    private IPdTaskInfoService pdTaskInfoService;
    @Autowired
    private IKbbKnowledgeBaseService kbbKnowledgeBaseService;

    /**
     * 更新已读标识
     */
    @PostMapping("/fileAdd")
    public AjaxResult fileAdd(@RequestBody TodoTaskDto todoTaskDto) {
        // 新增待办任务
        if (todoTaskDto.getKbId() != null) {
            KbbKnowledgeBase kbbKnowledgeBase = kbbKnowledgeBaseService.selectKbbKnowledgeBaseById(todoTaskDto.getKbId());
            todoTaskDto.setTitle(kbbKnowledgeBase.getKbName()+"/"+todoTaskDto.getTitle());
            todoTaskDto.setUserId(kbbKnowledgeBase.getUserId());
        }
        if (todoTaskDto.getStatus()!=null && todoTaskDto.getStatus() != 0) {
            TPInfoUtils.addKnowledgeFileParseTodo(todoTaskDto);
        }
        return AjaxResult.success();
    }

    /**
     * 删除待办任务信息
     */
    @DeleteMapping("/fileDel/{id}")
    public AjaxResult fileDel(@PathVariable("id") String id) {
        // 删除待办任务
        TPInfoUtils.remove(id, TPInfoEventEnum.KNOWLEDGE);
        return AjaxResult.success();
    }


    /**
     * 新增合同待办任务
     */
    @PostMapping("/contractAdd")
    public AjaxResult contractAdd(@RequestBody TodoTaskDto todoTaskDto) {
        TPInfoUtils.addContractAnalysisTodo(todoTaskDto);
        return AjaxResult.success();
    }

    /**
     * 删除合同待办任务
     */
    @DeleteMapping("/contractDel/{id}")
    public AjaxResult contractDel(@PathVariable("id") String id) {
        TPInfoUtils.remove(id, TPInfoEventEnum.CONTRACT);
        return AjaxResult.success();
    }

}