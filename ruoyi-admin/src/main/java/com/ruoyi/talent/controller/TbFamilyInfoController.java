package com.ruoyi.talent.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.talentbase.domain.TbFamilyInfo;
import com.ruoyi.talentbase.service.ITbFamilyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 家庭信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/talentbase/familyInfo")
public class TbFamilyInfoController extends BaseController
{
    @Autowired
    private ITbFamilyInfoService tbFamilyInfoService;

    /**
     * 查询家庭信息列表
     */
    @RequiresPermissions("talent:info:list")
    @GetMapping("/list")
    public TableDataInfo list(TbFamilyInfo tbFamilyInfo)
    {
        startPage();
        List<TbFamilyInfo> list = tbFamilyInfoService.selectTbFamilyInfoList(tbFamilyInfo);
        return getDataTable(list);
    }

    /**
     * 导出家庭信息列表
     */
    @RequiresPermissions("talent:info:export")
    @Log(title = "家庭信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbFamilyInfo tbFamilyInfo)
    {
        List<TbFamilyInfo> list = tbFamilyInfoService.selectTbFamilyInfoList(tbFamilyInfo);
        ExcelUtil<TbFamilyInfo> util = new ExcelUtil<TbFamilyInfo>(TbFamilyInfo.class);
        util.exportExcel(response, list, "家庭信息数据");
    }

    /**
     * 获取家庭信息详细信息
     */
    @RequiresPermissions("talent:info:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tbFamilyInfoService.selectTbFamilyInfoById(id));
    }

    /**
     * 新增家庭信息
     */
    @RequiresPermissions("talent:info:add")
    @Log(title = "家庭信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbFamilyInfo tbFamilyInfo)
    {
        return toAjax(tbFamilyInfoService.insertTbFamilyInfo(tbFamilyInfo));
    }

    /**
     * 修改家庭信息
     */
    @RequiresPermissions("talent:info:edit")
    @Log(title = "家庭信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbFamilyInfo tbFamilyInfo)
    {
        return toAjax(tbFamilyInfoService.updateTbFamilyInfo(tbFamilyInfo));
    }

    /**
     * 删除家庭信息
     */
    @RequiresPermissions("talent:info:remove")
    @Log(title = "家庭信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbFamilyInfoService.deleteTbFamilyInfoByIds(ids));
    }
}
