package com.ruoyi.talent.controller;

import com.ruoyi.common.core.utils.PinyinUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.talent.TaskStatusEnum;
import com.ruoyi.common.entity.domain.talent.TbAnalyseTask;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.entity.domain.talent.TbAnalysePersonalFile;
import com.ruoyi.talentbase.domain.TbpPositionBase;
import com.ruoyi.talentbase.domain.dto.AnalyseTaskQueryDTO;
import com.ruoyi.talentbase.domain.dto.ResumeListQueryDTO;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoDto;
import com.ruoyi.talentbase.domain.enums.ParseStatusEnum;
import com.ruoyi.talentbase.domain.enums.PersonalIdSourceEnum;
import com.ruoyi.talentbase.domain.vo.ResumeListVO;
import com.ruoyi.talentbase.domain.vo.TbAnalyseTaskVO;
import com.ruoyi.talentbase.service.*;
import com.ruoyi.talentbase.utils.TPInfoUtils;
import com.ruoyi.talentbase.utils.TalentAgentApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 简历分析任务Controller
 */
@Slf4j
@RestController
@RequestMapping("/talentbase/analyseTask")
public class TbAnalyseTaskController extends BaseController {

    @Autowired
    private ITbAnalyseTaskService taskService;

    @Autowired
    private ITbpPositionBaseService positionBaseService;

    @Autowired
    private TalentAgentApiUtil talentAgentApiUtil;

    @Autowired
    private ITbAnalyseTaskService analyseTaskService;

    @Autowired
    private AsyncParseService asyncParseService;

    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;

    @Autowired
    private ITbAnalysePersonalFileService tbAnalysePersonalFileService;

    /**
     * 查询任务列表
     */
    @GetMapping("/list")
    public TableDataInfo list(AnalyseTaskQueryDTO queryDTO) {
        startPage();
        List<TbAnalyseTask> list = taskService.selectTbAnalyseTaskList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        TbAnalyseTask analyseTask = taskService.selectTbAnalyseTaskById(id);
        if (analyseTask == null) {
            return AjaxResult.error("任务不存在");
        }
        if (analyseTask.getStatus() == TaskStatusEnum.COMPLETED.getCode()) {
            TPInfoUtils.removeResumeParseTask(id);
        }
        TbpPositionBase tbpPositionBase = positionBaseService.selectTbpPositionBaseById(analyseTask.getBaseId());
        if(tbpPositionBase != null){
            analyseTask.setOpenFlag(tbpPositionBase.getOpenFlag());
        }
        return AjaxResult.success(analyseTask);
    }

    /**
     * 新增任务
     */
    @Log(title = "简历分析任务", businessType = BusinessType.INSERT)
    @Transactional
    @PostMapping
    public AjaxResult add(@RequestBody TbAnalyseTask task) {
        if (task.getBaseId() == null) {
            return AjaxResult.error("请选择职位库");
        }
        TbpPositionBase positionBase = positionBaseService.selectTbpPositionBaseById(task.getBaseId());
        if (positionBase == null) {
            return AjaxResult.error("职位库不存在");
        }
        TbAnalyseTask query = new TbAnalyseTask();
        query.setBaseId(task.getBaseId());
        Integer count = taskService.selectTbAnalyseTaskCount(query);
        task.setTaskName(positionBase.getName()+"线下人才入库"+(count+ 1));
        task.setPositionName(positionBase.getName());
        task.setPositionRequirement(positionBase.getDescription());
        task.setUserId(SecurityUtils.getUserId());
        task.setCreateBy(SecurityUtils.getNickname());
        List<TbAnalysePersonalFile> files = task.getFiles();
        if (!files.isEmpty()){
            task.setStatus(TaskStatusEnum.IN_PROGRESS.getCode());
            task.setResumeCount(files.size());
        }else{
            task.setStatus(TaskStatusEnum.DRAFT.getCode());
        }
        int row = taskService.insertTbAnalyseTask(task);
        if (row > 0 && !files.isEmpty()) {
            Long taskId = task.getId();
            for(TbAnalysePersonalFile f: files){
                // 设置解析状态为解析中
                if(f.getStatus()==null){
                    f.setStatus(ParseStatusEnum.PARSING.getCode());
                }
                // 生成文件名拼音
                if (StringUtils.isNotEmpty(f.getFileName())) {
                    String pinyin = PinyinUtils.getPinyin(f.getFileName());
                    f.setPinyin(pinyin);
                }
                // 新增人才库信息
                TbPersonalInfoDto info = new TbPersonalInfoDto();
                info.setTaskId(taskId);
                info.setUserName(f.getFileName());
                info.setPinyin(f.getPinyin());
                info.setPersonalIdSource(PersonalIdSourceEnum.RESUME_POOL.getCode());
                info.setBaseId(task.getBaseId());
                tbPersonalInfoService.insertTbPersonalInfoDto(info);
                // 设置人才库ID
                f.setPersonalId(info.getId());
                f.setTaskId(taskId);
                // 增加完人才后在增加简历
                tbAnalysePersonalFileService.insertTbAnalysePersonalFile(f);
            }
            // 异步调用解析接口 - 使用专门的异步服务
            for(TbAnalysePersonalFile f: files){
                try {
                    asyncParseService.asyncParseTalent(f.getFileUrl(), f.getTaskId(), f.getId());
                } catch (Exception ex) {
                    log.error("异步调用简历解析接口异常", ex);
                }
            }
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    /**
     * 修改任务
     */
    @Log(title = "简历分析任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbAnalyseTask task) {
        task.setUpdateBy(SecurityUtils.getNickname());
        return toAjax(taskService.updateTbAnalyseTask(task));
    }

    /**
     * 删除任务
     */
    @Log(title = "简历分析任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        int row = taskService.deleteTbAnalyseTaskByIds(ids);
        if (row > 0) {
            for (Long id : ids) {
                TPInfoUtils.removeResumeParseTask(id);
            }
        }
        return toAjax(row);
    }

    /**
     * 根据任务ID复制基础信息（VO）
     */
    @GetMapping("/copyInfo/{id}")
    public AjaxResult copyInfo(@PathVariable Long id) {
        TbAnalyseTask task = taskService.selectTbAnalyseTaskById(id);
        if (task == null) {
            return AjaxResult.error("任务不存在");
        }
        TbAnalyseTaskVO vo = new TbAnalyseTaskVO();
        BeanUtils.copyProperties(task, vo);
        return AjaxResult.success(vo);
    }

    /**
     * 查询简历分析任务下的简历列表（统一接口）
     */
    @GetMapping("/resumeList")
    public TableDataInfo resumeList(ResumeListQueryDTO queryDTO) {
        startPage();
        List<ResumeListVO> list = taskService.selectResumeList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 修改查找人才任务状态
     */
    @GetMapping("/checkJobStatus/{baseId}")
    public AjaxResult checkJobStatus(@PathVariable Long baseId) {
        TbAnalyseTask query = new TbAnalyseTask();
        query.setBaseId(baseId);
        query.setStatus(TaskStatusEnum.IN_PROGRESS.getCode());
        int count = taskService.selectTbAnalyseTaskCount(query);
        if (count > 0) {
            return AjaxResult.error("当前存有正在进行中的任务");
        }
        return AjaxResult.success();
    }
}