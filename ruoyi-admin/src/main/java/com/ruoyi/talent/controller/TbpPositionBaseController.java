package com.ruoyi.talent.controller;

import com.ruoyi.common.core.enums.JobPostingStatus;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.talent.TaskStatusEnum;
import com.ruoyi.common.entity.domain.talent.TbAnalyseTask;
import com.ruoyi.common.entity.domain.talent.TbTaskPosting;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.talentbase.domain.TbPersonalInfo;
import com.ruoyi.talentbase.domain.TbpPositionBase;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoParamDto;
import com.ruoyi.talentbase.domain.enums.TalentSourceEnum;
import com.ruoyi.talentbase.domain.vo.TbPersonalTopicCount;
import com.ruoyi.talentbase.domain.vo.TbpPositionBaseCount;
import com.ruoyi.talentbase.service.*;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 职位库Controller
 *
 * <AUTHOR>
 * @date 2024-08-18
 */
@Api(tags = "职位库")
@RestController
@RequestMapping("/talentbase/positionBase")
public class TbpPositionBaseController extends BaseController {
    @Autowired
    private ITbpPositionBaseService tbpPositionBaseService;
    @Autowired
    private ITbPersonalInfoService iTbPersonalInfoService;
    @Autowired
    private ITbTaskPostingService taskPostingService;
    @Autowired
    private ITbAnalyseTaskService tbAnalyseTaskService;
    @Autowired
    private ITbOfflineInterviewService tbOfflineInterviewService;

    @Autowired
    private ITbOnlineInterviewService iTbOnlineInterviewService;

    /**
     * 查询职位库列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbpPositionBase tbpPositionBase) {
        startPage();
        List<TbpPositionBase> list = tbpPositionBaseService.selectTbpPositionBaseList(tbpPositionBase);
        for (TbpPositionBase base : list) {
            TbPersonalInfoParamDto infoDto = new TbPersonalInfoParamDto();
            infoDto.setBaseId(base.getId());
            infoDto.setSuitableFlag(0);
            TbPersonalTopicCount topicCount = iTbPersonalInfoService.selectTopicCount(infoDto);
            // 人数
            base.setPeopleCount(topicCount.getAllCount());
            // 关注数量
            base.setFollowCount(topicCount.getAttentionCount());
            // 进行中的任务
            TbTaskPosting tbTaskPosting = taskPostingService.selectTbTaskPostingByBaseId(base.getId(), JobPostingStatus.PROCESSING);
            if (tbTaskPosting != null) {
                base.setFindCount(1);
                base.setTaskId(tbTaskPosting.getId());
            } else {
                base.setFindCount(0);
            }
            // 是否存有进行中的简历分析
            TbAnalyseTask analyseTask = new TbAnalyseTask();
            analyseTask.setBaseId(base.getId());
            analyseTask.setStatus(TaskStatusEnum.IN_PROGRESS.getCode());
            int analyseTaskCount = tbAnalyseTaskService.selectTbAnalyseTaskCount(analyseTask);
            base.setResumeCount(analyseTaskCount);
        }

        return getDataTable(list);
    }

    /**
     * 导出职位库列表
     */
    @Log(title = "职位库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbpPositionBase tbpPositionBase) {
        List<TbpPositionBase> list = tbpPositionBaseService.selectTbpPositionBaseList(tbpPositionBase);
        ExcelUtil<TbpPositionBase> util = new ExcelUtil<TbpPositionBase>(TbpPositionBase.class);
        util.exportExcel(response, list, "职位库数据");
    }

    /**
     * 获取职位库详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TbpPositionBase base = tbpPositionBaseService.selectTbpPositionBaseById(id);
        TbPersonalInfoParamDto infoDto = new TbPersonalInfoParamDto();
        infoDto.setBaseId(base.getId());
        infoDto.setSuitableFlag(0);
        TbPersonalTopicCount topicCount = iTbPersonalInfoService.selectTopicCount(infoDto);
        // 人数
        base.setPeopleCount(topicCount.getAllCount());
        // 关注数量
        base.setFollowCount(topicCount.getAttentionCount());
        // 进行中的任务
        TbTaskPosting tbTaskPosting = taskPostingService.selectTbTaskPostingByBaseId(base.getId(), JobPostingStatus.PROCESSING);
        base.setTaskPosting(tbTaskPosting);
        // 进行中的简历分析
        TbAnalyseTask analyseTask = tbAnalyseTaskService.selectTbAnalyseTaskByBaseId(base.getId(), TaskStatusEnum.IN_PROGRESS);
        base.setAnalyseTask(analyseTask);
        return success(base);
    }

    /**
     * 新增职位库
     */
    @Log(title = "职位库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbpPositionBase tbpPositionBase) {
        AjaxResult check = check(tbpPositionBase);
        if (check != null) {
            return check;
        }
        return toAjax(tbpPositionBaseService.insertTbpPositionBase(tbpPositionBase));
    }

    /**
     * 修改职位库
     */
    @Log(title = "职位库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbpPositionBase tbpPositionBase) {
        AjaxResult check = check(tbpPositionBase);
        if (check != null) {
            return check;
        }
        if (tbpPositionBase.getOpenFlag() == 1) {
            // 查询职位库下的所有人员
            TbPersonalInfo personalInfo = new TbPersonalInfo();
            personalInfo.setBaseId(tbpPositionBase.getId());
            List<TbPersonalInfo> list = iTbPersonalInfoService.selectTbPersonalInfoList(personalInfo);
            //已安排但未完成的面试将自动过期;
            for (TbPersonalInfo info : list) {
                tbOfflineInterviewService.expireOfflineInterviewsBySource(TalentSourceEnum.TALENT_POOL.getCode(), info.getId());
                iTbOnlineInterviewService.expireOnlineInterviewsBySource(TalentSourceEnum.TALENT_POOL.getCode(), info.getId());
            }
            //将取消职位置顶
            tbpPositionBase.setSort(1);
            tbpPositionBase.setSortTime(System.currentTimeMillis());
        }

        return toAjax(tbpPositionBaseService.updateTbpPositionBase(tbpPositionBase));
    }

    /**
     * 改变开放标识
     */
    @Log(title = "职位库", businessType = BusinessType.UPDATE)
    @PutMapping("/editOpenFlag/{id}/{openFlag}")
    public AjaxResult editOpenFlag(@PathVariable("id") Long id, @PathVariable("openFlag") Integer openFlag) {
        TbpPositionBase tbpPositionBase = new TbpPositionBase();
        tbpPositionBase.setId(id);
        tbpPositionBase.setOpenFlag(openFlag);
        if (openFlag == 1) {
            // 查询职位库下的所有人员
            TbPersonalInfo personalInfo = new TbPersonalInfo();
            personalInfo.setBaseId(id);
            List<TbPersonalInfo> list = iTbPersonalInfoService.selectTbPersonalInfoList(personalInfo);
            //已安排但未完成的面试将自动过期;
            for (TbPersonalInfo info : list) {
                tbOfflineInterviewService.expireOfflineInterviewsBySource(TalentSourceEnum.TALENT_POOL.getCode(), info.getId());
                iTbOnlineInterviewService.expireOnlineInterviewsBySource(TalentSourceEnum.TALENT_POOL.getCode(), info.getId());
            }
            //将取消职位置顶
            tbpPositionBase.setSort(1);
            tbpPositionBase.setSortTime(System.currentTimeMillis());
        }
        return toAjax(tbpPositionBaseService.updateTbpPositionBase(tbpPositionBase));
    }

    /**
     * 置顶
     */
    @Log(title = "职位库", businessType = BusinessType.UPDATE)
    @PutMapping("/editTop/{id}/{topFlag}")
    public AjaxResult editTop(@PathVariable("id") Long id, @PathVariable("topFlag") Integer topFlag) {
        // 查询详情
        TbpPositionBase base = tbpPositionBaseService.selectTbpPositionBaseById(id);
        if (base == null) {
            return AjaxResult.error("职位库不存在");
        }
        if (!base.getOpenFlag().equals(0)) {
            return AjaxResult.error("当前职位库状态不允许更新");
        }
        return toAjax(tbpPositionBaseService.editTop(id, topFlag));
    }

    /**
     * 删除职位库
     */
    @Log(title = "职位库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbpPositionBaseService.deleteTbpPositionBaseByIds(ids));
    }

    /**
     * 校验
     */
    private AjaxResult check(TbpPositionBase tbpPositionBase) {
        if (tbpPositionBase.getOpenFlag() == null) {
            return AjaxResult.error("开放标识不能为空");
        }
        if (tbpPositionBase.getName() == null) {
            return AjaxResult.error("名称不能为空");
        }
        Integer maxSortCode = tbpPositionBaseService.getMaxSortCode(tbpPositionBase.getOpenFlag());
        if (maxSortCode != null && tbpPositionBase.getSort() != null && tbpPositionBase.getSort() > maxSortCode) {
            return AjaxResult.error("输入的排序值不能大于最大排序值: " + maxSortCode);
        }
        return null;
    }

    /**
     * 职位库统计
     */
    @GetMapping("/getPositionBaseCount")
    public AjaxResult getPositionBaseCount(TbpPositionBase tbpPositionBase) {
        TbpPositionBaseCount countVo = tbpPositionBaseService.getPositionBaseCount(tbpPositionBase);
        return success(countVo);
    }

    /**
     * 职位库校验
     */
    @GetMapping("/check/{id}")
    public AjaxResult check(@PathVariable("id") Long id) {
        TbpPositionBase tbpPositionBase = tbpPositionBaseService.selectTbpPositionBaseById(id);
        if (tbpPositionBase == null) {
            return AjaxResult.error("职位库不存在");
        }
        return success(tbpPositionBase.getOpenFlag());
    }
}