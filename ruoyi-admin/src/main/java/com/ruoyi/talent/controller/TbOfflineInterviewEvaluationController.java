package com.ruoyi.talent.controller;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.talentbase.domain.TbOfflineInterviewEvaluation;
import com.ruoyi.talentbase.domain.enums.EvaluationTypeEnum;
import com.ruoyi.talentbase.service.ITbOfflineInterviewEvaluationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 线下面试评价Controller
 */
@RestController
@RequestMapping("/talentbase/offlineInterviewEvaluation")
public class TbOfflineInterviewEvaluationController extends BaseController {
    @Autowired
    private ITbOfflineInterviewEvaluationService tbOfflineInterviewEvaluationService;
    @Autowired
    private RedisService redisService;

    /**
     * 查询线下面试评价列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        startPage();
        List<TbOfflineInterviewEvaluation> list = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationList(tbOfflineInterviewEvaluation);
        return getDataTable(list);
    }


    /**
     * 查询线下面试评价列表
     */
    @GetMapping("/getInterviewList")
    public AjaxResult getInterviewList(TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        if(StringUtils.isEmpty(tbOfflineInterviewEvaluation.getEvaluationType())){
            tbOfflineInterviewEvaluation.setEvaluationType(EvaluationTypeEnum.INTERVIEWER.getCode());
        }
        List<TbOfflineInterviewEvaluation> list = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationList(tbOfflineInterviewEvaluation);
//        list.forEach(evaluation -> evaluation.setEvaluationInfo(null));
        return success(list);
    }

    /**
     * 导出线下面试评价列表
     */
    @Log(title = "线下面试评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        List<TbOfflineInterviewEvaluation> list = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationList(tbOfflineInterviewEvaluation);
        ExcelUtil<TbOfflineInterviewEvaluation> util = new ExcelUtil<TbOfflineInterviewEvaluation>(TbOfflineInterviewEvaluation.class);
        util.exportExcel(response, list, "线下面试评价数据");
    }

    /**
     * 获取线下面试评价详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TbOfflineInterviewEvaluation evaluation = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationById(id);
        if (evaluation == null) {
            return error("该评价不存在");
        }
        evaluation.refreshList();
        return success(evaluation);
    }

    /**
     * 新增线下面试评价
     */
    @Log(title = "线下面试评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        tbOfflineInterviewEvaluation.setEvaluationType(EvaluationTypeEnum.INTERVIEWER.getCode());
        tbOfflineInterviewEvaluation.refreshText();
        tbOfflineInterviewEvaluation.setInterviewerName(SecurityUtils.getNickname());
        tbOfflineInterviewEvaluation.setInterviewerUserName(SecurityUtils.getUsername());
        tbOfflineInterviewEvaluation.setInterviewerUserId(SecurityUtils.getUserId());
        tbOfflineInterviewEvaluation.setCreateTime(DateUtils.getNowDate());
        tbOfflineInterviewEvaluation.setCreateBy(SecurityUtils.getUsername());
        int rows = tbOfflineInterviewEvaluationService.insertTbOfflineInterviewEvaluation(tbOfflineInterviewEvaluation);
        if (rows > 0) {
            return success(tbOfflineInterviewEvaluation);
        }
        return error("新增评价失败");
    }

    /**
     * 修改线下面试评价
     */
    @Log(title = "线下面试评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        // 获取当前评价信息
        TbOfflineInterviewEvaluation existingEvaluation = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationById(tbOfflineInterviewEvaluation.getId());
        if (existingEvaluation == null) {
            return error("评价不存在");
        }

        // 校验是否是当前用户创建的评价
        if (existingEvaluation.getInterviewerUserId() == null ||
                !existingEvaluation.getInterviewerUserId().equals(SecurityUtils.getUserId())) {
            return error("只能修改自己创建的评价");
        }
        existingEvaluation.setOfflineInterviewId(null);

        tbOfflineInterviewEvaluation.setInterviewerName(SecurityUtils.getNickname());
        tbOfflineInterviewEvaluation.setInterviewerUserName(SecurityUtils.getUsername());
        tbOfflineInterviewEvaluation.setInterviewerUserId(SecurityUtils.getUserId());
        int rows = tbOfflineInterviewEvaluationService.updateTbOfflineInterviewEvaluation(tbOfflineInterviewEvaluation);
        if (rows > 0) {
            // 查询更新后的数据
            TbOfflineInterviewEvaluation updatedEvaluation = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationById(tbOfflineInterviewEvaluation.getId());
            return success(updatedEvaluation);
        }
        return error("修改评价失败");
    }

    /**
     * 删除线下面试评价
     */
    @Log(title = "线下面试评价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        if (id == null) {
            return error("请选择要删除的评价");
        }
        TbOfflineInterviewEvaluation evaluation = tbOfflineInterviewEvaluationService.selectTbOfflineInterviewEvaluationById(id);
        if (evaluation == null){
            return error("评价不存在");
        }
        if (!evaluation.getEvaluationType().equals(EvaluationTypeEnum.INTERVIEWER.getCode())) {
            return error("只能删除面试官的评价");
        }
        return toAjax(tbOfflineInterviewEvaluationService.deleteTbOfflineInterviewEvaluationById(id));
    }

    @PostMapping("/submit/{uuid}")
    public AjaxResult submit(@PathVariable String uuid, @RequestBody TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        // TODO:设置填写信息过期时间的key
        tbOfflineInterviewEvaluation.setEvaluationType(EvaluationTypeEnum.INTERVIEWER.getCode());
        tbOfflineInterviewEvaluation.refreshText();
        return toAjax(tbOfflineInterviewEvaluationService.insertTbOfflineInterviewEvaluation(tbOfflineInterviewEvaluation));
    }

    @Log(title = "面试评价", businessType = BusinessType.UPDATE)
    @PutMapping("/updateEvaluation")
    public AjaxResult updateEvaluation(@RequestBody TbOfflineInterviewEvaluation tbOfflineInterviewEvaluation) {
        tbOfflineInterviewEvaluation.setEvaluationType(EvaluationTypeEnum.INTERVIEWER.getCode());
        tbOfflineInterviewEvaluation.setInterviewerName(SecurityUtils.getNickname());
        tbOfflineInterviewEvaluation.setInterviewerUserName(SecurityUtils.getUsername());
        tbOfflineInterviewEvaluation.setInterviewerUserId(SecurityUtils.getUserId());
        tbOfflineInterviewEvaluation.refreshText();
        return toAjax(tbOfflineInterviewEvaluationService.updateTbOfflineInterviewEvaluation(tbOfflineInterviewEvaluation));
    }
}