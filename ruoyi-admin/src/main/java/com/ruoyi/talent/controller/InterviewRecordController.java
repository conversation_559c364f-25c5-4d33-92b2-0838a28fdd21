package com.ruoyi.talent.controller;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.entity.domain.talent.InterviewStatusEnum;
import com.ruoyi.common.entity.domain.talent.TbInterviewJobPaper;
import com.ruoyi.common.entity.domain.talent.TbOnlineInterview;
import com.ruoyi.talentbase.utils.TPInfoUtils;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.OfflineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewQueryDTO;
import com.ruoyi.talentbase.domain.enums.InterviewTypeEnum;
import com.ruoyi.talentbase.domain.enums.TalentSourceEnum;
import com.ruoyi.talentbase.domain.vo.InterviewRecordVO;
import com.ruoyi.talentbase.domain.vo.OfflineInterviewConversationVO;
import com.ruoyi.talentbase.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/talentbase/interviewRecord")
public class InterviewRecordController extends BaseController {

    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;
    @Autowired
    private ITbOnlineInterviewService onlineInterviewService;

    @Autowired
    private ITbOfflineInterviewService offlineInterviewService;

    @Autowired
    private ITbInterviewJobPaperService jobPaperService;

    @Autowired
    private ITbInterviewJobPaperQuestionService jobPaperQuestionService;

    @Autowired
    private ITbOfflineInterviewEvaluationService offlineInterviewEvaluationService;

    @Autowired
    private ITbOnlineInterviewEvaluationService onlineInterviewEvaluationService;

    @Autowired
    private IOfflineInterviewAudioFileService offlineInterviewAudioFileService;

    @Autowired
    private IOfflineInterviewConversationService offlineInterviewConversationService;

    @Autowired
    private ITbOnlineInterviewAnswerService onlineInterviewAnswerService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private ISysConfigService sysConfigService;

    @GetMapping("/list")
    public AjaxResult list(@RequestParam Integer sourceType, @RequestParam Long sourceId) {
        List<InterviewRecordVO> result = new ArrayList<>();
        // 1. 查询线上面试记录
        OnlineInterviewQueryDTO onlineQuery = new OnlineInterviewQueryDTO();
        onlineQuery.setOnlineInterviewSource(sourceType);
        onlineQuery.setOnlineInterviewSourceId(sourceId);
        onlineQuery.setInterviewStatus(InterviewStatusEnum.COMPLETED.getCode());
        List<TbOnlineInterview> onlineInterviews = onlineInterviewService.selectTbOnlineInterviewList(onlineQuery);
        // 3. 查询线下面试记录
        OfflineInterviewQueryDTO offlineQuery = new OfflineInterviewQueryDTO();
        offlineQuery.setOfflineInterviewSource(sourceType);
        offlineQuery.setOfflineInterviewSourceId(sourceId);
        offlineQuery.setInterviewStatus(InterviewStatusEnum.COMPLETED.getCode());
        List<TbOfflineInterview> offlineInterviews = offlineInterviewService.selectTbOfflineInterviewList(offlineQuery);
        // 处理查询结果为null
        if (onlineInterviews == null) {
            onlineInterviews = new ArrayList<>();
        }
        if (offlineInterviews == null) {
            offlineInterviews = new ArrayList<>();
        }
        // 处理人才库信息
        TbPersonalInfo personalInfo;
        // 查询人才库是否存有相关信息
        if (sourceType.equals(TalentSourceEnum.TALENT_POOL.getCode())) {
            // 查询来源面试记录
            personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(sourceId);
            if (personalInfo != null && personalInfo.getTaskPersonalId() != null && personalInfo.getPersonalIdSource() != null) {
                // 查询线上面试记录
                onlineQuery.setOnlineInterviewSource(personalInfo.getPersonalIdSource());
                onlineQuery.setOnlineInterviewSourceId(personalInfo.getTaskPersonalId());
                onlineInterviews.addAll(onlineInterviewService.selectTbOnlineInterviewList(onlineQuery));
                // 查询线下面试记录
                offlineQuery.setOfflineInterviewSource(personalInfo.getPersonalIdSource());
                offlineQuery.setOfflineInterviewSourceId(personalInfo.getTaskPersonalId());
                offlineInterviews.addAll(offlineInterviewService.selectTbOfflineInterviewList(offlineQuery));
            }
        } else {
            // 查询人才面试记录
            personalInfo = tbPersonalInfoService.selectTbPersonalInfoByType(sourceId, sourceType);
            if (personalInfo != null) {
                Long infoId = personalInfo.getId();
                // 查询线上面试记录
                onlineInterviews.addAll(onlineInterviewService.selectInterviewListByPerId(infoId));
                // 查询线下面试记录
                offlineInterviews.addAll(offlineInterviewService.selectInterviewListByPerId(infoId));
            }
        }
        // 组装
        onlineInterviewList(result, onlineInterviews);
        offlineInterviewList(result, offlineInterviews);
        // 使用Stream API按面试时间倒序排序
        List<InterviewRecordVO> sortedResult = result.stream()
                .sorted(Comparator.comparing(
                        InterviewRecordVO :: getSortTime,
                        Comparator.nullsLast(String :: compareTo)
                ).reversed())
                .collect(Collectors.toList());
        return AjaxResult.success(sortedResult);
    }


    @GetMapping("/getOfflineViewInfo/{id}/{type}")
    public AjaxResult getInfo(@PathVariable Long id, @PathVariable Integer type) {
        if (type == null || id == null) {
            return AjaxResult.error("参数错误");
        }
        InterviewRecordVO vo;
        if (type.equals(InterviewTypeEnum.ONLINE.getCode())) {
            vo = onlineInterviewToVO(onlineInterviewService.selectTbOnlineInterviewById(id));
            // 移除待办信息
            TPInfoUtils.removeOnlineInterviewTodo(id);
        } else if (type.equals(InterviewTypeEnum.OFFLINE.getCode())) {
            vo = offlineInterviewToVO(offlineInterviewService.selectTbOfflineInterviewById(id));
        } else {
            return AjaxResult.error("未能查询到相关面试类型");
        }
        return AjaxResult.success(vo);
    }


    @GetMapping("/offlineInterview/audioAndConversation/{id}")
    public AjaxResult getOfflineInterviewAudioAndConversation(@PathVariable("id") Long offlineInterviewId) {
        // 查询面试记录是否存在
        TbOfflineInterview interview = offlineInterviewService.selectTbOfflineInterviewById(offlineInterviewId);
        if (interview == null) {
            return AjaxResult.error("未找到对应的线下面试记录");
        }

        // 创建返回对象
        InterviewRecordVO vo = new InterviewRecordVO();
        vo.setInterviewId(offlineInterviewId);
        vo.setInterviewType(InterviewTypeEnum.OFFLINE.getCode());

        // 获取音频文件
        OfflineInterviewAudioFile audioFileQuery = new OfflineInterviewAudioFile();
        audioFileQuery.setOfflineInterviewId(offlineInterviewId);
        List<OfflineInterviewAudioFile> audioFiles = offlineInterviewAudioFileService.selectOfflineInterviewAudioFileList(audioFileQuery);
        if (audioFiles != null && !audioFiles.isEmpty()) {
            vo.setAudioFile(audioFiles.get(0));
        }

        // 获取会话记录
        OfflineInterviewConversation conversationQuery = new OfflineInterviewConversation();
        conversationQuery.setOfflineInterviewId(offlineInterviewId);
        List<OfflineInterviewConversationVO> conversations = offlineInterviewConversationService.selectOfflineInterviewConversationVOList(conversationQuery);
        vo.setConversations(conversations);

        return AjaxResult.success(vo);
    }


    private void onlineInterviewList(List<InterviewRecordVO> result, List<TbOnlineInterview> onlineInterviews) {
        // 2. 处理线上面试记录
        for (TbOnlineInterview interview : onlineInterviews) {
            InterviewRecordVO vo = onlineInterviewToVO(interview);
            result.add(vo);
            // 移除待办信息
            TPInfoUtils.removeOnlineInterviewTodo(interview.getId());

        }
    }

    private void offlineInterviewList(List<InterviewRecordVO> result, List<TbOfflineInterview> offlineInterviews) {
        // 4. 处理线下面试记录
        for (TbOfflineInterview interview : offlineInterviews) {
            InterviewRecordVO vo = offlineInterviewToVO(interview);
            result.add(vo);
        }
    }

    // 线上面试转VO方法
    private InterviewRecordVO onlineInterviewToVO(TbOnlineInterview interview) {
        InterviewRecordVO vo = new InterviewRecordVO();
        BeanUtil.copyProperties(interview, vo);
        vo.setInterviewType(InterviewTypeEnum.ONLINE.getCode());
        vo.setInterviewId(interview.getId());
        vo.setInterviewLabel(InterviewTypeEnum.ONLINE.getDesc());
        vo.setInterviewJob(interview.getJobIntention());
        vo.setInterviewTime(interview.getInterviewTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewTime()) : null);
        vo.setInterviewEndTime(interview.getInterviewEndTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewEndTime()) : null);
        vo.setCreateTime(interview.getCreateTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getCreateTime()) : null);
        vo.setOperation(interview.getOperator());
        // 设置排序时间：面试完成状态用面试时间，其他状态用创建时间
        if (InterviewStatusEnum.COMPLETED.getCode() == interview.getInterviewStatus() && interview.getInterviewTime() != null) {
            vo.setSortTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewTime()));
        } else {
            vo.setSortTime(interview.getCreateTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getCreateTime()) : null);
        }

        // 计算线上面试时长
        if (interview.getInterviewTime() != null && interview.getInterviewEndTime() != null) {
            long duration = interview.getInterviewEndTime().getTime() - interview.getInterviewTime().getTime();
            long minutes = duration / (60 * 1000);
            long seconds = (duration / 1000) % 60;
            vo.setInterviewDuration(minutes + "分" + seconds + "秒");
        }

        // 获取面试评价
        TbOnlineInterviewEvaluation evaluationQuery = new TbOnlineInterviewEvaluation();
        evaluationQuery.setOnlineInterviewId(interview.getId());
        List<TbOnlineInterviewEvaluation> evaluations = onlineInterviewEvaluationService.selectTbOnlineInterviewEvaluationList(evaluationQuery);
        if (!evaluations.isEmpty()) {
            vo.setOnlineEvaluations(evaluations);
        }

        // 获取线上面试对话记录
        List<TbOnlineInterviewAnswer> answers = onlineInterviewAnswerService.selectByInterviewId(interview.getId());
        if (answers != null && !answers.isEmpty()) {
            vo.setOnlineAnswers(answers);
        }

        // 获取面试题目
        if (interview.getJobPaperId() != null) {
            TbInterviewJobPaper interviewJobPaper = jobPaperService.selectTbInterviewJobPaperById(interview.getJobPaperId());
            vo.setInterviewJobPaper(interviewJobPaper);
            vo.setJobDesc(interviewJobPaper.getJobDesc());
            if (interview.getInterviewStatus() == InterviewStatusEnum.EXPIRED.getCode()) {
                vo.setInviteExpireTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getUpdateTime()));
            } else {
                Date expireDate = DateUtils.addDays(interview.getInviteTime(), interviewJobPaper.getValidDays());
                vo.setInviteExpireTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", expireDate));
            }
            // 设置部门名称
            if (interviewJobPaper.getDeptId() != null) {
                SysDept dept = sysDeptService.selectDeptById(interviewJobPaper.getDeptId());
                if (dept != null) {
                    vo.setDeptName(dept.getDeptName());
                }
            }
            TbInterviewJobPaperQuestion questionQuery = new TbInterviewJobPaperQuestion();
            questionQuery.setJobPaperId(interview.getJobPaperId());
            List<TbInterviewJobPaperQuestion> questions = jobPaperQuestionService.selectTbInterviewJobPaperQuestionList(questionQuery);
            vo.setQuestions(questions);
        }
        String domain = sysConfigService.selectConfigByKey("hr.interview.url");
        vo.setInviteUrl(domain + interview.getInviteUrl());
        return vo;
    }


    // 线下面试转VO方法
    private InterviewRecordVO offlineInterviewToVO(TbOfflineInterview interview) {
        InterviewRecordVO vo = new InterviewRecordVO();
        vo.setInterviewType(InterviewTypeEnum.OFFLINE.getCode());
        vo.setInterviewId(interview.getId());
        vo.setInterviewLabel(InterviewTypeEnum.OFFLINE.getDesc());
        vo.setInterviewRound(interview.getInterviewRound());
        vo.setInterviewJob(interview.getJobIntention());
        vo.setInterviewTime(interview.getInterviewTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewTime()) : null);
        vo.setInterviewEndTime(interview.getInterviewEndTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewEndTime()) : null);
        vo.setInterviewStatus(interview.getInterviewStatus());
        vo.setOperation(interview.getOperator());
        vo.setDeptId(interview.getId());
        // 设置部门名称
        if (interview.getDeptId() != null) {
            SysDept dept = sysDeptService.selectDeptById(interview.getDeptId());
            if (dept != null) {
                vo.setDeptName(dept.getDeptName());
            }
        }
        vo.setJobDesc(interview.getJobDesc());
        vo.setCreateTime(interview.getCreateTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getCreateTime()) : null);
        // 设置排序时间：面试完成状态用面试时间，其他状态用创建时间
        if (InterviewStatusEnum.COMPLETED.getCode() == interview.getInterviewStatus() && interview.getInterviewTime() != null) {
            vo.setSortTime(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getInterviewTime()));
        } else {
            vo.setSortTime(interview.getCreateTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", interview.getCreateTime()) : null);
        }

        // 计算面试时长
        if (interview.getInterviewTime() != null && interview.getInterviewEndTime() != null) {
            long duration = interview.getInterviewEndTime().getTime() - interview.getInterviewTime().getTime();
            long minutes = duration / (60 * 1000);
            long seconds = (duration / 1000) % 60;
            vo.setInterviewDuration(minutes + "分" + seconds + "秒");
        }

        // 获取面试评价
        TbOfflineInterviewEvaluation evaluationQuery = new TbOfflineInterviewEvaluation();
        evaluationQuery.setOfflineInterviewId(interview.getId());
        List<TbOfflineInterviewEvaluation> evaluations = offlineInterviewEvaluationService.selectTbOfflineInterviewEvaluationList(evaluationQuery);
        if (!evaluations.isEmpty()) {
            vo.setEvaluations(evaluations);
        }

        // 获取音频文件
        OfflineInterviewAudioFile audioFileQuery = new OfflineInterviewAudioFile();
        audioFileQuery.setOfflineInterviewId(interview.getId());
        List<OfflineInterviewAudioFile> audioFiles = offlineInterviewAudioFileService.selectOfflineInterviewAudioFileList(audioFileQuery);
        if (audioFiles != null && !audioFiles.isEmpty()) {
            vo.setAudioFile(audioFiles.get(0));
        }

        // 获取会话记录
        OfflineInterviewConversation conversationQuery = new OfflineInterviewConversation();
        conversationQuery.setOfflineInterviewId(interview.getId());
        List<OfflineInterviewConversationVO> conversations = offlineInterviewConversationService.selectOfflineInterviewConversationVOList(conversationQuery);
        vo.setConversations(conversations);
        return vo;
    }
}