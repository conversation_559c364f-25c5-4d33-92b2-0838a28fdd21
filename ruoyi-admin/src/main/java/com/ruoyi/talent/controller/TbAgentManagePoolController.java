package com.ruoyi.talent.controller;

import com.ruoyi.common.core.enums.JobPostingStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import com.ruoyi.common.entity.domain.talent.TbAnalysePersonalFile;
import com.ruoyi.common.entity.domain.talent.TbAnalyseTask;
import com.ruoyi.common.entity.domain.talent.TbTaskPosting;
import com.ruoyi.common.entity.utils.DictUtils;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoDto;
import com.ruoyi.talentbase.domain.dto.TbTaskPersonalInfoDto;
import com.ruoyi.talentbase.domain.enums.ParseStatusEnum;
import com.ruoyi.talentbase.domain.enums.PersonalIdSourceEnum;
import com.ruoyi.talentbase.domain.vo.TbAnalyseFileStateCount;
import com.ruoyi.talentbase.domain.vo.TbAnalyseFileVo;
import com.ruoyi.talentbase.domain.vo.TbAnalyseTaskAgentVO;
import com.ruoyi.talentbase.domain.vo.TbTaskPostingVo;
import com.ruoyi.talentbase.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 【Agent服务接口】Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/talentbase/agentManagePool")
public class TbAgentManagePoolController extends BaseController {
    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;
    @Autowired
    private ITbTaskPostingService tbJobPostingService;
    @Autowired
    private ITbAnalyseTaskService analyseTaskService;
    @Autowired
    private ITbAnalysePersonalFileService personalFileService;
    @Autowired
    private ITbpResumeEvaluateService tbpResumeEvaluateService;

    /**
     * 新增【请填写功能名称】
     */
    @Transactional
    @PostMapping("/{flag}")
    public AjaxResult add(@PathVariable("flag") Integer flag, @RequestBody TbTaskPersonalInfoDto dto) {
        // 转换数据
        if (flag == 1) {
            updateTbTask(dto);
            dto.setSourceEnum(PersonalIdSourceEnum.TASK_POOL);
        } else {
            updateAnalyse(dto);
            dto.setSourceEnum(PersonalIdSourceEnum.RESUME_POOL);
        }
        Long id = this.convert(dto);
        if (id != null) {
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    /**
     * Agent调用
     * 获取任务信息
     */
    @GetMapping("/getPostingInfo/{id}/{flag}")
    public AjaxResult getPostingInfo(@PathVariable Long id, @PathVariable Integer flag) {
        if (flag == 1) {
            // 寻才任务
            TbTaskPosting tbJobPosting = tbJobPostingService.selectTbTaskPostingById(id);
            TbTaskPostingVo tbJobPostingVo = new TbTaskPostingVo();
            BeanUtils.copyProperties(tbJobPosting, tbJobPostingVo);
            Map<String, String> map = DictUtils.getDictCacheMap(SysDictDataEnum.EDUCATION.getName());
            tbJobPostingVo.setEducationMap(map);
            return AjaxResult.success(tbJobPostingVo);
        } else if (flag == 2) {
            // 简历解析
            TbAnalyseTask task = analyseTaskService.selectTbAnalyseTaskById(id);
            if (task == null) {
                return error("任务不存在");
            }
            TbAnalyseTaskAgentVO vo = new TbAnalyseTaskAgentVO();
            BeanUtils.copyProperties(task, vo);
            Map<String, String> educationMap = DictUtils.getDictCacheMap(SysDictDataEnum.EDUCATION.getName());
            vo.setEducationMap(educationMap);
            return AjaxResult.success(vo);
        }
        return AjaxResult.error();
    }


    /**
     * 新增或更新评价
     *
     * @param
     */
    @PostMapping("/saveEvaluate")
    public AjaxResult saveEvaluate(@RequestBody TbTaskPersonalInfoDto dto) {
        TbpResumeEvaluate tbpResumeEvaluate = new TbpResumeEvaluate();
        BeanUtils.copyProperties(dto, tbpResumeEvaluate);
        tbpResumeEvaluate.setTalentId(dto.getId());
        tbpResumeEvaluate.setComprehensiveEvaluation(dto.getComprehensiveEvaluation());
        tbpResumeEvaluateService.insertTbpResumeEvaluate(tbpResumeEvaluate);
        // 更新人才信息评价状态
        TbPersonalInfo tbPersonalInfo = tbPersonalInfoService.selectTbPersonalInfoById(dto.getId());
        tbPersonalInfo.setEvaluateStatus(0);
        tbPersonalInfo.setTotalScore(dto.getTotalScore());
        tbPersonalInfoService.updateById(tbPersonalInfo);
        return AjaxResult.success();
    }


    /**
     * 新增或更新评价
     *
     * @param
     */
    @GetMapping("/editEvaluateState/{id}/{state}")
    public AjaxResult editEvaluateState(@PathVariable Long id, @PathVariable Integer state) {
        TbPersonalInfo tbPersonalInfo = tbPersonalInfoService.selectTbPersonalInfoById(id);
        tbPersonalInfo.setEvaluateStatus(state);
        tbPersonalInfoService.updateById(tbPersonalInfo);
        return AjaxResult.success();
    }


    // 更新简历解析任务
    private void updateAnalyse(TbTaskPersonalInfoDto dto) {
        Long taskId = dto.getJobId();
        TbAnalyseTask analyseTask = analyseTaskService.selectTbAnalyseTaskById(taskId);
        if (analyseTask == null) {
            throw new ServiceException("任务不存在");
        }
        // 根据简历评分更新优秀和良好数量
        if (dto.getTotalScore() != null) {
            if (dto.getTotalScore() >= 80) {
                // 评分大于等于80，优秀
                if (analyseTask.getResumeExcellentCount() == null) {
                    analyseTask.setResumeExcellentCount(1);
                } else {
                    analyseTask.setResumeExcellentCount(analyseTask.getResumeExcellentCount() + 1);
                }
            } else {
                // 评分小于80，良好
                if (analyseTask.getResumeGoodCount() == null) {
                    analyseTask.setResumeGoodCount(1);
                } else {
                    analyseTask.setResumeGoodCount(analyseTask.getResumeGoodCount() + 1);
                }
            }
        }
        dto.setRecruitmentChannel("简历解析");
        dto.setBaseId(analyseTask.getBaseId());
        analyseTaskService.updateTbAnalyseTask(analyseTask);
    }


    // 更新寻才任务
    private void updateTbTask(TbTaskPersonalInfoDto dto) {
        Long jobId = dto.getJobId();
        // 查询任务详情
        TbTaskPosting tbJobPosting = tbJobPostingService.selectTbTaskPostingById(jobId);
        if (tbJobPosting == null) {
            throw new ServiceException("任务不存在");
        }
        tbJobPosting.setFinishCount(tbJobPosting.getFinishCount() + 1);
        // 如果人数达标就更新为已完成
        if (Objects.equals(tbJobPosting.getFinishCount(), tbJobPosting.getScreeningCount())) {
            tbJobPosting.setStatus(JobPostingStatus.COMPLETED.getCode());
        }
        // 转为code存入数据库
        Map<String, String> channels = DictUtils.getDictByValueMap(SysDictDataEnum.RECRUITMENT_CHANNELS.getName());
        if (channels != null && tbJobPosting.getChannel() != null) {
            String channel = tbJobPosting.getChannel().toString();
            dto.setRecruitmentChannel(channels.get(channel));
        }
        dto.setBaseId(tbJobPosting.getBaseId());
        tbJobPostingService.updateTbTaskPosting(tbJobPosting);
    }

    /**
     * 对象转换
     */
    private Long convert(TbTaskPersonalInfoDto taskDto) {
        TbPersonalInfoDto dto = new TbPersonalInfoDto();
        BeanUtils.copyProperties(taskDto, dto);
        // 基本信息
        dto.setTaskId(taskDto.getJobId());
        dto.setEmploymentStatus("0");
        dto.setPersonalIdSource(taskDto.getSourceEnum().getCode());
        List<String> skillList = taskDto.getSkillList();
        String skill = StringUtils.join(skillList, ",");
        dto.setSkills(skill);
        dto.setBaseId(taskDto.getBaseId());
        dto.setRecruitmentChannel(taskDto.getRecruitmentChannel());
        dto.setAvatar(null);
        // 转换值
        Map<String, String> education = DictUtils.getDictCacheMap(SysDictDataEnum.EDUCATION.getName());
        if (education != null && taskDto.getEducation() != null) {
            dto.setEducation(education.get(taskDto.getEducation()));
        }
        Map<String, String> map = DictUtils.getDictCacheMap(SysDictDataEnum.SYS_USER_SEX.getName());
        if (map != null) {
            if (StringUtils.isEmpty(taskDto.getSex()) || map.get(taskDto.getSex()) == null) {
                dto.setSex("2");
            } else {
                dto.setSex(map.get(taskDto.getSex()));
            }
        } else {
            dto.setSex("2");
        }
        if (taskDto.getWorkStatus() == null) {
            dto.setOldWorkStatus("离职");
        } else {
            dto.setOldWorkStatus(taskDto.getWorkStatus());
        }


        List<TbEducationInfo> educationInfoList = new ArrayList<>();
        if (taskDto.getTbEducationInfoList() != null && !taskDto.getTbEducationInfoList().isEmpty()) {
            for (TbTaskEducationInfo edu : taskDto.getTbEducationInfoList()) {
                TbEducationInfo educationInfo = new TbEducationInfo();
                BeanUtils.copyProperties(edu, educationInfo);
                educationInfoList.add(educationInfo);
            }
        }
        // 公司信息
        List<TbWorkExperience> experienceList = new ArrayList<>();
        if (taskDto.getTbWorkExperienceList() != null && !taskDto.getTbWorkExperienceList().isEmpty()) {
            for (TbTaskWorkExperience experience : taskDto.getTbWorkExperienceList()) {
                TbWorkExperience workExperience = new TbWorkExperience();
                BeanUtils.copyProperties(experience, workExperience);
                experienceList.add(workExperience);
            }
        }
        // 项目经历
        List<TbProjectExperience> projectExperienceList = new ArrayList<>();
        if (taskDto.getTbProjectExperienceList() != null && !taskDto.getTbProjectExperienceList().isEmpty()) {
            for (TbTaskProjectExperience projectExp : taskDto.getTbProjectExperienceList()) {
                TbProjectExperience projectExperience = new TbProjectExperience();
                BeanUtils.copyProperties(projectExp, projectExperience);
                projectExperienceList.add(projectExperience);
            }
        }
        dto.setEducationInfoList(educationInfoList);
        dto.setWorkExperienceList(experienceList);
        dto.setProjectExperienceList(projectExperienceList);
        try {
            if (taskDto.getFileId() != null) {
                TbAnalysePersonalFile file = personalFileService.selectTbAnalysePersonalFileById(taskDto.getFileId());
                if (file != null) {
                    dto.setId(file.getPersonalId());
                    file.setPersonalId(dto.getId());
                    file.setStatus(ParseStatusEnum.SUCCESS.getCode());
                    personalFileService.updateTbAnalysePersonalFile(file);
                }
                // 更新任务状态和文件数量
                TbAnalyseFileStateCount count = personalFileService.selectCount(taskDto.getJobId());
                if (count != null) {
                    count.setId(taskDto.getJobId());
                    analyseTaskService.updateTaskCount(count);
                }
            }
            tbPersonalInfoService.insertTbPersonalInfoDto(dto);
            // 新增评价
            TbpResumeEvaluate tbpResumeEvaluate = new TbpResumeEvaluate();
            BeanUtils.copyProperties(taskDto, tbpResumeEvaluate);
            tbpResumeEvaluate.setComprehensiveEvaluation(taskDto.getComprehensiveEvaluation());
            tbpResumeEvaluate.setTalentId(dto.getId());
            tbpResumeEvaluateService.insertTbpResumeEvaluate(tbpResumeEvaluate);
            return dto.getId();
        } catch (Exception e) {
            logger.error("个人信息入库失败", e);
        }
        return null;
    }

    @GetMapping("/getTask/{count}")
    public AjaxResult getTask(@PathVariable Integer count) {
        TbAnalysePersonalFile personalFile = new TbAnalysePersonalFile();
        personalFile.setStatus(ParseStatusEnum.PARSING.getCode());
        startPage(1,count);
        List<TbAnalysePersonalFile> files = personalFileService.selectTbAnalysePersonalFileList(personalFile);
        List<TbAnalyseFileVo> voList = new ArrayList<>();
        if (files != null && !files.isEmpty()) {
            for (TbAnalysePersonalFile file : files) {
                TbAnalyseFileVo vo = new TbAnalyseFileVo();
                vo.setId(file.getId());
                vo.setTaskId(file.getTaskId());
                vo.setFileUrl(file.getFileUrl());
                voList.add(vo);
            }   
        }
        return AjaxResult.success(voList);
    }


}
