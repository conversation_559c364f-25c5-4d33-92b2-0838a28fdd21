package com.ruoyi.talent.controller;

import com.ruoyi.common.core.utils.PinyinUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.talent.TbAnalysePersonalFile;
import com.ruoyi.common.entity.domain.talent.TbAnalyseTask;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoDto;
import com.ruoyi.talentbase.domain.enums.ParseStatusEnum;
import com.ruoyi.talentbase.domain.enums.PersonalIdSourceEnum;
import com.ruoyi.talentbase.domain.vo.TbAnalyseFileStateCount;
import com.ruoyi.talentbase.service.AsyncParseService;
import com.ruoyi.talentbase.service.ITbAnalysePersonalFileService;
import com.ruoyi.talentbase.service.ITbAnalyseTaskService;
import com.ruoyi.talentbase.service.ITbPersonalInfoService;
import com.ruoyi.talentbase.utils.TalentAgentApiUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/talentbase/analysePersonalFile")
public class TbAnalysePersonalFileController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(TbAnalysePersonalFileController.class);

    @Autowired
    private ITbAnalysePersonalFileService service;

    @Autowired
    private TalentAgentApiUtil talentAgentApiUtil;

    @Autowired
    private ITbAnalyseTaskService analyseTaskService;

    @Autowired
    private AsyncParseService asyncParseService;

    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;

    @GetMapping("/list")
    public TableDataInfo list(TbAnalysePersonalFile file) {
        startPage();
        List<TbAnalysePersonalFile> list = service.selectTbAnalysePersonalFileList(file);
        return getDataTable(list);
    }

    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(service.selectTbAnalysePersonalFileById(id));
    }

    @Log(title = "个人档案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbAnalysePersonalFile file) {
        return toAjax(service.insertTbAnalysePersonalFile(file));
    }

    @Log(title = "个人档案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbAnalysePersonalFile file) {
        return toAjax(service.updateTbAnalysePersonalFile(file));
    }

    @Log(title = "个人档案", businessType = BusinessType.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        // 检查是否所有文件都是分析失败状态
        List<Long> allowedIds = new ArrayList<>();
        List<Long> notAllowedIds = new ArrayList<>();

        // 用于统计需要更新的任务
        Map<Long, Integer> taskFailCountMap = new HashMap<>();
        Map<Long, Integer> taskTotalCountMap = new HashMap<>();
        // 创建一个list存储id
        List<Long> tpIds = new ArrayList<>();
        // 创建一个set集合，用于存储任务id
        Set<Long> taskIds = new HashSet<>();
        for (Long id : ids) {
            TbAnalysePersonalFile file = service.selectTbAnalysePersonalFileById(id);
            if (file == null) {
                notAllowedIds.add(id);
                continue;
            }
            // 加入到list中
            tpIds.add(file.getPersonalId());
            // 加入到set集合中
            taskIds.add(file.getTaskId());
            // 只允许删除分析失败的文件
            if (ParseStatusEnum.FAIL.getCode().equals(file.getStatus())) {
                allowedIds.add(id);
                // 统计任务ID对应的失败数量和总数
                Long taskId = file.getTaskId();
                if (taskId != null) {
                    taskFailCountMap.put(taskId, taskFailCountMap.getOrDefault(taskId, 0) + 1);
                    taskTotalCountMap.put(taskId, taskTotalCountMap.getOrDefault(taskId, 0) + 1);
                }
            } else {
                notAllowedIds.add(id);
                log.warn("尝试删除非失败状态的文件，ID: {}, 状态: {}", id, file.getStatus());
            }
        }

        // 如果有不允许删除的文件，返回错误信息
        if (!notAllowedIds.isEmpty()) {
            String errorMsg = "以下文件不允许删除（只允许删除分析失败的文件）：" + notAllowedIds;
            if (!allowedIds.isEmpty()) {
                errorMsg += "；允许删除的文件ID：" + allowedIds;
            }
            return AjaxResult.error(errorMsg);
        }

        // 执行删除操作
        if (allowedIds.isEmpty()) {
            return AjaxResult.error("没有可删除的文件");
        }

        Long[] allowedIdsArray = allowedIds.toArray(new Long[0]);
        int deleteResult = service.deleteTbAnalysePersonalFileByIds(allowedIdsArray);
        // 转数组
        tbPersonalInfoService.deleteTbPersonalInfoByIds(tpIds.toArray(new Long[0]));
        // 更新任务的统计数量
        if (deleteResult > 0) {
            // 遍历任务ID，更新任务状态和文件数量
            for (Long taskId : taskIds) {
                // 更新任务状态和文件数量
                TbAnalyseFileStateCount count = service.selectCount(taskId);
                if (count != null) {
                    count.setId(taskId);
                    analyseTaskService.updateTaskCount(count);
                }
            }
        }

        return toAjax(deleteResult);
    }

    @Log(title = "个人档案", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestBody List<TbAnalysePersonalFile> files) {
        int success = 0;
        List<TbAnalysePersonalFile> list = files.stream().filter(f -> f.getTaskId() != null).collect(Collectors.toList());
        if (list.isEmpty()) {
            return AjaxResult.error("请选择任务");
        }
        // 检查任务是否存在
        Long taskId = files.get(0).getTaskId();
        TbAnalyseTask task = analyseTaskService.selectTbAnalyseTaskById(taskId);
        if (task == null) {
            return AjaxResult.error("任务不存在");
        }

        for (TbAnalysePersonalFile f : files) {
            // 设置解析状态为解析中
            if (f.getStatus() == null) {
                f.setStatus(ParseStatusEnum.PARSING.getCode());
            }
            // 生成文件名拼音
            if (StringUtils.isNotEmpty(f.getFileName())) {
                String pinyin = PinyinUtils.getPinyin(f.getFileName());
                f.setPinyin(pinyin);
            }
            // 新增人才库信息
            TbPersonalInfoDto info = new TbPersonalInfoDto();
            info.setTaskId(taskId);
            info.setUserName(f.getFileName());
            info.setPinyin(f.getPinyin());
            info.setPersonalIdSource(PersonalIdSourceEnum.RESUME_POOL.getCode());
            info.setBaseId(task.getBaseId());
            tbPersonalInfoService.insertTbPersonalInfoDto(info);
            // 设置人才库ID
            f.setPersonalId(info.getId());
            // 增加完人才后在增加简历
            service.insertTbAnalysePersonalFile(f);
        }

        TbAnalyseFileStateCount count = service.selectCount(taskId);
        if (count != null) {
            count.setId(taskId);
            analyseTaskService.updateTaskCount(count);
        }

        // 异步调用解析接口 - 使用专门的异步服务
        for (TbAnalysePersonalFile f : files) {
            try {
                asyncParseService.asyncParseTalent(f.getFileUrl(), f.getTaskId(), f.getId());
            } catch (Exception ex) {
                log.error("异步调用简历解析接口异常", ex);
            }
        }

        return AjaxResult.success("已添加" + success + "条记录");
    }

    @Log(title = "个人档案", businessType = BusinessType.UPDATE)
    @PostMapping("/retry/{ids}")
    public AjaxResult retry(@PathVariable Long[] ids) {
        // 提取文件信息
        List<TbAnalysePersonalFile> files = new ArrayList<>();
        // 判断
        for (Long id : ids) {
            TbAnalysePersonalFile file = service.selectTbAnalysePersonalFileById(id);
            if (file == null) {
                return AjaxResult.error("文件不存在");
            }
            if (!file.getStatus().equals(ParseStatusEnum.FAIL.getCode())) {
                return AjaxResult.error("文件中含有状态不是失败状态，不能重试");
            }
            files.add(file);
        }
        // 更新
        service.batchUpdateStatus(Arrays.asList(ids), ParseStatusEnum.PARSING.getCode());
        // 定义一个set，用于存储任务ID
        Set<Long> taskIds = new HashSet<>();
        // 调用知识库接口触发解析（异步执行） - 使用专门的异步服务
        files.forEach(file -> {
            asyncParseService.asyncParseTalent(file.getFileUrl(), file.getTaskId(), file.getId());
            // 统计
            taskIds.add(file.getTaskId());
        });
        // 统计
        for (Long taskId : taskIds) {
            TbAnalyseFileStateCount count = service.selectCount(taskId);
            if (count != null) {
                count.setId(taskId);
                analyseTaskService.updateTaskCount(count);
            }
        }
        return AjaxResult.success("批量重试解析成功，共重试 " + ids.length + " 个文件");
    }

    @Log(title = "个人档案", businessType = BusinessType.UPDATE)
    @PostMapping("/retryByTask/{taskId}")
    public AjaxResult retryByTask(@PathVariable Long taskId) {
        log.info("开始处理任务重试请求 - taskId: {}", taskId);

        // 查询任务是否存在
        TbAnalyseTask task = analyseTaskService.selectTbAnalyseTaskById(taskId);
        if (task == null) {
            log.error("解析任务不存在 - taskId: {}", taskId);
            return AjaxResult.error("解析任务不存在，ID: " + taskId);
        }
        log.info("任务验证通过 - taskId: {}, taskName: {}", taskId, task.getTaskName());

        // 查询该任务下所有解析失败的简历
        TbAnalysePersonalFile queryFile = new TbAnalysePersonalFile();
        queryFile.setTaskId(taskId);
        queryFile.setStatus(ParseStatusEnum.FAIL.getCode());
        log.info("开始查询失败文件 - taskId: {}, status: {}", taskId, ParseStatusEnum.FAIL.getCode());

        List<TbAnalysePersonalFile> failedFiles = service.selectTbAnalysePersonalFileList(queryFile);
        log.info("查询失败文件完成 - taskId: {}, 失败文件数量: {}", taskId, failedFiles != null ? failedFiles.size() : 0);

        if (failedFiles == null || failedFiles.isEmpty()) {
            log.info("该任务下没有解析失败的简历 - taskId: {}", taskId);
            return AjaxResult.success("该任务下没有解析失败的简历");
        }

        log.info("准备启动异步处理 - taskId: {}, 文件数量: {}", taskId, failedFiles.size());

        // 异步处理批量重试，避免前端超时 - 使用专门的异步服务
        asyncParseService.asyncRetryByTask(taskId, failedFiles);

        log.info("异步处理已启动，立即返回响应 - taskId: {}", taskId);
        return AjaxResult.success("任务重试解析已启动，共处理 " + failedFiles.size() + " 个文件");
    }

    /**
     * 批量更新文件名拼音数据
     */
    @Log(title = "个人档案", businessType = BusinessType.UPDATE)
    @PostMapping("/updatePinyin")
    public AjaxResult updatePinyin() {
        try {
            // 查询所有没有拼音的文件数据
            TbAnalysePersonalFile query = new TbAnalysePersonalFile();
            List<TbAnalysePersonalFile> list = service.selectTbAnalysePersonalFileList(query);

            int updateCount = 0;
            for (TbAnalysePersonalFile file : list) {
                if (StringUtils.isNotEmpty(file.getFileName()) && StringUtils.isEmpty(file.getPinyin())) {
                    String pinyin = PinyinUtils.getPinyin(file.getFileName());
                    file.setPinyin(pinyin);
                    service.updateTbAnalysePersonalFile(file);
                    updateCount++;
                }
            }

            return AjaxResult.success("成功更新 " + updateCount + " 条文件的拼音数据");
        } catch (Exception e) {
            log.error("批量更新文件名拼音失败", e);
            return AjaxResult.error("批量更新文件名拼音失败：" + e.getMessage());
        }
    }
} 