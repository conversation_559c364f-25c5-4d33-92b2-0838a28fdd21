package com.ruoyi.talent.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.talentbase.domain.TbWorkExperience;
import com.ruoyi.talentbase.service.ITbWorkExperienceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 工作经历Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/talentbase/experience")
public class TbWorkExperienceController extends BaseController
{
    @Autowired
    private ITbWorkExperienceService tbWorkExperienceService;

    /**
     * 查询工作经历列表
     */
    @RequiresPermissions("talent:experience:list")
    @GetMapping("/list")
    public TableDataInfo list(TbWorkExperience tbWorkExperience)
    {
        startPage();
        List<TbWorkExperience> list = tbWorkExperienceService.selectTbWorkExperienceList(tbWorkExperience);
        return getDataTable(list);
    }

    /**
     * 导出工作经历列表
     */
    @RequiresPermissions("talent:experience:export")
    @Log(title = "工作经历", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbWorkExperience tbWorkExperience)
    {
        List<TbWorkExperience> list = tbWorkExperienceService.selectTbWorkExperienceList(tbWorkExperience);
        ExcelUtil<TbWorkExperience> util = new ExcelUtil<TbWorkExperience>(TbWorkExperience.class);
        util.exportExcel(response, list, "工作经历数据");
    }

    /**
     * 获取工作经历详细信息
     */
    @RequiresPermissions("talent:experience:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tbWorkExperienceService.selectTbWorkExperienceById(id));
    }

    /**
     * 新增工作经历
     */
    @RequiresPermissions("talent:experience:add")
    @Log(title = "工作经历", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbWorkExperience tbWorkExperience)
    {
        return toAjax(tbWorkExperienceService.insertTbWorkExperience(tbWorkExperience));
    }

    /**
     * 修改工作经历
     */
    @RequiresPermissions("talent:experience:edit")
    @Log(title = "工作经历", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbWorkExperience tbWorkExperience)
    {
        return toAjax(tbWorkExperienceService.updateTbWorkExperience(tbWorkExperience));
    }

    /**
     * 删除工作经历
     */
    @RequiresPermissions("talent:experience:remove")
    @Log(title = "工作经历", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbWorkExperienceService.deleteTbWorkExperienceByIds(ids));
    }
}
