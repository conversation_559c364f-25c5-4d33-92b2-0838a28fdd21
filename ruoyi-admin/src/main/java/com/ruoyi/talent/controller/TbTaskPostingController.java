package com.ruoyi.talent.controller;

import com.ruoyi.common.core.enums.JobPostingStatus;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.system.LoginUser;
import com.ruoyi.common.entity.domain.talent.TbTaskPosting;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.talentbase.domain.TbpPositionBase;
import com.ruoyi.talentbase.domain.dto.TbTPostingEditStatusDto;
import com.ruoyi.talentbase.service.ITbTaskPostingService;
import com.ruoyi.talentbase.service.ITbpPositionBaseService;
import com.ruoyi.talentbase.utils.TPInfoUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 查找人才任务Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/talentbase/posting")
public class TbTaskPostingController extends BaseController {
    @Autowired
    private ITbTaskPostingService tbJobPostingService;
    @Autowired
    private ITbpPositionBaseService positionBaseService;

    /**
     * 查询查找人才任务列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbTaskPosting tbJobPosting) {
        startPage();
        List<TbTaskPosting> list = tbJobPostingService.selectTbTaskPostingList(tbJobPosting);
        return getDataTable(list);
    }

    /**
     * 获取查找人才任务详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TbTaskPosting tbJobPosting = tbJobPostingService.selectTbTaskPostingById(id);
        if (tbJobPosting == null) {
            return AjaxResult.error("任务不存在");
        }
        tbJobPosting.makeSearchCondition();
        if (Objects.equals(tbJobPosting.getStatus(), JobPostingStatus.COMPLETED.getCode()) ||
                Objects.equals(tbJobPosting.getStatus(), JobPostingStatus.FAILED.getCode())) {
            // 如果是成功或者失败时就删除待办
            TPInfoUtils.removeFindTalentTask(tbJobPosting.getId());
        }
        TbpPositionBase tbpPositionBase = positionBaseService.selectTbpPositionBaseById(tbJobPosting.getBaseId());
        if(tbpPositionBase != null){
            tbJobPosting.setOpenFlag(tbpPositionBase.getOpenFlag());
        }
        return AjaxResult.success(tbJobPosting);
    }

    /**
     * 新增查找人才任务
     */
    @Log(title = "查找人才任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbTaskPosting tbJobPosting) {
        if (tbJobPosting == null || tbJobPosting.getStatus() == null) {
            return AjaxResult.error("新增失败，请检查参数");
        }
        if (tbJobPosting.getBaseId() == null) {
            return AjaxResult.error("请选择职位库");
        }
        TbpPositionBase positionBase = positionBaseService.selectTbpPositionBaseById(tbJobPosting.getBaseId());
        if (positionBase == null) {
            return AjaxResult.error("职位库不存在");
        }

        TbTaskPosting query = new TbTaskPosting();
        query.setBaseId(tbJobPosting.getBaseId());
        int count = tbJobPostingService.selectTbTaskPostingCount(query);
        tbJobPosting.setTaskName(positionBase.getName() + "寻才任务" + (count + 1));
        Integer status = tbJobPosting.getStatus();
        if (status.equals(JobPostingStatus.PROCESSING.getCode())) {
            tbJobPosting.setStartTime(new Date());
        }
        if (tbJobPosting.getPositionName() == null) {
            tbJobPosting.setPositionName(positionBase.getName());
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        tbJobPosting.setCreateBy(loginUser.getUser().getNickName());
        tbJobPosting.setUpdateBy(SecurityUtils.getNickname());
        tbJobPosting.setUserId(loginUser.getUserId());
        int row = tbJobPostingService.insertTbTaskPosting(tbJobPosting);
        if (row > 0) {
            return AjaxResult.success(tbJobPosting);
        }
        return AjaxResult.error();
    }

    /**
     * 修改查找人才任务
     */
    @Log(title = "查找人才任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbTaskPosting tbJobPosting) {
        if (!Objects.equals(tbJobPosting.getStatus(), JobPostingStatus.DRAFT.getCode())
                && !Objects.equals(tbJobPosting.getStatus(), JobPostingStatus.PAUSED.getCode())) {
            return AjaxResult.error("任务状态不是草稿和暂停不允许更新");
        }
        tbJobPosting.setUpdateBy(SecurityUtils.getNickname());
        int row = tbJobPostingService.updateTbTaskPosting(tbJobPosting);
        if (row > 0) {
            return AjaxResult.success(tbJobPosting);
        }
        return AjaxResult.error();
    }

    /**
     * 删除查找人才任务
     */
    @Log(title = "查找人才任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        int row = tbJobPostingService.deleteTbTaskPostingByIds(ids);
        if (row > 0) {
            for (Long id : ids) {
                TPInfoUtils.removeFindTalentTask(id);
            }
        }
        return toAjax(row);
    }

    /**
     * 修改查找人才任务状态
     */
    @GetMapping("/editStatus")
    public AjaxResult editStatus(TbTPostingEditStatusDto dto) {
        if (dto == null || dto.getId() == null || dto.getStatus() == null) {
            return AjaxResult.error("请求参数异常");
        }
        TbTaskPosting tbJobPosting = tbJobPostingService.selectTbTaskPostingById(dto.getId());
        if (tbJobPosting == null) {
            return AjaxResult.error("当前任务不存在");
        }
        if (tbJobPosting.getStatus().equals(dto.getStatus())) {
            logger.info("任务状态已一致");
            return AjaxResult.success();
        }
        TbTaskPosting jobPosting = new TbTaskPosting();
        jobPosting.setId(dto.getId());
        jobPosting.setUpdateTime(new Date());
        jobPosting.setUpdateBy(SecurityUtils.getNickname());
        jobPosting.setStatus(dto.getStatus());
        jobPosting.setUserId(tbJobPosting.getUserId());
        jobPosting.setPositionName(tbJobPosting.getPositionName());
        jobPosting.setTaskName(tbJobPosting.getTaskName());
        jobPosting.setFailureReason(dto.getFailureReason());
        if (Objects.equals(dto.getStatus(), JobPostingStatus.PROCESSING.getCode())) {
            jobPosting.setStartTime(new Date());
        }
        // 增加待办数据
        TPInfoUtils.addFindTalentTask(jobPosting);
        return toAjax(tbJobPostingService.updateTbTaskPostingStatus(jobPosting));
    }

    /**
     * 修改查找人才任务状态
     */
    @GetMapping("/checkJobStatus")
    public AjaxResult checkJobStatus(TbTaskPosting tbJobPosting) {
        tbJobPosting.setStatus(JobPostingStatus.PROCESSING.getCode());
        int count = tbJobPostingService.selectTbTaskPostingCount(tbJobPosting);
        if (count > 0) {
            return AjaxResult.error("当前存有正在进行中的任务");
        }
        return AjaxResult.success();
    }

    /**
     * 重新开始任务
     */
    @GetMapping("/reStartJob")
    public AjaxResult reStartJob(@RequestParam Long id, @RequestParam String taskName) {
        TbTaskPosting tbJobPosting = tbJobPostingService.selectTbTaskPostingById(id);
        if (tbJobPosting == null) {
            return AjaxResult.error("任务不存在");
        }
        tbJobPosting.setTaskName(taskName);
        tbJobPosting.setStatus(1);
        tbJobPosting.setFinishCount(0L);
        tbJobPosting.setId(null);
        tbJobPosting.setCreateBy(SecurityUtils.getNickname());
        tbJobPosting.setUpdateBy(SecurityUtils.getNickname());
        int row = tbJobPostingService.insertTbTaskPosting(tbJobPosting);
        if (row > 0) {
            return AjaxResult.success(tbJobPosting);
        }
        return AjaxResult.error();
    }


}
