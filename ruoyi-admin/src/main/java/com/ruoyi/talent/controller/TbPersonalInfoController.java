package com.ruoyi.talent.controller;

import com.ruoyi.common.core.enums.DelFlagStatus;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.talentbase.domain.TbAnalysePersonalInfo;
import com.ruoyi.talentbase.domain.TbPersonalInfo;
import com.ruoyi.talentbase.domain.TbTaskPersonalInfo;
import com.ruoyi.talentbase.domain.TbpPositionBase;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoDto;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoParamDto;
import com.ruoyi.talentbase.domain.dto.TbPersonalSelectedDto;
import com.ruoyi.talentbase.domain.enums.*;
import com.ruoyi.talentbase.domain.vo.ResumeListVO;
import com.ruoyi.talentbase.domain.vo.TbPersonalGetInfoVo;
import com.ruoyi.talentbase.domain.vo.TbPersonalInfoVo;
import com.ruoyi.talentbase.domain.vo.TbPersonalTopicCount;
import com.ruoyi.talentbase.service.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 个人简历信息Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/talentbase/personalInfo")
public class TbPersonalInfoController extends BaseController {
    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;
    @Autowired
    private ITbOnlineInterviewService tbOnlineInterviewService;
    @Autowired
    private ITbOfflineInterviewService tbOfflineInterviewService;
    @Autowired
    private ITbTaskPersonalInfoService tbTaskPersonalInfoService;
    @Autowired
    private ITbAnalysePersonalInfoService analysePersonalInfoService;
    @Autowired
    private ITbUserInfoService tbUserInfoService;
    @Autowired
    private ITbUserUndergoService tbUserUndergoService;
    @Autowired
    private ITbAnalysePersonalFileService analysePersonalFileService;
    @Autowired
    private ITbpPositionBaseService positionBaseService;
    @Autowired
    private ITbpResumeEvaluateService tbpResumeEvaluateService;


    // 手动录入
    private static final String MANUAL_ENTRY = "手动录入";

    /**
     * 查询个人简历信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbPersonalInfo tbPersonalInfo) {
        startPage();
        tbPersonalInfo.setDelFlag(Integer.valueOf(DelFlagStatus.OK.getCode()));
        tbPersonalInfo.setStorageDisplay(StorageDisplayEnum.STORAGE_DISPLAY.getCode());
        List<TbPersonalInfo> list = tbPersonalInfoService.selectTbPersonalInfoList(tbPersonalInfo);

        // 优化面试次数统计逻辑
        for (TbPersonalInfo personalInfo : list) {
            Long infoId = personalInfo.getId();
            if (infoId != null) {
                // 人才库中的人员，面试来源类型为0（人才库）
                int onlineCount = tbOnlineInterviewService.countActiveAndCompletedInterviews(infoId, 0);
                int offlineCount = tbOfflineInterviewService.countActiveAndCompletedInterviews(infoId, 0);
                // 设置总面试次数
                personalInfo.setInterviewCount(onlineCount + offlineCount);
            } else {
                personalInfo.setInterviewCount(0);
            }
        }
        return getDataTable(list);
    }

    @PostMapping("/getList")
    public TableDataInfo getList(@RequestBody TbPersonalInfoParamDto infoDto) {
        startPage();
        infoDto.setParseStatus(ParseStatusEnum.SUCCESS.getCode());
        List<TbPersonalInfoVo> list = tbPersonalInfoService.selectTbPersonalInfoVoList(infoDto);
        List<TbPersonalSelectedDto> selectedList = infoDto.getSelectedList();
        if (selectedList != null && !selectedList.isEmpty()) {
            List<TbPersonalSelectedDto> dtoList = selectedList.stream().filter(s -> s.getType() != 0).collect(Collectors.toList());
            // 拼接id和type
            List<String> ids = dtoList.stream().map(s -> s.getId() + "_" + s.getType()).collect(Collectors.toList());
            // 判断是否存在dtoList中
            list.forEach(item -> {
                if (ids.contains(item.getTaskPersonalId() + "_" + item.getPersonalIdSource())) {
                    item.setSelected(1);
                } else {
                    item.setSelected(0);
                }
            });
        } else {
            list.forEach(item -> item.setSelected(0));
        }
        return getDataTable(list);
    }

    /**
     * 导出个人简历信息列表
     */
    @Log(title = "个人简历信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbPersonalInfo tbPersonalInfo) {
        List<TbPersonalInfo> list = tbPersonalInfoService.selectTbPersonalInfoList(tbPersonalInfo);
        ExcelUtil<TbPersonalInfo> util = new ExcelUtil<TbPersonalInfo>(TbPersonalInfo.class);
        util.exportExcel(response, list, "个人简历信息数据");
    }

    /**
     * 获取个人简历信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TbPersonalGetInfoVo infoVo = tbPersonalInfoService.selectTbPersonalInfoVoById(id);
        TbpPositionBase tbpPositionBase = positionBaseService.selectTbpPositionBaseById(infoVo.getBaseId());
        if(tbpPositionBase != null){
            infoVo.setOpenFlag(tbpPositionBase.getOpenFlag());
        }
        return success(infoVo);
    }

    /**
     * 新增个人简历信息
     */
    @Log(title = "个人简历信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbPersonalInfoDto infoDto) {
        infoDto.setRecruitmentChannel(MANUAL_ENTRY);
        infoDto.setPersonalIdSource(PersonalIdSourceEnum.RECRUIT_POOL.getCode());
        int row = tbPersonalInfoService.insertTbPersonalInfoDto(infoDto);
        if (row > 0) {
            TbPersonalGetInfoVo getInfoVo = tbPersonalInfoService.selectTbPersonalInfoVoById(infoDto.getId());
            // 生成评价
            tbpResumeEvaluateService.reEvaluate(infoDto.getId());
            return success(getInfoVo);
        }
        return AjaxResult.error("添加信息失败");
    }

    // 基础信息更新
    @ApiOperation(tags = "个人简历信息", value = "基础信息更新")
    @PostMapping("/editBase")
    public AjaxResult editBase(@RequestBody TbPersonalInfo tbPersonalInfo) {
        return AjaxResult.success(tbPersonalInfoService.updateTbPersonalInfo(tbPersonalInfo));
    }

    // 教育信息更新
    @ApiOperation(tags = "个人简历信息", value = "教育信息更新")
    @PostMapping("/editEducation")
    public AjaxResult editEducation(@RequestBody TbPersonalInfoDto infoDto) {
        return AjaxResult.success(tbPersonalInfoService.editEducation(infoDto));
    }

    // 工作信息更新
    @ApiOperation(tags = "个人简历信息", value = "工作信息更新")
    @PostMapping("/editWork")
    public AjaxResult editWork(@RequestBody TbPersonalInfoDto infoDto) {
        return AjaxResult.success(tbPersonalInfoService.editWork(infoDto));
    }

    // 家庭信息更新
    @ApiOperation(tags = "个人简历信息", value = "家庭信息更新")
    @PostMapping("/editFamily")
    public AjaxResult editFamily(@RequestBody TbPersonalInfoDto infoDto) {
        return AjaxResult.success(tbPersonalInfoService.updateFamily(infoDto));
    }

    // 技能证书更新
    @ApiOperation(tags = "个人简历信息", value = "技能证书更新")
    @PostMapping("/editSkill")
    public AjaxResult editSkill(@RequestBody TbPersonalInfoDto infoDto) {
        return AjaxResult.success(tbPersonalInfoService.editSkill(infoDto));
    }

    // 项目经历更新
    @ApiOperation(tags = "个人简历信息", value = "项目经历更新")
    @PostMapping("/editProject")
    public AjaxResult editProject(@RequestBody TbPersonalInfoDto infoDto) {
        return AjaxResult.success(tbPersonalInfoService.editProject(infoDto));
    }

    @ApiOperation(tags = "个人简历信息", value = "查询是否可以切换")
    @GetMapping("/canSwitch/{id}/{type}")
    public AjaxResult canSwitch(@PathVariable Long id, @PathVariable Integer type) {
        PersonalIdSourceEnum code = PersonalIdSourceEnum.getByCode(type);
        if (code == null) {
            return AjaxResult.error("参数错误");
        }
        Map<String, Object> map = new HashMap<>();
        if (code == PersonalIdSourceEnum.RECRUIT_POOL) {
            // 查询人才库关联信息
            TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(id);
            if (personalInfo != null && personalInfo.getTaskPersonalId() != null) {
                map.put("id", personalInfo.getTaskPersonalId());
                map.put("type", personalInfo.getPersonalIdSource());
                return AjaxResult.success(map);
            }
        } else {
            TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoByTaskPidAndSource(id, code.getCode());
            if (personalInfo == null ||
                    (personalInfo.getStorageDisplay().equals(StorageDisplayEnum.NOT_STORAGE_DISPLAY.getCode())
                            && personalInfo.getFiledFlag().equals(FiledFlagEnum.NOT_FILED_FLAG.getCode()))
            ) {
                return AjaxResult.success();

            }
            map.put("id", personalInfo.getId());
            map.put("type", PersonalIdSourceEnum.RECRUIT_POOL.getCode());
            return AjaxResult.success(map);
        }
        return AjaxResult.success();
    }

    /**
     * 更新个人简历附件
     */
    @ApiOperation(tags = "个人简历信息", value = "更新个人简历附件")
    @PostMapping("/editAttach")
    public AjaxResult editAttach(@RequestBody TbPersonalInfo info) {
        if (info.getResumeFile() == null) {
            return AjaxResult.error("请上传附件");
        }
        if (info.getId() == null) {
            return AjaxResult.error("请指定上传的人才信息");
        }
        TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(info.getId());
        if (personalInfo == null) {
            return AjaxResult.error("查询失败,未能获取到相关信息");
        }
        personalInfo.setResumeFile(info.getResumeFile());
        int row = tbPersonalInfoService.updateTbPersonalInfo(personalInfo);
        return toAjax(row);
    }


    /**
     * 修改个人简历信息
     */
    @Log(title = "个人简历信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbPersonalInfo tbPersonalInfo) {
        TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(tbPersonalInfo.getId());
        if (personalInfo == null) {
            return AjaxResult.error("查询失败,未能获取到相关信息");
        }
        return toAjax(tbPersonalInfoService.updateTbPersonalInfo(tbPersonalInfo));
    }

    /**
     * 删除个人简历信息
     */
    @Log(title = "个人简历信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult remove(@PathVariable Long[] ids) {
        // 更新标识的ids
        List<Long> idList = new ArrayList<>();
        for (Long id : ids) {
            TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(id);
            if (personalInfo == null) {
                throw new RuntimeException("查询失败,未能获取到相关信息");
            }

            // 根据来源类型处理不同的逻辑
            if (personalInfo.getTaskPersonalId() != null && personalInfo.getPersonalIdSource() != null) {
                if (personalInfo.getPersonalIdSource().equals(PersonalIdSourceEnum.TASK_POOL.getCode())) {
                    // 寻才库来源
                    TbTaskPersonalInfo taskPersonalInfo = new TbTaskPersonalInfo();
                    taskPersonalInfo.setId(personalInfo.getTaskPersonalId());
                    taskPersonalInfo.setTalentPoolStatus(0);
                    tbTaskPersonalInfoService.updateById(taskPersonalInfo);
                } else if (personalInfo.getPersonalIdSource().equals(PersonalIdSourceEnum.RESUME_POOL.getCode())) {
                    // 简历库来源
                    TbAnalysePersonalInfo analysePersonalInfo = new TbAnalysePersonalInfo();
                    analysePersonalInfo.setId(personalInfo.getTaskPersonalId());
                    analysePersonalInfo.setTalentPoolStatus(0);
                    analysePersonalInfoService.updateTbAnalysePersonalInfo(analysePersonalInfo);
                }
            }
            if (personalInfo.getFiledFlag().equals(FiledFlagEnum.NOT_FILED_FLAG.getCode())) {
                // 如果未建档 直接删除所有信息
                deleteData(personalInfo, true);
                continue;
            }
            idList.add(id);
        }
        TbPersonalInfo personalInfo = new TbPersonalInfo();
        personalInfo.setStorageDisplay(1);
        // 转数组 更新移除人才数据
        if (!idList.isEmpty()) {
            return toAjax(tbPersonalInfoService.updateFlagByIds(personalInfo, idList.toArray(new Long[0])));
        }
        return AjaxResult.success();
    }

    /**
     * 档案入库
     *
     * @param id
     * @return
     */
    @GetMapping("/addTalentPool/{id}")
    public AjaxResult addTalentPool(@PathVariable Long id) {
        TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(id);
        if (personalInfo == null) {
            return AjaxResult.error("查询失败,未能获取到相关信息");
        }
        personalInfo.setStorageDisplay(StorageDisplayEnum.STORAGE_DISPLAY.getCode());
        personalInfo.setCreateTime(DateUtils.getNowDate());
        tbPersonalInfoService.updateFlagById(personalInfo);
        return AjaxResult.success();
    }

    // 每年年初所有人年岁+1
    @Scheduled(cron = "0 0 0 1 1 ?")
    public void updateAge() {
        // 所有人年龄+1岁
        tbPersonalInfoService.updateAge();
    }


    /**
     * 删除人才库未入库未建档的数据
     *
     * @param isDelInterview 是否删除线下面试和线上面试数据
     *                       待确认在什么时候调用这个方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteData(boolean isDelInterview) {
        try {
            // 未入库未建档的数据
            TbPersonalInfo personalInfo = new TbPersonalInfo();
            personalInfo.setStorageDisplay(StorageDisplayEnum.NOT_STORAGE_DISPLAY.getCode());
            personalInfo.setFiledFlag(FiledFlagEnum.NOT_FILED_FLAG.getCode());
            List<TbPersonalInfo> list = tbPersonalInfoService.selectList(personalInfo);
            for (TbPersonalInfo info : list) {
                deleteData(info, isDelInterview);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("删除失败");
        }
    }

    private void deleteData(TbPersonalInfo info, boolean isDelInterview) {
        // 删除人才库相关数据
        tbPersonalInfoService.removeAll(info.getId());
        // 删除档案相关数据
        tbUserInfoService.deleteTbUserInfoByPersonalIds(new Long[]{info.getId()});
        // 删除履历相关数据
        tbUserUndergoService.deleteByPInfoId(info.getId());
        if (isDelInterview) {
            // 删除线下面试相关数据
            tbOfflineInterviewService.deleteByPInfoId(info.getId(), TalentSourceEnum.TALENT_POOL);
            // 删除线上面试相关数据
            tbOnlineInterviewService.deleteByPInfoId(info.getId(), TalentSourceEnum.TALENT_POOL);
        }
    }


    /**
     * 职位库人才列表
     */
    @GetMapping("tpList")
    public TableDataInfo tpList(TbPersonalInfoParamDto infoDto) {
        startPage();
        // 只能查看合适的人才
        infoDto.workExConvert();
        if (infoDto.getSuitableFlag() == null) {
            infoDto.setSuitableFlag(0);
        }
        List<ResumeListVO> list = tbPersonalInfoService.selectVOList(infoDto);
        return getDataTable(list);
    }

    /**
     * 职位库人才统计
     */
    @GetMapping("tpCount")
    public AjaxResult tpCount(TbPersonalInfoParamDto infoDto) {
        infoDto.setAttentionFlag(null);
        infoDto.setSuitableFlag(null);

        // 关注和未关注的人才数量
        infoDto.setSuitableFlag(0);
        TbPersonalTopicCount topicCount = tbPersonalInfoService.selectTopicCount(infoDto);
        // 不合适的人才数量
        infoDto.setSuitableFlag(1);
        TbPersonalTopicCount unsuitableTopicCount = tbPersonalInfoService.selectTopicCount(infoDto);
        topicCount.setUnsuitableCount(unsuitableTopicCount.getUnsuitableCount());
        return AjaxResult.success(topicCount);
    }

    /**
     * 更新标识
     */
    @GetMapping("/editFlag")
    public AjaxResult editFlag(TbPersonalInfo personalInfo) {
        if (personalInfo.getAttentionFlag() != null) {
            if (personalInfo.getAttentionFlag() != 0 && personalInfo.getAttentionFlag() != 1) {
                return AjaxResult.error("参数异常，关注状态标识只能为0或1");
            }
        }
        if (personalInfo.getSuitableFlag() != null) {
            if (personalInfo.getSuitableFlag() != 0 && personalInfo.getSuitableFlag() != 1) {
                return AjaxResult.error("参数异常，是否合适标识只能为0或1");
            }
        }
        return toAjax(tbPersonalInfoService.updateFlagById(personalInfo));
    }

}
