package com.ruoyi.talent.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.talentbase.domain.TbAnalyseProjectExperience;
import com.ruoyi.talentbase.service.ITbAnalyseProjectExperienceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 项目经历分析Controller
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@RestController
@RequestMapping("/talentbase/analyseProjectExperience")
public class TbAnalyseProjectExperienceController extends BaseController
{
    @Autowired
    private ITbAnalyseProjectExperienceService tbAnalyseProjectExperienceService;

    /**
     * 查询项目经历分析列表
     */
    @RequiresPermissions("talent:analyseProjectExperience:list")
    @GetMapping("/list")
    public TableDataInfo list(TbAnalyseProjectExperience tbAnalyseProjectExperience)
    {
        startPage();
        List<TbAnalyseProjectExperience> list = tbAnalyseProjectExperienceService.selectTbAnalyseProjectExperienceList(tbAnalyseProjectExperience);
        return getDataTable(list);
    }

    /**
     * 导出项目经历分析列表
     */
    @RequiresPermissions("talent:analyseProjectExperience:export")
    @Log(title = "项目经历分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbAnalyseProjectExperience tbAnalyseProjectExperience)
    {
        List<TbAnalyseProjectExperience> list = tbAnalyseProjectExperienceService.selectTbAnalyseProjectExperienceList(tbAnalyseProjectExperience);
        ExcelUtil<TbAnalyseProjectExperience> util = new ExcelUtil<TbAnalyseProjectExperience>(TbAnalyseProjectExperience.class);
        util.exportExcel(response, list, "项目经历分析数据");
    }

    /**
     * 获取项目经历分析详细信息
     */
    @RequiresPermissions("talent:analyseProjectExperience:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tbAnalyseProjectExperienceService.selectTbAnalyseProjectExperienceById(id));
    }

    /**
     * 新增项目经历分析
     */
    @RequiresPermissions("talent:analyseProjectExperience:add")
    @Log(title = "项目经历分析", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TbAnalyseProjectExperience tbAnalyseProjectExperience)
    {
        return toAjax(tbAnalyseProjectExperienceService.insertTbAnalyseProjectExperience(tbAnalyseProjectExperience));
    }

    /**
     * 修改项目经历分析
     */
    @RequiresPermissions("talent:analyseProjectExperience:edit")
    @Log(title = "项目经历分析", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TbAnalyseProjectExperience tbAnalyseProjectExperience)
    {
        return toAjax(tbAnalyseProjectExperienceService.updateTbAnalyseProjectExperience(tbAnalyseProjectExperience));
    }

    /**
     * 删除项目经历分析
     */
    @RequiresPermissions("talent:analyseProjectExperience:remove")
    @Log(title = "项目经历分析", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbAnalyseProjectExperienceService.deleteTbAnalyseProjectExperienceByIds(ids));
    }
} 