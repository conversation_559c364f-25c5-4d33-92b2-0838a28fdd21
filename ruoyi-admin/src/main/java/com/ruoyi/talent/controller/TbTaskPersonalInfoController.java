package com.ruoyi.talent.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import com.ruoyi.common.entity.domain.talent.TbTaskPosting;
import com.ruoyi.common.entity.utils.DictUtils;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoDto;
import com.ruoyi.talentbase.domain.dto.TbPersonalSelectedDto;
import com.ruoyi.talentbase.domain.dto.TbTaskPersonalInfoParamDto;
import com.ruoyi.talentbase.domain.enums.FiledFlagEnum;
import com.ruoyi.talentbase.domain.enums.PersonalIdSourceEnum;
import com.ruoyi.talentbase.domain.enums.StorageDisplayEnum;
import com.ruoyi.talentbase.domain.vo.TbTaskPersonalInfoVo;
import com.ruoyi.talentbase.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 【个人简历基本信息】Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RestController
@RequestMapping("/talentbase/personal")
public class TbTaskPersonalInfoController extends BaseController {
    @Autowired
    private ITbTaskPersonalInfoService tbTaskPersonalInfoService;
    @Autowired
    private ITbTaskWorkExperienceService tbTaskWorkExperienceService;
    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;
    @Autowired
    private ITbTaskEducationInfoService tbTaskEducationInfoService;
    @Autowired
    private ITbTaskPostingService tbTaskPostingService;
    @Autowired
    private ITbTaskProjectExperienceService projectExperienceService;

    /**
     * 查询【个人简历基本信息】列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbTaskPersonalInfoParamDto tbPersonalInfo) {
        startPage();
        if (tbPersonalInfo.getJobId() != null) {
            List<TbTaskPersonalInfoVo> list = tbTaskPersonalInfoService.selectTbTaskPersonalInfoVoList(tbPersonalInfo);
            return getDataTable(list);
        }
        return getDataTable(new ArrayList<>());
    }


    /**
     * 查询人才列表列表
     */
    @PostMapping("/getList")
    public TableDataInfo getList(@RequestBody TbTaskPersonalInfoParamDto tbPersonalInfo) {
        startPage();
        if (tbPersonalInfo.getJobId() != null) {
            tbPersonalInfo.setFiledFlag(FiledFlagEnum.NOT_FILED_FLAG.getCode());
            List<TbTaskPersonalInfoVo> list = tbTaskPersonalInfoService.selectTbTaskPersonalInfoVoList(tbPersonalInfo);
            List<TbPersonalSelectedDto> selectedList = tbPersonalInfo.getSelectedList();
            if (selectedList != null && !selectedList.isEmpty()) {
                List<Long> tpInfoIds = selectedList.stream().filter(s -> s.getType() == 0).map(TbPersonalSelectedDto :: getId).collect(Collectors.toList());
                List<Long> ids = new ArrayList<>();
                if (!tpInfoIds.isEmpty()) {
                    Long[] infoIds = tpInfoIds.toArray(new Long[0]);
                    List<TbPersonalInfo> infos = tbPersonalInfoService.selectByIds(infoIds);
                    ids.addAll(infos.stream().map(TbPersonalInfo :: getTaskPersonalId).collect(Collectors.toList()));
                }
                // 判断是否存在Ids中
                list.forEach(item -> {
                    if (!ids.isEmpty() && ids.contains(item.getId())) {
                        item.setSelected(1);
                    } else {
                        item.setSelected(0);
                    }
                });
            } else {
                list.forEach(item -> item.setSelected(0));
            }
            return getDataTable(list);
        }
        return getDataTable(new ArrayList<>());
    }

    /**
     * 获取【个人简历基本信息】详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TbTaskPersonalInfo personalInfo = tbTaskPersonalInfoService.selectTbTaskPersonalInfoById(id);
        if (personalInfo == null){
            return AjaxResult.error("未找到对应的人才信息");
        }
        // 查询工作经验
        List<TbTaskWorkExperience> workExperienceList = tbTaskWorkExperienceService.selectTbTaskWorkExperienceByPersonalId(id);
        personalInfo.setWorkExperienceList(workExperienceList);
        // 查询教育经历
        TbTaskEducationInfo taskEducationInfo = new TbTaskEducationInfo();
        taskEducationInfo.setPersonalId(id);
        List<TbTaskEducationInfo> educationInfoList = tbTaskEducationInfoService.selectTbTaskEducationInfoList(taskEducationInfo);
        personalInfo.setEducationInfoList(educationInfoList);
        TbTaskPosting tbTaskPosting = tbTaskPostingService.selectTbTaskPostingById(personalInfo.getJobId());
        personalInfo.setEducationWeight(tbTaskPosting.getEducationWeight());
        personalInfo.setWorkExperienceWeight(tbTaskPosting.getWorkExperienceWeight());
        personalInfo.setJobHoppingRateWeight(tbTaskPosting.getJobHoppingRateWeight());
        personalInfo.setSalaryRangeWeight(tbTaskPosting.getSalaryRangeWeight());
        List<TbTaskProjectExperience> projectExperiences = projectExperienceService.selectTbTaskProjectExperienceByPId(id);
        personalInfo.setProjectExperienceList(projectExperiences);
        return AjaxResult.success(personalInfo);
    }

    /**
     * 删除【个人简历基本信息】
     */
    @Log(title = "【个人简历基本信息】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbTaskPersonalInfoService.deleteTbTaskPersonalInfoByIds(ids));
    }

    /**
     * 入职
     */
    @GetMapping("/entry/{id}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult entry(@PathVariable Long id) {
        TbTaskPersonalInfo info = tbTaskPersonalInfoService.selectTbTaskPersonalInfoById(id);
        if (info == null) {
            return AjaxResult.error("未找到对应的人才信息");
        }
        TbPersonalInfo tbPersonalInfo = tbPersonalInfoService.selectTbPersonalInfoByTaskPidAndSource(id, PersonalIdSourceEnum.TASK_POOL.getCode());
        // 如果没有人才库,就先入库
        Map<String, Long> map = new HashMap<>();
        if (tbPersonalInfo == null) {
            Map<String, String> education = DictUtils.getDictByValueMap(SysDictDataEnum.EDUCATION.getName());
            info.setStorageDisplay(1);
            Long pid = addTalentPool(info, education);
            map.put("id", pid);
        } else {
            if (tbPersonalInfo.getFiledFlag().equals(FiledFlagEnum.FILED_FLAG.getCode())) {
                return AjaxResult.error("该人才已经建档,请前往人才档案操作");
            }
            map.put("id", tbPersonalInfo.getId());
        }
        return AjaxResult.success(map);
    }

    /**
     * 批量入库
     */
    @GetMapping("/batchEditTalentPoolStatus/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult batchEditTalentPoolStatus(@PathVariable Long[] ids) {
        List<TbTaskPersonalInfo> infoList = tbTaskPersonalInfoService.selectListByIds(ids);
        if (infoList.isEmpty()) {
            return AjaxResult.error("未找到对应的人才信息");
        }
        logger.error("入库开始");
        try {
            int count = tbPersonalInfoService.selectInfoByTaskIds(ids, PersonalIdSourceEnum.TASK_POOL.getCode());
            if (count > 0) {
                return AjaxResult.error("当前选中的人中,存在已入库的人才信息");
            }
            Map<String, String> education = DictUtils.getDictByValueMap(SysDictDataEnum.EDUCATION.getName());
            for (TbTaskPersonalInfo info : infoList) {
                TbPersonalInfo tbPersonalInfo = tbPersonalInfoService.selectTbPersonalInfoByTaskPidAndSource(info.getId(), PersonalIdSourceEnum.TASK_POOL.getCode());
                if (tbPersonalInfo == null) {
                    addTalentPool(info, education);
                } else {
                    // 更新人才库信息
                    TbPersonalInfo updateData = new TbPersonalInfo();
                    updateData.setTaskPersonalId(info.getId());
                    updateData.setStorageDisplay(StorageDisplayEnum.STORAGE_DISPLAY.getCode());
                    updateData.setPersonalIdSource(PersonalIdSourceEnum.TASK_POOL.getCode());
                    tbPersonalInfoService.updateByTaskPid(updateData);
                }
                info.setTalentPoolStatus(2);
                tbTaskPersonalInfoService.updateTbTaskPersonalInfo(info);
            }
            logger.error("入库结束");
        } catch (Exception e) {
            logger.error("入库失败", e);
        }
        return AjaxResult.success();
    }

    /**
     * 新增入库
     */
    public Long addTalentPool(TbTaskPersonalInfo info, Map<String, String> education) {
        TbPersonalInfoDto dto = new TbPersonalInfoDto();
        BeanUtils.copyProperties(info, dto);
        // 基本信息
        dto.setId(null);
        dto.setTaskPersonalId(info.getId());
        dto.setEmploymentStatus("0");
        dto.setPersonalIdSource(PersonalIdSourceEnum.TASK_POOL.getCode());
        TbEducationInfo educationInfo = new TbEducationInfo();
        if (info.getEducation() != null && education != null) {
            educationInfo.setEducationLevel(education.get(info.getEducation()));
        }
        educationInfo.setGraduationSchool(info.getSchoolName());
        educationInfo.setMajor(info.getMajor());
        educationInfo.setIntroduction(info.getIntroduction());
        List<TbEducationInfo> educationInfoList = new ArrayList<>();
        educationInfoList.add(educationInfo);
        // 公司信息
        List<TbTaskWorkExperience> experiences = tbTaskWorkExperienceService.selectTbTaskWorkExperienceByPersonalId(info.getId());
        List<TbWorkExperience> experienceList = new ArrayList<>();
        // 数组拷贝
        for (TbTaskWorkExperience experience : experiences) {
            TbWorkExperience workExperience = new TbWorkExperience();
            BeanUtils.copyProperties(experience, workExperience);
            experienceList.add(workExperience);
        }

        TbTaskProjectExperience taskProjectExperience = new TbTaskProjectExperience();
        taskProjectExperience.setPersonalId(info.getId());
        List<TbTaskProjectExperience> projectExperiences = projectExperienceService.selectTbTaskProjectExperienceList(taskProjectExperience);
        List<TbProjectExperience> projectExperienceList = new ArrayList<>();
        for (TbTaskProjectExperience projectExp : projectExperiences) {
            TbProjectExperience projectExperience = new TbProjectExperience();
            BeanUtils.copyProperties(projectExp, projectExperience);
            projectExperienceList.add(projectExperience);
        }
        dto.setEducationInfoList(educationInfoList);
        dto.setWorkExperienceList(experienceList);
        dto.setProjectExperienceList(projectExperienceList);
        try {
            tbPersonalInfoService.insertTbPersonalInfoDto(dto);
            return dto.getId();
        } catch (Exception e) {
            String message = e.getMessage();
            logger.error("id信息：{}入库失败:{}", info.getId(), message);
            info.setErrorMsg(message);
        }
        return null;
    }
}
