package com.ruoyi.talent.controller;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.talentbase.domain.InterviewEvaluationTemplate;
import com.ruoyi.talentbase.domain.TbOfflineInterview;
import com.ruoyi.talentbase.service.IInterviewEvaluationTemplateService;
import com.ruoyi.talentbase.service.ITbOfflineInterviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 面试评价模板Controller
 */
@RestController
@RequestMapping("/talentbase/template")
public class InterviewEvaluationTemplateController extends BaseController {
    @Autowired
    private IInterviewEvaluationTemplateService interviewEvaluationTemplateService;
    @Autowired
    private ITbOfflineInterviewService offlineInterviewService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ISysDeptService sysDeptService;

    /**
     * 获取面试评价模板详细信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo() {
        List<InterviewEvaluationTemplate> list = interviewEvaluationTemplateService.selectTemplateListByPid(null);
        if (list == null){
            return success();
        }

        // list将有父级id的放入父级的下面去
        for (InterviewEvaluationTemplate template : list) {
            Long id = template.getId();
            List<InterviewEvaluationTemplate> children = interviewEvaluationTemplateService.selectTemplateListByPid(id);
            template.setChildren(children);
        }
        return success(list);
    }

    /**
     * 修改面试评价模板
     */
    @PutMapping
    public AjaxResult edit(@RequestBody List<InterviewEvaluationTemplate> list) {
        try {
            // 先删除后新增
            interviewEvaluationTemplateService.removeAll();
            for (InterviewEvaluationTemplate item : list) {
                item.setId(null);
                item.setParentId(null);
                item.setCreateTime(DateUtils.getNowDate());
            }
            boolean batch = interviewEvaluationTemplateService.saveBatch(list);
            if (batch) {
                list.stream().filter(s -> s.getChildren() != null).forEach(item -> {
                    item.getChildren().forEach(child -> {
                        child.setParentId(item.getId());
                        child.setCreateTime(DateUtils.getNowDate());
                    });
                    interviewEvaluationTemplateService.saveBatch(item.getChildren());
                });
            }
            return toAjax(batch);
        } catch (Exception e) {
            e.printStackTrace();
            return error("更新模板信息失败");
        }
    }

    @Log(title = "获取二维码信息")
    @GetMapping("/getQrcode")
    public AjaxResult getQrcode(Long offlineInterviewId) {
        TbOfflineInterview interview = offlineInterviewService.selectTbOfflineInterviewById(offlineInterviewId);
        if (interview == null) {
            return error("未找到面试记录");
        }
        String uuid = UUID.randomUUID().toString();
        String key = Constants.OFFLINE_INTERVIEW_QRCODE_EXPIRE_TIME_KEY + interview.getId() + ":" + uuid;
        if (redisService.hasKey(key)) {
            redisService.deleteObject(key);
        }
        redisService.setCacheObject(key, interview, 10L, TimeUnit.MINUTES);
        return success(uuid);
    }

    /**
     * 获取面试评价模板详细信息
     */
    @GetMapping("/getFormat/{interviewId}/{uuid}")
    public AjaxResult getFormat(@PathVariable Long interviewId, @PathVariable String uuid) {
        // 填写信息过期时间的key
        String key = Constants.OFFLINE_INTERVIEW_QRCODE_EXPIRE_TIME_KEY + interviewId + ":" + uuid;
        TbOfflineInterview interview = redisService.getCacheObject(key);
        if (interview == null) {
            return error("二维码已过期");
        }
        List<InterviewEvaluationTemplate> list = interviewEvaluationTemplateService.selectTemplateListByPid(null);
        if (list == null){
            return success();
        }

        // list将有父级id的放入父级的下面去
        for (InterviewEvaluationTemplate template : list) {
            Long id = template.getId();
            List<InterviewEvaluationTemplate> children = interviewEvaluationTemplateService.selectTemplateListByPid(id);
            template.setChildren(children);
        }

        Map<String, Object> map = new HashMap<>();
        map.put("name", interview.getName());
        map.put("jobIntention", interview.getJobIntention());
        if (interview.getDeptId() != null){
            SysDept sysDept = sysDeptService.selectDeptById(interview.getDeptId());
            if (sysDept != null) {
                map.put("deptName", sysDept.getDeptName());
            }
        }
        map.put("format", list);
        return success(map);
    }
}