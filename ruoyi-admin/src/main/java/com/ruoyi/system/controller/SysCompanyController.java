package com.ruoyi.system.controller;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysDept;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.service.ISysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 企业信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/company")
@Api(tags = "企业信息（企业管理）")
public class SysCompanyController extends BaseController {
    @Autowired
    private ISysDeptService deptService;


    /**
     * 根据企业编号获取详细信息
     */
//    @RequiresPermissions("system:company:query")
    @GetMapping("/query")
    @ApiOperation("获取企业详细信息")
    public AjaxResult getInfo() {
        SysDept sysDept = deptService.selectDeptById(SysDept.comRootDeptID);
        return AjaxResult.success(sysDept);
    }

    /**
     * 修改企业信息
     */
//    @RequiresPermissions("system:company:edit")
    @Log(title = "企业管理", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    @ApiOperation("修改企业信息")
    public AjaxResult edit(@Validated @RequestBody SysDept dept) {
        dept.setDeptId(SysDept.comRootDeptID);
        if (StringUtils.isEmpty(dept.getDeptName())) {
            return AjaxResult.error("名称不允许为空");
        }
        int row = deptService.updateDept(dept);
        return toAjax(row);
    }

}
