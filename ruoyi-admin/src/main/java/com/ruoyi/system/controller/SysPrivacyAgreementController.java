package com.ruoyi.system.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysPrivacyAgreement;
import com.ruoyi.system.service.ISysPrivacyAgreementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 隐私协议Controller
 *
 * <AUTHOR>
 * @date 2022-03-15
 */
@RestController
@RequestMapping("/system/sysPrivacyAgreement")
@Api(tags = "隐私协议API")
public class SysPrivacyAgreementController extends BaseController {
    @Autowired
    private ISysPrivacyAgreementService sysPrivacyAgreementService;

    /**
     * 查询隐私协议列表
     */
    @ApiOperation(value = "查询隐私协议列表")
    @RequiresPermissions("system:agreement:list")
    @GetMapping("/list")
    public AjaxResult list(SysPrivacyAgreement sysPrivacyAgreement) {
        startPage();
        List<SysPrivacyAgreement> list = sysPrivacyAgreementService.selectList(sysPrivacyAgreement);
        return getNewDataTable(list);
    }

    /**
     * 导出隐私协议列表
     */
    @ApiOperation(value = "导出隐私协议列表")
    @RequiresPermissions("system:agreement:export")
    @Log(title = "隐私协议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPrivacyAgreement sysPrivacyAgreement) {
        List<SysPrivacyAgreement> list = sysPrivacyAgreementService.selectList(sysPrivacyAgreement);
        ExcelUtil<SysPrivacyAgreement> util = new ExcelUtil<SysPrivacyAgreement>(SysPrivacyAgreement.class);
        util.exportExcel(response, list, "隐私协议数据");
    }

    /**
     * 获取隐私协议详细信息
     */
    @ApiOperation(value = "获取隐私协议详细信息")
    @RequiresPermissions("system:agreement:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sysPrivacyAgreementService.selectById(id));
    }

    /**
     * 新增隐私协议
     */
    @ApiOperation(value = "新增隐私协议")
    @RequiresPermissions("system:agreement:add")
    @Log(title = "隐私协议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysPrivacyAgreement sysPrivacyAgreement) {
        SysPrivacyAgreement sysPrivacyAgreement1 = new SysPrivacyAgreement();
        sysPrivacyAgreement1.setType(sysPrivacyAgreement.getType());
        List<SysPrivacyAgreement> list = sysPrivacyAgreementService.selectList(sysPrivacyAgreement1);
        if (list.size() > 0) {
            return AjaxResult.error("该类型已存在不要重复添加");
        }

        return toAjax(sysPrivacyAgreementService.insert(sysPrivacyAgreement));
    }

    /**
     * 修改隐私协议
     */
    @ApiOperation(value = "修改隐私协议")
    @RequiresPermissions("system:agreement:edit")
    @Log(title = "隐私协议", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody SysPrivacyAgreement sysPrivacyAgreement) {
        return toAjax(sysPrivacyAgreementService.update(sysPrivacyAgreement));
    }

    /**
     * 删除隐私协议
     */
    @ApiOperation(value = "删除隐私协议")
    @RequiresPermissions("system:agreement:remove")
    @Log(title = "隐私协议", businessType = BusinessType.DELETE)
    @PostMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysPrivacyAgreementService.deleteByIds(ids));
    }

    /**
     * 查询隐私协议列表-没有拦截
     */
    //@RequiresPermissions("system:agreement:list")
    @GetMapping("/app/list")
    @ApiOperation("隐私协议查询")
    public AjaxResult appList(SysPrivacyAgreement sysPrivacyAgreement) {
        List<SysPrivacyAgreement> list = sysPrivacyAgreementService.selectList(sysPrivacyAgreement);
        return AjaxResult.success(list);
    }

    /**
     * 获取隐私协议详细信息
     */
    @ApiOperation("隐私协议详情")
    @GetMapping(value = "/app/{id}")
    public AjaxResult getAppInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sysPrivacyAgreementService.selectById(id));
    }
}
