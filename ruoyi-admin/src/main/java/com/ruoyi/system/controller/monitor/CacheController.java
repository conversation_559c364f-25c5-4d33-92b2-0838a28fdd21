package com.ruoyi.system.controller.monitor;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.interceptor.DdosProtectionInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * DDOS缓存监控
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/cache")
public class CacheController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(CacheController.class);

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private RedisService redisService;

    @Autowired
    private DdosProtectionInterceptor ddosProtectionInterceptor;

    // Redis键前缀
    private static final String RATE_LIMIT_PREFIX = "ddos:rate_limit:";
    private static final String BLACKLIST_PREFIX = "ddos:blacklist:";
    private static final String ABNORMAL_PREFIX = "ddos:abnormal:";
    private static final String CONCURRENT_PREFIX = "ddos:concurrent:";

    @GetMapping()
    public AjaxResult getInfo() throws Exception
    {
        Properties info = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info());
        Properties commandStats = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info("commandstats"));
        Object dbSize = redisTemplate.execute((RedisCallback<Object>) connection -> connection.dbSize());

        Map<String, Object> result = new HashMap<>(3);
        result.put("info", info);
        result.put("dbSize", dbSize);

        List<Map<String, String>> pieList = new ArrayList<>();
        commandStats.stringPropertyNames().forEach(key -> {
            Map<String, String> data = new HashMap<>(2);
            String property = commandStats.getProperty(key);
            data.put("name", StringUtils.removeStart(key, "cmdstat_"));
            data.put("value", StringUtils.substringBetween(property, "calls=", ",usec"));
            pieList.add(data);
        });
        result.put("commandStats", pieList);
        return AjaxResult.success(result);
    }

    /**
     * 查询指定IP的DDoS防护状态
     */
    @GetMapping("/ddos/status/{ip}")
    public AjaxResult getDdosStatus(@PathVariable String ip) {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 查询并发连接数
            Long concurrentConnections = ddosProtectionInterceptor.getCurrentConcurrentConnections(ip);
            status.put("concurrentConnections", concurrentConnections);
            
            // 查询频率限制
            String rateLimitKey = RATE_LIMIT_PREFIX + ip;
            Object rateLimitObj = redisService.getCacheObject(rateLimitKey);
            Long rateCount = convertToLong(rateLimitObj);
            status.put("rateLimit", rateCount != null ? rateCount : 0);
            
            // 查询黑名单状态
            String blacklistKey = BLACKLIST_PREFIX + ip;
            Boolean isBlacklisted = redisService.getCacheObject(blacklistKey);
            status.put("isBlacklisted", Boolean.TRUE.equals(isBlacklisted));
            
            // 查询异常请求记录
            String abnormalKey = ABNORMAL_PREFIX + ip;
            Object abnormalObj = redisService.getCacheObject(abnormalKey);
            Long abnormalCount = convertToLong(abnormalObj);
            status.put("abnormalRequests", abnormalCount != null ? abnormalCount : 0);
            
            return AjaxResult.success(status);
        } catch (Exception e) {
            log.error("查询IP {} DDoS防护状态失败: {}", ip, e.getMessage(), e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 清理指定IP的并发连接限制
     */
    @PostMapping("/ddos/clearConcurrent/{ip}")
    public AjaxResult clearConcurrentConnections(@PathVariable String ip) {
        try {
            boolean cleared = ddosProtectionInterceptor.clearConcurrentConnections(ip);
            if (cleared) {
                log.info("清理IP {} 的并发连接限制成功", ip);
                return AjaxResult.success("成功清理IP " + ip + " 的并发连接限制");
            } else {
                return AjaxResult.success("IP " + ip + " 当前没有并发连接限制");
            }
        } catch (Exception e) {
            log.error("清理IP {} 并发连接限制失败: {}", ip, e.getMessage(), e);
            return AjaxResult.error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 清理指定IP的频率限制
     */
    @PostMapping("/ddos/clearRateLimit/{ip}")
    public AjaxResult clearRateLimit(@PathVariable String ip) {
        try {
            String rateLimitKey = RATE_LIMIT_PREFIX + ip;
            boolean deleted = redisService.deleteObject(rateLimitKey);
            
            if (deleted) {
                log.info("清理IP {} 的频率限制成功", ip);
                return AjaxResult.success("成功清理IP " + ip + " 的频率限制");
            } else {
                return AjaxResult.success("IP " + ip + " 当前没有频率限制");
            }
        } catch (Exception e) {
            log.error("清理IP {} 频率限制失败: {}", ip, e.getMessage(), e);
            return AjaxResult.error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 清理指定IP的黑名单状态
     */
    @PostMapping("/ddos/clearBlacklist/{ip}")
    public AjaxResult clearBlacklist(@PathVariable String ip) {
        try {
            // 使用新的简化黑名单清理方法
            boolean cleared = ddosProtectionInterceptor.clearBlacklist(ip);
            
            if (cleared) {
                log.info("清理IP {} 的黑名单状态成功", ip);
                return AjaxResult.success("成功清理IP " + ip + " 的黑名单状态");
            } else {
                return AjaxResult.success("IP " + ip + " 当前不在黑名单中");
            }
        } catch (Exception e) {
            log.error("清理IP {} 黑名单状态失败: {}", ip, e.getMessage(), e);
            return AjaxResult.error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 清理指定IP的异常请求记录
     */
    @PostMapping("/ddos/clearAbnormal/{ip}")
    public AjaxResult clearAbnormalRequests(@PathVariable String ip) {
        try {
            String abnormalKey = ABNORMAL_PREFIX + ip;
            boolean deleted = redisService.deleteObject(abnormalKey);
            
            if (deleted) {
                log.info("清理IP {} 的异常请求记录成功", ip);
                return AjaxResult.success("成功清理IP " + ip + " 的异常请求记录");
            } else {
                return AjaxResult.success("IP " + ip + " 当前没有异常请求记录");
            }
        } catch (Exception e) {
            log.error("清理IP {} 异常请求记录失败: {}", ip, e.getMessage(), e);
            return AjaxResult.error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 清理指定IP的所有DDoS防护状态
     */
    @PostMapping("/ddos/clearAll/{ip}")
    public AjaxResult clearAllLimits(@PathVariable String ip) {
        try {
            int clearedCount = 0;
            StringBuilder result = new StringBuilder();
            
            // 清理并发连接限制
            if (ddosProtectionInterceptor.clearConcurrentConnections(ip)) {
                clearedCount++;
                result.append("并发连接限制 ");
            }
            
            // 清理频率限制
            String rateLimitKey = RATE_LIMIT_PREFIX + ip;
            if (redisService.deleteObject(rateLimitKey)) {
                clearedCount++;
                result.append("频率限制 ");
            }
            
            // 清理黑名单状态
            if (ddosProtectionInterceptor.clearBlacklist(ip)) {
                clearedCount++;
                result.append("黑名单状态 ");
            }
            
            // 清理异常请求记录
            String abnormalKey = ABNORMAL_PREFIX + ip;
            if (redisService.deleteObject(abnormalKey)) {
                clearedCount++;
                result.append("异常请求记录 ");
            }
            
            if (clearedCount > 0 || result.length() > 0) {
                log.info("清理IP {} 的所有DDoS防护状态: {}", ip, result.toString());
                return AjaxResult.success("成功清理IP " + ip + " 的DDoS防护状态: " + result.toString());
            } else {
                return AjaxResult.success("IP " + ip + " 当前没有任何DDoS防护限制");
            }
        } catch (Exception e) {
            log.error("清理IP {} 所有DDoS防护状态失败: {}", ip, e.getMessage(), e);
            return AjaxResult.error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 批量清理所有IP的DDoS防护状态
     */
    @PostMapping("/ddos/clearAllIps")
    public AjaxResult clearAllIpsLimits() {
        try {
            int clearedCount = 0;
            
            // 清理所有并发连接限制
            Collection<String> concurrentKeys = redisService.keys(CONCURRENT_PREFIX + "*");
            if (!concurrentKeys.isEmpty()) {
                redisService.deleteObject(concurrentKeys);
                clearedCount += concurrentKeys.size();
                log.info("清理了 {} 个IP的并发连接限制", concurrentKeys.size());
            }
            
            // 清理所有频率限制
            Collection<String> rateLimitKeys = redisService.keys(RATE_LIMIT_PREFIX + "*");
            if (!rateLimitKeys.isEmpty()) {
                redisService.deleteObject(rateLimitKeys);
                clearedCount += rateLimitKeys.size();
                log.info("清理了 {} 个IP的频率限制", rateLimitKeys.size());
            }
            
            // 清理所有黑名单状态
            Collection<String> blacklistKeys = redisService.keys(BLACKLIST_PREFIX + "*");
            if (!blacklistKeys.isEmpty()) {
                redisService.deleteObject(blacklistKeys);
                clearedCount += blacklistKeys.size();
                log.info("清理了 {} 个IP的黑名单状态", blacklistKeys.size());
            }
            
            // 清理所有异常请求记录
            Collection<String> abnormalKeys = redisService.keys(ABNORMAL_PREFIX + "*");
            if (!abnormalKeys.isEmpty()) {
                redisService.deleteObject(abnormalKeys);
                clearedCount += abnormalKeys.size();
                log.info("清理了 {} 个IP的异常请求记录", abnormalKeys.size());
            }
            
            return AjaxResult.success("成功清理所有IP的DDoS防护状态，共清理 " + clearedCount + " 项记录");
        } catch (Exception e) {
            log.error("清理所有IP DDoS防护状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 安全地将Object转换为Long
     */
    private Long convertToLong(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof Long) {
            return (Long) obj;
        }
        if (obj instanceof Integer) {
            return ((Integer) obj).longValue();
        }
        if (obj instanceof String) {
            try {
                return Long.parseLong((String) obj);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为Long: {}", obj);
                return null;
            }
        }
        // 其他数字类型
        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        }
        log.warn("无法将对象转换为Long: {}, 类型: {}", obj, obj.getClass().getName());
        return null;
    }
}
