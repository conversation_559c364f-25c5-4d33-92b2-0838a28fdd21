package com.ruoyi.system.controller;

import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.sign.RsaUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.common.entity.domain.SysLogininfor;
import com.ruoyi.common.entity.domain.SysUser;
import com.ruoyi.common.entity.domain.system.LoginUser;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.service.ISysAdminUserService;
import com.ruoyi.system.service.ISysLogininforService;
import com.ruoyi.system.service.ISysRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@Api(tags = "个人信息API")
@RestController
@RequestMapping("/system/sysProfile")
public class SysProfileController extends BaseController {
    @Autowired
    private ISysAdminUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysLogininforService logininforService;

    /**
     * 个人信息
     */
    @ApiOperation(value = "个人信息")
    @GetMapping
    public AjaxResult profile() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();

        // 获取用户最后一次登录记录
        SysLogininfor lastLogin = logininforService.selectLastLogininfor(loginUser.getUsername());
        if(lastLogin != null){
            user.setLoginDate(lastLogin.getLoginTime());
        }

        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roleGroup", roleService.selectUserRoleGroup(loginUser.getUsername()));
        return ajax;
    }

    /**
     * 修改用户
     */
    @ApiOperation(value = "修改个人信息")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProfile(@RequestBody SysAdminUser user) {
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysAdminUser sysUser = (SysAdminUser) loginUser.getUser();
        user.setUserId(sysUser.getUserId());
        if (userService.updateUserProfileInfo(user) > 0) {
            // 更新缓存用户信息
            sysUser.setNickName(user.getNickName());
            sysUser.setPhonenumber(user.getPhonenumber());
            sysUser.setEmail(user.getEmail());
            tokenService.setLoginUser(loginUser);
            return AjaxResult.success();
        }
        return AjaxResult.error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @ApiOperation(value = "重置密码")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String userName = loginUser.getUsername();
        String password = loginUser.getPassword();
        try {
//            oldPassword = RsaUtils.decryptByPrivateKey(oldPassword);
            newPassword = RsaUtils.decryptByPrivateKey(newPassword);
        } catch (Exception e) {
            return AjaxResult.error("解密失败");
        }
        if (!SecurityUtils.matchesPassword(oldPassword, password)) {
            return AjaxResult.error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            return AjaxResult.error("新密码不能与旧密码相同");
        }

        SysAdminUser user = (SysAdminUser) loginUser.getUser();
        user.setPassword(SecurityUtils.encryptPassword(newPassword));
        if (userService.updatePassword(user) > 0) {
            // 更新缓存用户密码
            tokenService.setLoginUser(loginUser);
            return AjaxResult.success();
        }
        return AjaxResult.error("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */@ApiOperation(value = "头像上传")
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestBody SysAdminUser user, LoginUser loginUser) throws IOException {
        String avatar = user.getAvatar();
        if (!avatar.isEmpty()) {
            loginUser = SecurityUtils.getLoginUser();
            SysAdminUser adminUser = (SysAdminUser) loginUser.getUser();
            adminUser.setAvatar(avatar);
            if (userService.updateUserAvatar(adminUser) > 0) {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("imgUrl", avatar);
                // 更新缓存用户头像
                tokenService.setLoginUser(loginUser);
                return ajax;
            }
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }
}
