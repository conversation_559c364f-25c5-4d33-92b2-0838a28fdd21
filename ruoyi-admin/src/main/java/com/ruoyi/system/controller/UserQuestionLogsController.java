package com.ruoyi.system.controller;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.redis.service.RedisCache;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.domain.UserQuestionLogs;
import com.ruoyi.system.domain.UserQuestionStat;
import com.ruoyi.system.service.IUserQuestionLogsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 用户问答记录Controller
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@RestController
@RequestMapping("/system/questionLogs")
public class UserQuestionLogsController extends BaseController {
    @Autowired
    private IUserQuestionLogsService userQuestionLogsService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询用户问答记录列表
     */
    @RequiresPermissions("system:logs:list")
    @GetMapping("/list")
    public TableDataInfo list(UserQuestionLogs userQuestionLogs) {
        startPage();
        List<UserQuestionLogs> list = userQuestionLogsService.selectUserQuestionLogsList(userQuestionLogs);
        return getDataTable(list);
    }

    /**
     * 导出用户问答记录列表
     */
    @RequiresPermissions("system:logs:export")
    @Log(title = "用户问答记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserQuestionLogs userQuestionLogs) {
        List<UserQuestionLogs> list = userQuestionLogsService.selectUserQuestionLogsList(userQuestionLogs);
        ExcelUtil<UserQuestionLogs> util = new ExcelUtil<UserQuestionLogs>(UserQuestionLogs.class);
        util.exportExcel(response, list, "用户问答记录数据");
    }

    /**
     * 获取用户问答记录详细信息
     */
    @RequiresPermissions("system:logs:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(userQuestionLogsService.selectUserQuestionLogsById(id));
    }

    /**
     * 新增用户问答记录
     */
//    @RequiresPermissions("system:logs:add")
    @Log(title = "用户问答记录", businessType = BusinessType.INSERT)
    @Transactional
    @PostMapping
    public AjaxResult add(@RequestBody UserQuestionLogs userQuestionLogs) {
        userQuestionLogs.setUserId(SecurityUtils.getUserId());
        // 更新用户问答次数
        String date = DateUtils.parseDateToStr("yyyy-MM-dd", new Date());
        String userKey = Constants.USER_QUESTION_COUNT + date + ":" + SecurityUtils.getUserId();
        UserQuestionStat stat = redisCache.getCacheObject(userKey);
        if (stat == null) {

        }
        if (stat.getConsume() >= stat.getCount()) {
            // 0 标识次数已用尽
            return AjaxResult.success(0);
        }
        stat.setConsume(stat.getConsume() + 1);
        redisCache.setCacheObject(userKey, stat);
        // 将用户问题入库
        userQuestionLogsService.insertUserQuestionLogs(userQuestionLogs);
        return AjaxResult.success(stat.getCount() - stat.getConsume());
    }

    /**
     * 修改用户问答记录
     */
    @RequiresPermissions("system:logs:edit")
    @Log(title = "用户问答记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserQuestionLogs userQuestionLogs) {
        return toAjax(userQuestionLogsService.updateUserQuestionLogs(userQuestionLogs));
    }

    /**
     * 删除用户问答记录
     */
    @RequiresPermissions("system:logs:remove")
    @Log(title = "用户问答记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(userQuestionLogsService.deleteUserQuestionLogsByIds(ids));
    }

    /**
     * 校验用户问答次数
     */
    @GetMapping("/checkCount")
    public AjaxResult checkCount() {
        Long userId = SecurityUtils.getUserId();
        // redisKey
        String date = DateUtils.parseDateToStr("yyyy-MM-dd", new Date());
        String userKey = Constants.USER_QUESTION_COUNT + date + ":" + userId;
        // 判断用户是否问答
        UserQuestionStat stat = redisCache.getCacheObject(userKey);
        // 系统配置总数
        try {
            String str = redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + "sys.question.count");
            Long count = Long.valueOf(str);
            if (stat == null && count > 0) {
                // 初始化用户次数
                UserQuestionStat questionStat = new UserQuestionStat();
                questionStat.setCount(count);
                questionStat.setConsume(0L);
                questionStat.setUserId(userId);
                questionStat.setCompanyId(SecurityUtils.getCompanyId());
                questionStat.setCreateBy(SecurityUtils.getUsername());
                questionStat.setCreateTime(new Date());
                redisCache.setCacheObject(userKey, questionStat);
                return AjaxResult.success(count);
            }
        }catch (Exception e){
            throw new ServiceException("获取可用次数异常请联系管理员");
        }
        return AjaxResult.success(stat.getCount() - stat.getConsume());
    }

    /**
     * 生成文本Key 二维码
     * map.text 参数文本 参数值: 以防后面增加参数传输
     */
    @PostMapping("/createTextCode")
    public AjaxResult createTextCode(@RequestBody Map<String, String> map) {
        String uuid = String.valueOf(UUID.randomUUID());
        String key = Constants.USER_QUESTION_TEXT + uuid;
        String text = map.get("text");
        redisCache.setCacheObject(key, text, 10, TimeUnit.MINUTES);
        return AjaxResult.success("操作成功", uuid);
    }

    /**
     * 获取文本内容
     *
     * @param code
     * @return
     */
    @GetMapping("/getTextByCode")
    public AjaxResult getTextByCode(@RequestParam(name = "code") String code) {
        String key = Constants.USER_QUESTION_TEXT + code;
        String text = redisCache.getCacheObject(key);
        if (text == null) {
            return AjaxResult.error("文本内容暂无内容或已过期,请重新生成二维码");
        }
        return AjaxResult.success("操作成功", text);
    }

}
