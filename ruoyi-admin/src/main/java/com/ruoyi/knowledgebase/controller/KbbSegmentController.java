package com.ruoyi.knowledgebase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.knowledgebase.domain.KbbSegment;
import com.ruoyi.knowledgebase.service.IKbbSegmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件分段Controller
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@RestController
@RequestMapping("/knowledgebase/segment")
public class KbbSegmentController extends BaseController
{
    @Autowired
    private IKbbSegmentService kbbSegmentService;

    /**
     * 查询文件分段列表
     */
    @RequiresPermissions("knowledgebase:segment:list")
    @GetMapping("/list")
    public TableDataInfo list(KbbSegment kbbSegment)
    {
        startPage();
        List<KbbSegment> list = kbbSegmentService.selectKbbSegmentList(kbbSegment);
        return getDataTable(list);
    }

    /**
     * 导出文件分段列表
     */
    @RequiresPermissions("knowledgebase:segment:export")
    @Log(title = "文件分段", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KbbSegment kbbSegment)
    {
        List<KbbSegment> list = kbbSegmentService.selectKbbSegmentList(kbbSegment);
        ExcelUtil<KbbSegment> util = new ExcelUtil<KbbSegment>(KbbSegment.class);
        util.exportExcel(response, list, "文件分段数据");
    }

    /**
     * 获取文件分段详细信息
     */
    @RequiresPermissions("knowledgebase:segment:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(kbbSegmentService.selectKbbSegmentById(id));
    }

    /**
     * 新增文件分段
     */
    @RequiresPermissions("knowledgebase:segment:add")
    @Log(title = "文件分段", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KbbSegment kbbSegment)
    {
        return toAjax(kbbSegmentService.insertKbbSegment(kbbSegment));
    }

    /**
     * 修改文件分段
     */
    @RequiresPermissions("knowledgebase:segment:edit")
    @Log(title = "文件分段", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KbbSegment kbbSegment)
    {
        return toAjax(kbbSegmentService.updateKbbSegment(kbbSegment));
    }

    /**
     * 删除文件分段
     */
    @RequiresPermissions("knowledgebase:segment:remove")
    @Log(title = "文件分段", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kbbSegmentService.deleteKbbSegmentByIds(ids));
    }
}
