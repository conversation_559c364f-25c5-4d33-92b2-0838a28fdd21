package com.ruoyi.knowledgebase.controller;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableSupport;
import com.ruoyi.common.entity.domain.SysAdminUser;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeBase;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeUser;
import com.ruoyi.knowledgebase.domain.dto.KbbKnowledgeBaseDto;
import com.ruoyi.knowledgebase.domain.vo.KbbKnowledgeBaseVo;
import com.ruoyi.knowledgebase.service.IKbbKnowledgeBaseService;
import com.ruoyi.knowledgebase.service.IKbbKnowledgeUserService;
import com.ruoyi.knowledgebase.utils.KnowledgeAgentApiUtil;
import com.ruoyi.system.service.ISysAdminUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识库Controller
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Slf4j
@RestController
@RequestMapping("/knowledgebase/base")
public class KbbKnowledgeBaseController extends BaseController
{
    @Autowired
    private IKbbKnowledgeBaseService kbbKnowledgeBaseService;

    @Autowired
    private IKbbKnowledgeUserService kbbKnowledgeUserService;

    @Autowired
    private ISysAdminUserService sysAdminUserService;

    @Autowired
    private KnowledgeAgentApiUtil knowledgeAgentApiUtil;

    /**
     * 查询知识库列表
     */
    @RequiresPermissions("knowledgebase:base:list")
    @GetMapping("/list")
    public AjaxResult list(KbbKnowledgeBaseDto kbbKnowledgeBaseDto)
    {
        // 设置分页参数
        startPage();

        // 如果未指定归属类型，默认为0（查询当前用户创建的和被授权的知识库）
        if (kbbKnowledgeBaseDto.getOwnershipType() == null) {
            kbbKnowledgeBaseDto.setOwnershipType(0);
        }

        // 如果未指定排序规则，默认为按名称排序(0)
        if (kbbKnowledgeBaseDto.getSortType() == null) {
            kbbKnowledgeBaseDto.setSortType(0);
        }

        // 设置当前用户ID，用于查询条件
        kbbKnowledgeBaseDto.setUserId(SecurityUtils.getUserId());

        // 查询数据
        List<KbbKnowledgeBaseVo> list = kbbKnowledgeBaseService.selectKbbKnowledgeBaseList(kbbKnowledgeBaseDto);

        // 获取总记录数
        int total = kbbKnowledgeBaseService.selectKbbKnowledgeBaseCount(kbbKnowledgeBaseDto);

        // 返回分页数据
        Map<String, Integer> pageMap = new HashMap<>();
        pageMap.put("total", total);
        pageMap.put("pageSize", TableSupport.buildPageRequest().getPageSize());
        pageMap.put("pageNum", TableSupport.buildPageRequest().getPageNum());
        return getNewDataTable(list, pageMap);
    }

    /**
     * 指派知识库给用户
     */
    @RequiresPermissions("knowledgebase:base:assign")
    @Log(title = "知识库", businessType = BusinessType.UPDATE)
    @PostMapping("/assigned")
    public AjaxResult assigned(@RequestBody KbbKnowledgeBaseDto kbbKnowledgeBaseDto)
    {
        // 检查知识库是否存在
        KbbKnowledgeBase knowledgeBase = kbbKnowledgeBaseService.selectKbbKnowledgeBaseById(kbbKnowledgeBaseDto.getId());
        if (knowledgeBase == null) {
            return AjaxResult.error("知识库不存在");
        }

        // 检查当前用户是否有权限修改该知识库
        if (!knowledgeBase.getUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("您没有权限修改该知识库");
        }

        // 2. 删除原有的用户关联
        kbbKnowledgeUserService.deleteKbbKnowledgeUserByKbId(kbbKnowledgeBaseDto.getId());

        // 3. 创建新的用户关联
        if (kbbKnowledgeBaseDto.getUserIds() != null && kbbKnowledgeBaseDto.getUserIds().length > 0)
        {
            // 更新指派人数
            kbbKnowledgeBaseService.incrementAssignCount(kbbKnowledgeBaseDto.getId(), kbbKnowledgeBaseDto.getUserIds().length);

            for (Long userId : kbbKnowledgeBaseDto.getUserIds())
            {
                KbbKnowledgeUser kbbKnowledgeUser = new KbbKnowledgeUser();
                kbbKnowledgeUser.setKbId(kbbKnowledgeBaseDto.getId());
                kbbKnowledgeUser.setUserId(userId);
                kbbKnowledgeUser.setCreateTime(DateUtils.getNowDate());
                kbbKnowledgeUserService.insertKbbKnowledgeUser(kbbKnowledgeUser);
            }
        }else{
            // 更新指派人数
            kbbKnowledgeBaseService.incrementAssignCount(kbbKnowledgeBaseDto.getId(), 0);
        }

        return AjaxResult.success();
    }

    /**
     * 导出知识库列表
     */
    @RequiresPermissions("knowledgebase:base:export")
    @Log(title = "知识库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KbbKnowledgeBaseDto kbbKnowledgeBaseDto)
    {
        // 设置当前用户ID，只导出当前用户的知识库
        kbbKnowledgeBaseDto.setUserId(SecurityUtils.getUserId());
        List<KbbKnowledgeBaseVo> list = kbbKnowledgeBaseService.selectKbbKnowledgeBaseList(kbbKnowledgeBaseDto);
        ExcelUtil<KbbKnowledgeBaseVo> util = new ExcelUtil<KbbKnowledgeBaseVo>(KbbKnowledgeBaseVo.class);
        util.exportExcel(response, list, "知识库数据");
    }

    /**
     * 获取知识库详细信息
     */
    @RequiresPermissions("knowledgebase:base:list")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        KbbKnowledgeBase kbbKnowledgeBase = kbbKnowledgeBaseService.selectKbbKnowledgeBaseById(id);
        if (kbbKnowledgeBase == null) {
            return AjaxResult.error("知识库不存在");
        }

        // 检查当前用户是否有权限访问该知识库
        Long currentUserId = SecurityUtils.getUserId();
        if (!kbbKnowledgeBase.getUserId().equals(currentUserId)) {
            // 检查用户是否被授权访问
            KbbKnowledgeUser query = new KbbKnowledgeUser();
            query.setKbId(id);
            query.setUserId(currentUserId);
            List<KbbKnowledgeUser> userList = kbbKnowledgeUserService.selectKbbKnowledgeUserList(query);
            if (userList.isEmpty()) {
                return AjaxResult.error("您无权访问此知识库");
            }
        }

        // 获取创建者的用户信息
        if (kbbKnowledgeBase.getUserId() != null) {
            List<Long> userIds = new ArrayList<>();
            userIds.add(kbbKnowledgeBase.getUserId());
            List<SysAdminUser> userList = sysAdminUserService.selectUserByIds(userIds);
            if (!userList.isEmpty()) {
                kbbKnowledgeBase.setCreateBy(userList.get(0).getNickName());
            }
        }

        return AjaxResult.success(kbbKnowledgeBase);
    }

    /**
     * 新增知识库
     */
    @RequiresPermissions("knowledgebase:base:add")
    @Log(title = "知识库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KbbKnowledgeBaseDto kbbKnowledgeBaseDto)
    {
        // 验证知识库名称不能为空
        if (kbbKnowledgeBaseDto.getKbName() == null || kbbKnowledgeBaseDto.getKbName().trim().isEmpty()) {
            return AjaxResult.error("知识库名称不能为空");
        }

        try {
            Long id = kbbKnowledgeBaseService.insertKbbKnowledgeBase(kbbKnowledgeBaseDto);
            return AjaxResult.success(id);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改知识库
     */
    @RequiresPermissions("knowledgebase:base:edit")
    @Log(title = "知识库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KbbKnowledgeBaseDto kbbKnowledgeBaseDto)
    {
        // 检查当前用户是否有权限修改该知识库
        KbbKnowledgeBase existingKb = kbbKnowledgeBaseService.selectKbbKnowledgeBaseById(kbbKnowledgeBaseDto.getId());
        if (existingKb == null) {
            return AjaxResult.error("知识库不存在");
        }

        if (!existingKb.getUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("您没有权限修改该知识库");
        }

        return toAjax(kbbKnowledgeBaseService.updateKbbKnowledgeBase(kbbKnowledgeBaseDto));
    }

    /**
     * 删除知识库
     */
    @RequiresPermissions("knowledgebase:base:remove")
    @Log(title = "知识库", businessType = BusinessType.DELETE)
    @Transactional
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 检查当前用户是否有权限删除这些知识库
        int fileCount = 0;
        for (Long id : ids) {
            KbbKnowledgeBase existingKb = kbbKnowledgeBaseService.selectKbbKnowledgeBaseById(id);
            if (existingKb != null && !existingKb.getUserId().equals(SecurityUtils.getUserId())) {
                return AjaxResult.error("您没有权限删除ID为 " + id + " 的知识库");
            }
            // 检查是否为默认知识库
            if (existingKb != null && "1".equals(existingKb.getIsDefault())) {
                return AjaxResult.error("不能删除默认知识库");
            }
            fileCount += existingKb.getFileCount();
        }

        // 删除知识库授权用户关系
        for (Long id : ids) {
            kbbKnowledgeUserService.deleteKbbKnowledgeUserByKbId(id);
        }
        // 1. 获取所有文件的documentId
        List<String> kbIds = new ArrayList<>();
        for (Long id : ids) {
            kbIds.add(id.toString());
        }
        int row = kbbKnowledgeBaseService.deleteKbbKnowledgeBaseByIds(ids);
        if (!kbIds.isEmpty()) {
            String kbIdsStr = String.join(",", kbIds);
            int deleteRow = knowledgeAgentApiUtil.deleteFileFromKb(null, kbIdsStr);
            // 判断删除文件数量跟实际数量是否一致
            if (deleteRow != fileCount) {
                logger.info("删除Agent文档失败，预期删除" + kbIds.size() + "个，实际删除" + deleteRow + "个");
                throw new ServiceException("当前知识库中存在解析中的文件");
            }
        }

        // 删除知识库
        return toAjax(row);
    }

    /**
     * 获取知识库已指派人员ID列表
     */
    @RequiresPermissions("knowledgebase:base:assign")
    @GetMapping("/assignedUsers/{kbId}")
    public AjaxResult getAssignedUsers(@PathVariable("kbId") Long kbId)
    {
        // 检查知识库是否存在
        KbbKnowledgeBase knowledgeBase = kbbKnowledgeBaseService.selectKbbKnowledgeBaseById(kbId);
        if (knowledgeBase == null) {
            return AjaxResult.error("知识库不存在");
        }

        // 检查当前用户是否有权限访问该知识库
        if (!knowledgeBase.getUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("您没有权限访问该知识库");
        }

        // 查询已指派的用户ID列表
        KbbKnowledgeUser query = new KbbKnowledgeUser();
        query.setKbId(kbId);
        List<KbbKnowledgeUser> userList = kbbKnowledgeUserService.selectKbbKnowledgeUserList(query);

        // 提取用户ID列表
        List<Long> userIds = userList.stream()
            .map(KbbKnowledgeUser::getUserId)
            .collect(java.util.stream.Collectors.toList());

        if (userIds.isEmpty()) {
            return AjaxResult.success(new ArrayList<>());
        }

        // 调用用户服务获取用户信息
        List<SysAdminUser> userInfoList = sysAdminUserService.selectUserByIds(userIds);

        // 转换为前端需要的格式
        List<Map<String, Object>> result = userInfoList.stream()
            .map(user -> {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("userId", user.getUserId());
                userInfo.put("nickName", user.getNickName());
                return userInfo;
            })
            .collect(java.util.stream.Collectors.toList());

        return AjaxResult.success(result);
    }

    /**
     * 处理用户删除时的知识库相关操作
     */
    @InnerAuth
    @DeleteMapping("/deleteByUserId/{userId}")
    public AjaxResult deleteByUserId(@PathVariable("userId") Long userId) {
        // 1. 查询用户创建的知识库列表
        KbbKnowledgeBaseDto knowledgeBaseDto = new KbbKnowledgeBaseDto();
        // 只查userId创建的知识库
        knowledgeBaseDto.setOwnershipType(1);
        knowledgeBaseDto.setUserId(userId);
        List<KbbKnowledgeBaseVo> userKbs = kbbKnowledgeBaseService.selectKbbKnowledgeBaseList(knowledgeBaseDto);
        if (!userKbs.isEmpty()) {
            // 2. 对用户创建的知识库进行逻辑删除
            for (KbbKnowledgeBase kb : userKbs) {
                kb.setDelFlag("2");
                kb.setUpdateBy(SecurityUtils.getUsername());
                kb.setUpdateTime(DateUtils.getNowDate());
                kbbKnowledgeBaseService.deleteKbbKnowledgeBase(kb);
            }
        }

        // 3. 删除用户的知识库授权关系（物理删除）
        kbbKnowledgeUserService.deleteKbbKnowledgeUserByUserId(userId);

        return AjaxResult.success();
    }

    /**
     * 根据用户Id查询所有可用知识库
     */
    @GetMapping("/getIdsByUserId/{userId}")
    public AjaxResult getKbIdsByUserId(@PathVariable Long userId) {
        KbbKnowledgeBaseDto knowledgeBase = new KbbKnowledgeBaseDto();
        // 只查正常状态的知识库
        // 如果未指定归属类型，默认为1（查询当前用户创建的知识库）
        if (knowledgeBase.getOwnershipType() == null) {
            knowledgeBase.setOwnershipType(1);
        }

        // 如果未指定排序规则，默认为按名称排序(0)
        if (knowledgeBase.getSortType() == null) {
            knowledgeBase.setSortType(0);
        }

        // 设置当前用户ID，用于查询条件
        knowledgeBase.setUserId(userId);
        List<KbbKnowledgeBaseVo> kbbKnowledgeBaseList = kbbKnowledgeBaseService.selectKbbKnowledgeBaseList(knowledgeBase);
        List<Long> kbIds = kbbKnowledgeBaseList.stream().map(KbbKnowledgeBaseVo::getId).collect(Collectors.toList());
        String ids = StringUtils.join(kbIds, ",");
        Map<String, Object> map = new HashMap<>();
        map.put("ids",ids);
        return AjaxResult.success(map);
    }

    /**
     * 查询用户默认知识库ID，如果不存在则创建
     */
    @GetMapping("/getDefaultKnowledgeBaseId")
    public AjaxResult getDefaultKnowledgeBaseId() {
        try {
            // 从SecurityUtils中获取当前用户ID
            Long userId = SecurityUtils.getUserId();
            
            // 使用Service方法获取或创建默认知识库
            Long kbId = kbbKnowledgeBaseService.getOrCreateDefaultKnowledgeBase(userId);
            return AjaxResult.success(kbId);
        } catch (Exception e) {
            log.error("获取用户默认知识库ID失败，用户ID: {}", SecurityUtils.getUserId(), e);
            return AjaxResult.error("获取默认知识库失败: " + e.getMessage());
        }
    }

}
