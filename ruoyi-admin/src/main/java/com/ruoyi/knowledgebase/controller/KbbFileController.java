package com.ruoyi.knowledgebase.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.utils.agent.FileInfo;
import com.ruoyi.common.core.utils.agent.FileTrunkQuery;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableSupport;
import com.ruoyi.common.entity.domain.SysFiles;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.file.domain.UploadResult;
import com.ruoyi.file.service.ISysFileService;
import com.ruoyi.knowledgebase.domain.KbbFile;
import com.ruoyi.knowledgebase.domain.vo.KbbFileVo;
import com.ruoyi.knowledgebase.service.IKbbFileService;
import com.ruoyi.knowledgebase.service.IKbbKnowledgeBaseService;
import com.ruoyi.knowledgebase.utils.KnowledgeAgentApiUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识库文件Controller
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@RestController
@RequestMapping("/knowledgebase/file")
public class KbbFileController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(KbbFileController.class);

    @Autowired
    private IKbbFileService kbbFileService;

    @Autowired
    private ISysFileService fileService;

    @Autowired
    private KnowledgeAgentApiUtil knowledgeAgentApiUtil;

    @Autowired
    private IKbbKnowledgeBaseService kbbKnowledgeBaseService;

    /**
     * 查询知识库文件列表
     */
    @RequiresPermissions("knowledgebase:file:list")
    @GetMapping("/list")
    public AjaxResult list(KbbFile kbbFile) {
        // 设置分页参数
        startPage();

        // 查询数据
        List<KbbFile> list = kbbFileService.selectKbbFileList(kbbFile);

        // 转换为VO
        List<KbbFileVo> voList = list.stream().map(file -> {
            KbbFileVo vo = new KbbFileVo();
            // 复制基本属性
            vo.setId(file.getId());
            vo.setKbId(file.getKbId());
            vo.setFileName(file.getFileName());
            vo.setFileUrl(file.getFileUrl());
            vo.setFileType(file.getFileType());
            vo.setFileSize(file.getFileSize());
            vo.setDocumentId(file.getDocumentId());
            vo.setSegmentCount(file.getSegmentCount());
            vo.setStatus(file.getStatus());
            vo.setIsAvailable(file.getIsAvailable());
            vo.setIsDownload(file.getIsDownload());
            vo.setOperator(file.getOperator());
            vo.setUploadTime(file.getUploadTime());
            return vo;
        }).collect(Collectors.toList());

        // 获取总记录数
        int total = kbbFileService.selectKbbFileCount(kbbFile);

        // 返回分页数据
        Map<String, Integer> pageMap = new HashMap<>();
        pageMap.put("total", total);
        pageMap.put("pageSize", TableSupport.buildPageRequest().getPageSize());
        pageMap.put("pageNum", TableSupport.buildPageRequest().getPageNum());
        return getNewDataTable(voList, pageMap);
    }

    /**
     * 导出知识库文件列表
     */
    @RequiresPermissions("knowledgebase:file:export")
    @Log(title = "知识库文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KbbFile kbbFile) {
        List<KbbFile> list = kbbFileService.selectKbbFileList(kbbFile);
        ExcelUtil<KbbFile> util = new ExcelUtil<KbbFile>(KbbFile.class);
        util.exportExcel(response, list, "知识库文件数据");
    }

    /**
     * 获取知识库文件详细信息
     */
    @RequiresPermissions("knowledgebase:file:list")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        // 获取文件基本信息
        KbbFile kbbFile = kbbFileService.selectKbbFileById(id);
        if (kbbFile == null) {
            return AjaxResult.error("文件不存在");
        }

        return AjaxResult.success(kbbFile);
    }

    /**
     * 新增知识库文件
     */
    @RequiresPermissions("knowledgebase:file:add")
    @Log(title = "知识库文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KbbFile kbbFile) {
        return toAjax(kbbFileService.insertKbbFile(kbbFile));
    }

    /**
     * 批量添加知识库文件
     */
    @RequiresPermissions("knowledgebase:file:add")
    @Log(title = "知识库文件", businessType = BusinessType.INSERT)
    @PostMapping(value = "/batchAdd/{kbId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult batchAdd(@PathVariable("kbId") Long kbId,
                               @RequestPart("files") MultipartFile[] files,
                               @RequestParam(value = "isDownloadMap", required = false) String isDownloadMapJson,
                               @RequestParam(value = "filesName", required = false) String filesNameJson) {
        try {
            log.info("开始批量添加知识库文件，文件数量: {}, 知识库ID: {}", files.length, kbId);

            // 解析下载权限配置
            Map<String, String> isDownloadMap = new HashMap<>();
            if (isDownloadMapJson != null && !isDownloadMapJson.trim().isEmpty()) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    isDownloadMap = mapper.readValue(isDownloadMapJson,
                            new TypeReference<Map<String, String>>() {
                            });
                    // 验证和标准化值
                    for (Map.Entry<String, String> entry : isDownloadMap.entrySet()) {
                        // 确保值为 "0" 或 "1"
                        entry.setValue("1".equals(entry.getValue()) ? "1" : "0");
                    }
                } catch (Exception e) {
                    log.warn("解析isDownloadMap失败: {}, 将使用默认值", e.getMessage());
                    isDownloadMap = new HashMap<>();
                }
            }

            // 解析文件重命名配置
            Map<String, String> filesNameMap = new HashMap<>();
            if (filesNameJson != null && !filesNameJson.trim().isEmpty()) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    filesNameMap = mapper.readValue(filesNameJson,
                            new TypeReference<Map<String, String>>() {
                            });
                } catch (Exception e) {
                    log.warn("解析filesNameMap失败: {}, 将使用原始文件名", e.getMessage());
                    filesNameMap = new HashMap<>();
                }
            }

            // 调用文件服务上传文件
            List<UploadResult> uploadResults = fileService.uploadFiles(files);
            if (uploadResults == null || uploadResults.isEmpty()) {
                return AjaxResult.error("文件上传失败");
            }

            List<SysFiles> sysFiles = new ArrayList<>();
            List<FileInfo> fileInfoList = new ArrayList<>();

            for (UploadResult file : uploadResults) {
                String originalFileName = file.getFileName();
                String fileType = file.getFileType().toLowerCase();
                String fullFileName = originalFileName + "." + fileType;

                // 获取下载权限，优先使用传入的配置，否则使用默认值
                String isDownload = isDownloadMap.getOrDefault(fullFileName, "0");

                // 获取新文件名，如果有的话
                String newFileName = filesNameMap.get(fullFileName);
                String finalFileName = newFileName != null ? newFileName : originalFileName;

                // 创建 SysFiles 对象
                SysFiles sysFile = new SysFiles();
                sysFile.setName(finalFileName);
                sysFile.setUrl(file.getUrl());
                sysFile.setFileSize(file.getTotalSize());
                sysFile.setFileType(fileType);
                sysFile.setIsDownload(isDownload);
                sysFiles.add(sysFile);

                // 创建 FileInfo 对象
                FileInfo fileInfo = FileInfo.buildFileInfo(
                        finalFileName,
                        sysFile.getUrl(),
                        fileType,
                        "0".equals(isDownload) ? 0 : 1,  // 0-允许下载，1-不允许下载
                        sysFile.getFileSize(),
                        0,    // 默认状态
                        0  // 是否可用（0-可用 1-不可用）
                );
                fileInfoList.add(fileInfo);
            }

            // 调用知识库API添加文件
            List<Map<String, String>> fileIds = knowledgeAgentApiUtil.addFilesToKb(kbId, fileInfoList);

            // 更新文件的documentId并创建KbbFile对象
            if (fileIds != null && !fileIds.isEmpty()) {
                List<KbbFile> addedFiles = new ArrayList<>();

                for (int i = 0; i < sysFiles.size(); i++) {
                    SysFiles sysFile = sysFiles.get(i);

                    // 设置documentId
                    if (i < fileIds.size()) {
                        Map<String, String> fileId = fileIds.get(i);
                        sysFile.setDocumentId(fileId.get("file_id"));
                    }

                    // 创建知识库文件对象
                    KbbFile kbbFile = new KbbFile();
                    kbbFile.setKbId(kbId);
                    kbbFile.setFileName(sysFile.getName());
                    kbbFile.setFileUrl(sysFile.getUrl());
                    kbbFile.setDocumentId(sysFile.getDocumentId());
                    kbbFile.setFileSize(sysFile.getFileSize() != null ? sysFile.getFileSize() : 0L);
                    kbbFile.setFileType(sysFile.getFileType() != null ? sysFile.getFileType() : "unknown");
                    // 设置状态：如果有documentId则为未处理(0)，否则为失败(-1)
                    kbbFile.setStatus(sysFile.getDocumentId() != null ? "0" : "-1");
                    kbbFile.setIsAvailable("0"); // 可用
                    kbbFile.setIsDownload(sysFile.getIsDownload());
                    kbbFile.setUploadTime(new Date());

                    addedFiles.add(kbbFile);
                    log.info("成功添加知识库文件: {}", sysFile.getName());
                }

                // 获取最新的文件数量并更新
                int totalFiles = knowledgeAgentApiUtil.getKbFileList(kbId);
                if (totalFiles >= 0) {
                    kbbKnowledgeBaseService.decrementFileCount(kbId, totalFiles);
                }
                return AjaxResult.success(addedFiles);
            }
            return AjaxResult.error();
        } catch (Exception e) {
            log.error("批量添加知识库文件失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改知识库文件
     */
    @RequiresPermissions("knowledgebase:file:edit")
    @Log(title = "知识库文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KbbFile kbbFile) {
        return toAjax(kbbFileService.updateKbbFile(kbbFile));
    }

    /**
     * 修改知识库文件
     */
    @RequiresPermissions("knowledgebase:file:isDownload")
    @Log(title = "知识库文件", businessType = BusinessType.UPDATE)
    @PutMapping("/isDownload")
    public AjaxResult isDownload(@RequestBody KbbFile kbbFile) {
        return toAjax(kbbFileService.updateKbbFile(kbbFile));
    }

    /**
     * 修改知识库文件
     */
    @RequiresPermissions("knowledgebase:file:isEnable")
    @Log(title = "知识库文件", businessType = BusinessType.UPDATE)
    @PutMapping("/isEnable")
    public AjaxResult isEnable(@RequestBody KbbFile kbbFile) {
        return toAjax(kbbFileService.updateKbbFile(kbbFile));
    }

    /**
     * 删除知识库文件
     */
    @RequiresPermissions("knowledgebase:file:remove")
    @Log(title = "知识库文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public AjaxResult removeByFileIds(@RequestParam("fileIds") String fileIds, @RequestParam("kbId") Long kbId) {
        int row = 0;

        if (!fileIds.isEmpty()) {
            row = knowledgeAgentApiUtil.deleteFileFromKb(fileIds, null);
            if (row > 0) {
                // 获取最新的文件数量并更新
                int totalFiles = knowledgeAgentApiUtil.getKbFileList(kbId);
                if (totalFiles >= 0) {
                    kbbKnowledgeBaseService.decrementFileCount(kbId, totalFiles);
                }
            }
        }
        return row > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 获取文件分块列表
     */
    @RequiresPermissions("knowledgebase:file:trunk:query")
    @GetMapping("/trunks/{id}")
    public AjaxResult getFileTrunks(@PathVariable("id") Long id,
                                    @RequestParam(value = "trunkState", required = false) Integer trunkState,
                                    @RequestParam(value = "pageNum", required = false) Integer pageNum,
                                    @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        try {
            // 1. 获取文件信息
            KbbFile kbbFile = kbbFileService.selectKbbFileById(id);
            if (kbbFile == null) {
                return AjaxResult.error("文件不存在");
            }

            // 2. 检查文件是否有documentId
            if (kbbFile.getDocumentId() == null || kbbFile.getDocumentId().isEmpty()) {
                return AjaxResult.error("文件未完成解析，无法获取分块信息");
            }

            // 3. 构建查询参数，处理空值情况
            FileTrunkQuery query = FileTrunkQuery.build(
                    kbbFile.getDocumentId(),
                    trunkState != null ? trunkState.toString() : null,
                    pageNum != null ? pageNum.toString() : null,
                    pageSize != null ? pageSize.toString() : null
            );

            // 4. 调用Agent API获取分块列表
            Map<String, Object> result = knowledgeAgentApiUtil.getFileTrunkList(query);

            if (result == null) {
                return AjaxResult.error("获取文件分块列表失败");
            }

            return AjaxResult.success(result.get("data"));
        } catch (Exception e) {
            log.error("获取文件分块列表失败", e);
            return AjaxResult.error("获取文件分块列表失败: " + e.getMessage());
        }
    }

    /**
     * 重试解析文件
     */
    @RequiresPermissions("knowledgebase:file:retryParse")
    @Log(title = "知识库文件", businessType = BusinessType.UPDATE)
    @PostMapping("/retryParse/{id}")
    public AjaxResult retryParse(@PathVariable("id") Long id) {
        try {
            // 1. 获取文件信息
            KbbFile kbbFile = kbbFileService.selectKbbFileById(id);
            if (kbbFile == null) {
                return AjaxResult.error("文件不存在");
            }

            // 2. 检查文件是否有documentId
            if (kbbFile.getDocumentId() == null || kbbFile.getDocumentId().isEmpty()) {
                return AjaxResult.error("文件未完成上传，无法重试解析");
            }

            // 3. 调用Agent API重试解析
            Map<String, Object> result = knowledgeAgentApiUtil.retryParseFile(kbbFile.getDocumentId());

            if (result == null) {
                return AjaxResult.error("重试解析文件失败");
            }

            // 4. 更新文件状态
            kbbFile.setStatus("0"); // 设置为解析中状态
            kbbFileService.updateKbbFile(kbbFile);

            return AjaxResult.success();
        } catch (Exception e) {
            log.error("重试解析文件失败", e);
            return AjaxResult.error("重试解析文件失败: " + e.getMessage());
        }
    }
}
