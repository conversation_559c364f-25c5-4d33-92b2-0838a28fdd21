package com.ruoyi.knowledgebase.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.entity.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.knowledgebase.domain.KbbKnowledgeUser;
import com.ruoyi.knowledgebase.service.IKbbKnowledgeUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 知识库用户关联Controller
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@RestController
@RequestMapping("/knowledgebase/user")
public class KbbKnowledgeUserController extends BaseController
{
    @Autowired
    private IKbbKnowledgeUserService kbbKnowledgeUserService;

    /**
     * 查询知识库用户关联列表
     */
    @RequiresPermissions("knowledgebase:user:list")
    @GetMapping("/list")
    public TableDataInfo list(KbbKnowledgeUser kbbKnowledgeUser)
    {
        startPage();
        List<KbbKnowledgeUser> list = kbbKnowledgeUserService.selectKbbKnowledgeUserList(kbbKnowledgeUser);
        return getDataTable(list);
    }

    /**
     * 导出知识库用户关联列表
     */
    @RequiresPermissions("knowledgebase:user:export")
    @Log(title = "知识库用户关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KbbKnowledgeUser kbbKnowledgeUser)
    {
        List<KbbKnowledgeUser> list = kbbKnowledgeUserService.selectKbbKnowledgeUserList(kbbKnowledgeUser);
        ExcelUtil<KbbKnowledgeUser> util = new ExcelUtil<KbbKnowledgeUser>(KbbKnowledgeUser.class);
        util.exportExcel(response, list, "知识库用户关联数据");
    }

    /**
     * 获取知识库用户关联详细信息
     */
    @RequiresPermissions("knowledgebase:user:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(kbbKnowledgeUserService.selectKbbKnowledgeUserById(id));
    }

    /**
     * 新增知识库用户关联
     */
    @RequiresPermissions("knowledgebase:user:add")
    @Log(title = "知识库用户关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KbbKnowledgeUser kbbKnowledgeUser)
    {
        return toAjax(kbbKnowledgeUserService.insertKbbKnowledgeUser(kbbKnowledgeUser));
    }

    /**
     * 修改知识库用户关联
     */
    @RequiresPermissions("knowledgebase:user:edit")
    @Log(title = "知识库用户关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KbbKnowledgeUser kbbKnowledgeUser)
    {
        return toAjax(kbbKnowledgeUserService.updateKbbKnowledgeUser(kbbKnowledgeUser));
    }

    /**
     * 删除知识库用户关联
     */
    @RequiresPermissions("knowledgebase:user:remove")
    @Log(title = "知识库用户关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kbbKnowledgeUserService.deleteKbbKnowledgeUserByIds(ids));
    }
}
