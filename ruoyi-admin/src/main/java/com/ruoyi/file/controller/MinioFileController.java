package com.ruoyi.file.controller;

import com.ruoyi.common.entity.domain.file.SysPackZIPFile;
import com.ruoyi.file.config.MinioConfig;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@RestController
@RequestMapping("/file/packZip")
public class MinioFileController {

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private MinioClient client;

    /**
     * 下载多个文件转zip压缩包
     */
    @PostMapping("/minioDownloadZip")
    public void minioZip(@RequestBody List<SysPackZIPFile> fileMap, HttpServletResponse response) {
        // 设置响应头
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=esimp-minio.zip");
        // 创建ZIP输出流
        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            for (SysPackZIPFile zipFile : fileMap) {
                String fileName = zipFile.getOriginalName();
                String newFile = zipFile.getNewName();
                if (newFile == null) {
                    newFile = fileName;
                }
                // 获取每个文件的输入流
                try (InputStream inputStream = client.getObject(
                        GetObjectArgs.builder().bucket(minioConfig.getBucketName()).object(fileName).build())) {
                    // 创建ZIP文件条目
                    zipOut.putNextEntry(new ZipEntry(newFile));
                    // 将文件数据写入ZIP
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = inputStream.read(buffer)) >= 0) {
                        zipOut.write(buffer, 0, length);
                    }
                    zipOut.closeEntry();  // 结束该条目的写入
                }
            }
            zipOut.finish();  // 完成ZIP文件
        } catch (Exception e) {
            throw new RuntimeException("Error occurred while downloading files as zip", e);
        }
    }


}
