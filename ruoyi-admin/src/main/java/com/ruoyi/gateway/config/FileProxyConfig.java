package com.ruoyi.gateway.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件代理配置
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "file.proxy")
public class FileProxyConfig {
    
    /**
     * 文件上传最大大小（字节）
     */
    private long maxFileSize = 100 * 1024 * 1024; // 100MB
    
    /**
     * 请求最大大小（字节）
     */
    private long maxRequestSize = 200 * 1024 * 1024; // 200MB
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 60000; // 60秒
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 600000; // 10分钟
    
    /**
     * 是否启用文件代理
     */
    private boolean enabled = true;
    
    /**
     * 支持的文件类型
     */
    private String[] allowedFileTypes = {
        "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
        "txt", "md", "csv", "json", "xml", "html", "htm",
        "jpg", "jpeg", "png", "gif", "bmp", "svg",
        "mp3", "mp4", "avi", "mov", "wmv", "flv",
        "zip", "rar", "7z", "tar", "gz"
    };
    
    /**
     * 文件上传临时目录
     */
    private String tempDir = System.getProperty("java.io.tmpdir");
    
    /**
     * 是否启用文件类型检查
     */
    private boolean enableFileTypeCheck = true;
    
    /**
     * 是否启用文件大小检查
     */
    private boolean enableFileSizeCheck = true;

    public long getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public long getMaxRequestSize() {
        return maxRequestSize;
    }

    public void setMaxRequestSize(long maxRequestSize) {
        this.maxRequestSize = maxRequestSize;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String[] getAllowedFileTypes() {
        return allowedFileTypes;
    }

    public void setAllowedFileTypes(String[] allowedFileTypes) {
        this.allowedFileTypes = allowedFileTypes;
    }

    public String getTempDir() {
        return tempDir;
    }

    public void setTempDir(String tempDir) {
        this.tempDir = tempDir;
    }

    public boolean isEnableFileTypeCheck() {
        return enableFileTypeCheck;
    }

    public void setEnableFileTypeCheck(boolean enableFileTypeCheck) {
        this.enableFileTypeCheck = enableFileTypeCheck;
    }

    public boolean isEnableFileSizeCheck() {
        return enableFileSizeCheck;
    }

    public void setEnableFileSizeCheck(boolean enableFileSizeCheck) {
        this.enableFileSizeCheck = enableFileSizeCheck;
    }
}
