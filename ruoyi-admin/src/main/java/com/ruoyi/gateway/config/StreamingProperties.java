package com.ruoyi.gateway.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 流式响应配置属性
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "streaming")
public class StreamingProperties {
    
    /**
     * 最大响应时间（毫秒）- 0表示无限制
     */
    private long maxResponseTime = 0;
    
    /**
     * 缓冲区大小（字节）
     */
    private int bufferSize = 8192;
    
    // Getters and Setters
    public long getMaxResponseTime() {
        return maxResponseTime;
    }
    
    public void setMaxResponseTime(long maxResponseTime) {
        this.maxResponseTime = maxResponseTime;
    }
    
    public int getBufferSize() {
        return bufferSize;
    }
    
    public void setBufferSize(int bufferSize) {
        this.bufferSize = bufferSize;
    }
} 