package com.ruoyi.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.http.converter.support.AllEncompassingFormHttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.Duration;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * 服务代理配置
 * 包含Python服务和Agent服务的RestTemplate配置
 * 
 * <AUTHOR>
 */
@Configuration
public class ServiceProxyConfig {

    /**
     * 配置RestTemplate用于转发请求到Agent服务
     * 支持SSE流式响应和普通JSON响应
     */
    @Bean("agentRestTemplate")
    public RestTemplate agentRestTemplate(RestTemplateBuilder builder) {
        // 创建自定义的RequestFactory以支持更长的超时时间
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(60000); // 60秒连接超时
        requestFactory.setReadTimeout(1800000); // 30分钟读取超时，适合流式响应

        RestTemplate restTemplate = new RestTemplate(requestFactory);

        // 配置消息转换器以支持SSE流式响应
        configureMessageConverters(restTemplate);

        return restTemplate;
    }

    /**
     * 配置RestTemplate用于文件上传下载
     * 支持multipart/form-data和二进制流
     */
    @Bean("fileProxyRestTemplate")
    public RestTemplate fileProxyRestTemplate() {
        // 创建自定义的RequestFactory以支持文件传输
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(60000); // 60秒连接超时
        requestFactory.setReadTimeout(600000); // 10分钟读取超时，适合大文件传输
        requestFactory.setBufferRequestBody(false); // 禁用请求体缓冲，避免大文件内存溢出

        RestTemplate restTemplate = new RestTemplate(requestFactory);

        // 配置消息转换器以支持文件传输
        configureFileMessageConverters(restTemplate);

        return restTemplate;
    }
    
    /**
     * 配置Tomcat以支持更长的连接超时时间
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            factory.addConnectorCustomizers(connector -> {
                // 设置连接超时时间为60秒
                connector.setProperty("connectionTimeout", "60000");
                // 设置最大线程数为200
                connector.setProperty("maxThreads", "200");
                // 设置最小空闲线程数为10
                connector.setProperty("minSpareThreads", "10");
                // 设置最大连接数为8192
                connector.setProperty("maxConnections", "8192");
                // 设置接受连接队列大小
                connector.setProperty("acceptCount", "100");
                // 设置socket超时时间为10分钟
                connector.setProperty("socketTimeout", "600000");
                // 设置keep-alive超时时间为10分钟
                connector.setProperty("keepAliveTimeout", "600000");
                // 设置最大keep-alive请求数
                connector.setProperty("maxKeepAliveRequests", "1000");
                // 设置异步请求超时时间为30分钟
                connector.setProperty("asyncTimeout", "1800000");
            });
        };
    }
    
    /**
     * 配置消息转换器
     */
    private void configureMessageConverters(RestTemplate restTemplate) {
        // 清除默认的消息转换器
        restTemplate.getMessageConverters().clear();

        // 添加字符串消息转换器，支持text/event-stream
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        stringConverter.setSupportedMediaTypes(Arrays.asList(
            MediaType.TEXT_PLAIN,
            MediaType.TEXT_HTML,
            MediaType.TEXT_XML,
            MediaType.TEXT_EVENT_STREAM, // 支持SSE
            MediaType.APPLICATION_JSON,
            MediaType.APPLICATION_XML
        ));
        restTemplate.getMessageConverters().add(stringConverter);

        // 添加JSON消息转换器
        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
        jsonConverter.setSupportedMediaTypes(Arrays.asList(
            MediaType.APPLICATION_JSON
        ));
        restTemplate.getMessageConverters().add(jsonConverter);
    }

    /**
     * 配置文件传输消息转换器
     */
    private void configureFileMessageConverters(RestTemplate restTemplate) {
        // 清除默认的消息转换器
        restTemplate.getMessageConverters().clear();

        // 添加字节数组消息转换器，支持二进制数据 - 必须放在最前面
        ByteArrayHttpMessageConverter byteArrayConverter = new ByteArrayHttpMessageConverter();
        // 支持所有类型的二进制数据
        byteArrayConverter.setSupportedMediaTypes(Arrays.asList(
            MediaType.APPLICATION_OCTET_STREAM,
            MediaType.valueOf("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"), // Excel
            MediaType.valueOf("application/vnd.ms-excel"), // Excel
            MediaType.APPLICATION_PDF,
            MediaType.valueOf("application/msword"), // Word
            MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"), // Word
            MediaType.IMAGE_JPEG,
            MediaType.IMAGE_PNG,
            MediaType.IMAGE_GIF,
            MediaType.valueOf("application/zip"),
            MediaType.valueOf("application/x-rar-compressed"),
            MediaType.ALL // 支持所有类型作为字节数组
        ));
        restTemplate.getMessageConverters().add(byteArrayConverter);

        // 添加资源消息转换器，支持文件资源
        ResourceHttpMessageConverter resourceConverter = new ResourceHttpMessageConverter();
        restTemplate.getMessageConverters().add(resourceConverter);

        // 添加表单消息转换器，支持multipart/form-data
        AllEncompassingFormHttpMessageConverter formConverter = new AllEncompassingFormHttpMessageConverter();
        restTemplate.getMessageConverters().add(formConverter);

        // 添加字符串消息转换器
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        stringConverter.setSupportedMediaTypes(Arrays.asList(
            MediaType.TEXT_PLAIN,
            MediaType.APPLICATION_JSON,
            MediaType.APPLICATION_XML,
            MediaType.TEXT_HTML
        ));
        restTemplate.getMessageConverters().add(stringConverter);

        // 添加JSON消息转换器
        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
        restTemplate.getMessageConverters().add(jsonConverter);
    }
    
    /**
     * 配置异步请求支持
     */
    @Bean
    public WebMvcConfigurer asyncConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
                // 设置异步请求超时时间为30分钟
                configurer.setDefaultTimeout(1800000);
                // 设置任务执行器
                configurer.setTaskExecutor(null); // 使用默认执行器
            }
        };
    }
} 