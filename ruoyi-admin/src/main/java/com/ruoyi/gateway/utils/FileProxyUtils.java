package com.ruoyi.gateway.utils;

import com.ruoyi.gateway.config.FileProxyConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;

/**
 * 文件代理工具类
 * 
 * <AUTHOR>
 */
@Component
public class FileProxyUtils {
    
    private static final Logger log = LoggerFactory.getLogger(FileProxyUtils.class);
    
    @Autowired
    private FileProxyConfig fileProxyConfig;
    
    /**
     * 验证文件是否符合上传要求
     * 
     * @param file 要验证的文件
     * @return 验证结果
     */
    public FileValidationResult validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return FileValidationResult.error("文件不能为空");
        }
        
        // 检查文件大小
        if (fileProxyConfig.isEnableFileSizeCheck()) {
            if (file.getSize() > fileProxyConfig.getMaxFileSize()) {
                return FileValidationResult.error(
                    String.format("文件大小超过限制，最大允许 %d MB，当前文件 %.2f MB",
                        fileProxyConfig.getMaxFileSize() / 1024 / 1024,
                        file.getSize() / 1024.0 / 1024.0));
            }
        }
        
        // 检查文件类型
        if (fileProxyConfig.isEnableFileTypeCheck()) {
            String fileName = file.getOriginalFilename();
            if (fileName == null || !fileName.contains(".")) {
                return FileValidationResult.error("文件名无效或缺少扩展名");
            }
            
            String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            String[] allowedTypes = fileProxyConfig.getAllowedFileTypes();
            
            if (!Arrays.asList(allowedTypes).contains(fileExtension)) {
                return FileValidationResult.error(
                    String.format("不支持的文件类型：%s，支持的类型：%s", 
                        fileExtension, String.join(", ", allowedTypes)));
            }
        }
        
        return FileValidationResult.success();
    }
    
    /**
     * 验证多个文件
     * 
     * @param files 要验证的文件数组
     * @return 验证结果
     */
    public FileValidationResult validateFiles(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return FileValidationResult.error("文件列表不能为空");
        }
        
        long totalSize = 0;
        for (MultipartFile file : files) {
            // 验证单个文件
            FileValidationResult result = validateFile(file);
            if (!result.isValid()) {
                return result;
            }
            totalSize += file.getSize();
        }
        
        // 检查总大小
        if (fileProxyConfig.isEnableFileSizeCheck()) {
            if (totalSize > fileProxyConfig.getMaxRequestSize()) {
                return FileValidationResult.error(
                    String.format("文件总大小超过限制，最大允许 %d MB，当前总大小 %.2f MB",
                        fileProxyConfig.getMaxRequestSize() / 1024 / 1024,
                        totalSize / 1024.0 / 1024.0));
            }
        }
        
        return FileValidationResult.success();
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 文件扩展名（小写）
     */
    public String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }
    
    /**
     * 判断是否是图片文件
     * 
     * @param fileName 文件名
     * @return 是否是图片文件
     */
    public boolean isImageFile(String fileName) {
        String extension = getFileExtension(fileName);
        return Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "svg", "webp").contains(extension);
    }
    
    /**
     * 判断是否是文档文件
     * 
     * @param fileName 文件名
     * @return 是否是文档文件
     */
    public boolean isDocumentFile(String fileName) {
        String extension = getFileExtension(fileName);
        return Arrays.asList("pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "md").contains(extension);
    }
    
    /**
     * 判断是否是压缩文件
     * 
     * @param fileName 文件名
     * @return 是否是压缩文件
     */
    public boolean isArchiveFile(String fileName) {
        String extension = getFileExtension(fileName);
        return Arrays.asList("zip", "rar", "7z", "tar", "gz").contains(extension);
    }
    
    /**
     * 格式化文件大小
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的文件大小字符串
     */
    public String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / 1024.0 / 1024.0);
        } else {
            return String.format("%.2f GB", size / 1024.0 / 1024.0 / 1024.0);
        }
    }
    
    /**
     * 文件验证结果类
     */
    public static class FileValidationResult {
        private boolean valid;
        private String message;
        
        private FileValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public static FileValidationResult success() {
            return new FileValidationResult(true, null);
        }
        
        public static FileValidationResult error(String message) {
            return new FileValidationResult(false, message);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
    }
}
