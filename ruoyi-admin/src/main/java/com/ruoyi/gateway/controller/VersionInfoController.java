package com.ruoyi.gateway.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

@RestController
@RequestMapping("/version")
@Slf4j
public class VersionInfoController {

    private static final String FILE_NAME = "front_version.txt";

    @Value("${ruoyi.version}")
    private String version;

    // 后端版本查询
    @RequestMapping("")
    public String getVersionInfo() {
        log.info("获取版本信息:" + version);
        return version;
    }

    @RequestMapping("/front")
    public String getFrontVersionInfo() {
        log.info("获取前端版本信息");
        try {
            Path path = Paths.get(FILE_NAME);
            if (!Files.exists(path)) {
                return "暂无前端版本信息";
            }
            String version = Files.readAllLines(path, StandardCharsets.UTF_8)
                    .stream()
                    .findFirst()
                    .orElse("");
            log.info("获取前端版本信息成功: {}", version);
            return version;
        } catch (Exception e) {
            log.error("获取前端版本信息失败: {}", e.getMessage());
            return "获取前端版本信息失败";
        }
    }

    @RequestMapping("/addFront")
    public AjaxResult setFrontVersion(@RequestParam String version) {
        log.info("设置前端版本信息: {}", version);
        try {
            Files.write(Paths.get(FILE_NAME),
                    version.getBytes(StandardCharsets.UTF_8),
                    StandardOpenOption.CREATE,
                    StandardOpenOption.TRUNCATE_EXISTING);
            log.info("设置前端版本信息成功");
            return AjaxResult.success();
        } catch (Exception e) {
            log.error("设置前端版本信息失败: {}", e.getMessage());
            return AjaxResult.error("设置前端版本信息失败");
        }
    }
}
