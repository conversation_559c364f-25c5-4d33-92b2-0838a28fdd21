package com.ruoyi.gateway.controller;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.annotation.RequiresLogin;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.gateway.config.StreamingProperties;
import org.apache.catalina.connector.ClientAbortException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;

/**
 * Agent服务代理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agentService")
public class AgentServiceProxyController {

    private static final Logger log = LoggerFactory.getLogger(AgentServiceProxyController.class);

    @Autowired
    @Qualifier("agentRestTemplate")
    private RestTemplate agentRestTemplate;

    @Autowired
    @Qualifier("fileProxyRestTemplate")
    private RestTemplate fileProxyRestTemplate;

    @Value("${agent.base-url}")
    private String agentServiceUrl;

    @Autowired
    private StreamingProperties streamingProperties;

    /**
     * 转发GET请求到Agent服务
     * 需要登录权限
     */
    @GetMapping("/**")
    @RequiresLogin
    public ResponseEntity<Object> proxyGet(HttpServletRequest request, HttpServletResponse response) {
        log.info("通用GET请求代理 - 用户: {}, 路径: {}",
                SecurityUtils.getUsername(), request.getRequestURI());
        ResponseEntity<Object> result = proxyRequest(request, response, HttpMethod.GET, null);
        // 如果是文件下载请求，proxyRequest返回null，我们返回一个空的ResponseEntity
        return result != null ? result : ResponseEntity.ok().build();
    }

    /**
     * 知识库聊天接口 - 支持SSE流式响应
     */
    @PostMapping("/api/knowledge/chat")
    @RequiresLogin
    public ResponseEntity<StreamingResponseBody> proxyChat(HttpServletRequest request, @RequestBody(required = false) String body) {
        String requestId = generateRequestId();
        log.info("[{}] 知识库聊天接口被调用 - 用户: {}, 请求体大小: {} bytes",
                requestId, SecurityUtils.getUsername(), body != null ? body.getBytes().length : 0);

        // 记录请求体内容（调试用）
        if (body != null && log.isDebugEnabled()) {
            log.debug("[{}] 请求体内容: {}", requestId, body);
        }

        long startTime = System.currentTimeMillis();

        try {
            // 获取当前登录用户信息
            String currentUsername = SecurityUtils.getUsername();
            Long currentUserId = SecurityUtils.getUserId();

            // 获取请求路径和参数
            String requestUri = request.getRequestURI();
            String queryString = request.getQueryString();

            // 处理URL编码的查询参数
            if (queryString != null) {
                try {
                    // 对查询字符串进行URL解码
                    queryString = java.net.URLDecoder.decode(queryString, StandardCharsets.UTF_8);
                    log.debug("[{}] URL解码后的查询参数: {}", requestId, queryString);
                } catch (Exception e) {
                    log.warn("[{}] URL解码失败，使用原始查询参数: {}", requestId, e.getMessage());
                }
            }

            String fullUrl = requestUri + (queryString != null ? "?" + queryString : "");

            log.info("[{}] 开始转发聊天请求到Agent服务 - 用户: {}, 用户ID: {}, URL: {}",
                    requestId, currentUsername, currentUserId, fullUrl);

            // 构建目标URL
            String targetUrl = agentServiceUrl + requestUri;
            if (queryString != null) {
                targetUrl += "?" + queryString;
            }

            log.info("[{}] 转发聊天请求到Agent服务: {}", requestId, targetUrl);

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);

                // 跳过一些不需要转发的头部
                if (!shouldSkipHeader(headerName)) {
                    headers.add(headerName, headerValue);
                }
            }

            // 确保设置正确的Content-Type
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 添加代理标识头
            headers.add("X-Proxy-By", "SpringBoot");
            headers.add("X-Original-IP", getClientIpAddress(request));
            headers.add("X-Request-ID", requestId);

            // 添加用户信息头
            headers.add("X-User-Id", currentUserId.toString());
            headers.add("X-Username", currentUsername);

            // 创建流式响应体
            final String finalRequestId = requestId;
            final HttpHeaders finalHeaders = headers;
            final String finalBody = body;
            final String finalTargetUrl = targetUrl;

            StreamingResponseBody responseBody = outputStream -> {
                try {
                    // 使用 RestTemplate 执行请求并处理流式响应
                    agentRestTemplate.execute(
                            finalTargetUrl,
                            HttpMethod.POST,
                            clientRequest -> {
                                // 设置请求头
                                clientRequest.getHeaders().putAll(finalHeaders);
                                // 设置请求体
                                if (finalBody != null) {
                                    clientRequest.getBody().write(finalBody.getBytes(StandardCharsets.UTF_8));
                                }
                            },
                            (ClientHttpResponse clientHttpResponse) -> {
                                try (BufferedReader reader = new BufferedReader(
                                        new InputStreamReader(clientHttpResponse.getBody(), StandardCharsets.UTF_8))) {

                                    log.info("[{}] 开始处理Python服务的流式响应 - 状态码: {}",
                                            finalRequestId, clientHttpResponse.getStatusCode());

                                    String line;
                                    int lineCount = 0;

                                    while ((line = reader.readLine()) != null) {
                                        // 检查客户端连接状态
                                        if (Thread.currentThread().isInterrupted()) {
                                            log.warn("[{}] 线程被中断，停止流式响应处理", finalRequestId);
                                            break;
                                        }

                                        lineCount++;

                                        try {
                                            // 立即发送每一行数据，不进行缓冲
                                            outputStream.write(line.getBytes(StandardCharsets.UTF_8));
                                            outputStream.write("\n".getBytes(StandardCharsets.UTF_8));
                                            outputStream.flush(); // 立即刷新，确保数据实时发送
                                        } catch (IOException e) {
                                            // 检查是否是客户端断开连接
                                            if (isClientDisconnected(e)) {
                                                log.info("[{}] 客户端断开连接，停止流式响应处理", finalRequestId);
                                                break;
                                            } else {
                                                log.error("[{}] 发送数据到客户端失败: {}", finalRequestId, e.getMessage());
                                                throw e;
                                            }
                                        }

                                        // 每处理1000行记录一次日志
                                        if (lineCount % 1000 == 0) {
                                            log.info("[{}] 已处理 {} 行流式数据", finalRequestId, lineCount);
                                        }

                                        // 检查是否超过最大响应时间
                                        long currentTime = System.currentTimeMillis();
                                        if (streamingProperties.getMaxResponseTime() > 0 &&
                                                (currentTime - startTime) > streamingProperties.getMaxResponseTime()) {
                                            log.warn("[{}] 流式响应超时，强制结束 - 已处理 {} 行", finalRequestId, lineCount);
                                            break;
                                        }
                                    }

                                    // 最终刷新
                                    try {
                                        outputStream.flush();
                                        log.info("[{}] 流式响应处理完成 - 总共处理 {} 行数据", finalRequestId, lineCount);
                                    } catch (IOException e) {
                                        if (!isClientDisconnected(e)) {
                                            log.error("[{}] 最终刷新失败: {}", finalRequestId, e.getMessage());
                                        }
                                    }

                                } catch (IOException e) {
                                    if (isClientDisconnected(e)) {
                                        log.info("[{}] 客户端断开连接，流式响应处理结束", finalRequestId);
                                    } else {
                                        log.error("[{}] 流式响应处理失败: {}", finalRequestId, e.getMessage(), e);
                                        // 发送错误信息到客户端
                                        try {
                                            String errorData = "data: {\"error\": \"流式响应处理失败: " + e.getMessage() + "\"}\n\n";
                                            outputStream.write(errorData.getBytes(StandardCharsets.UTF_8));
                                            outputStream.flush();
                                        } catch (IOException ioException) {
                                            if (!isClientDisconnected(ioException)) {
                                                log.error("[{}] 发送错误信息到客户端失败: {}", finalRequestId, ioException.getMessage());
                                            }
                                        }
                                    }
                                }
                                return null;
                            }
                    );
                } catch (Exception e) {
                    log.error("[{}] 流式响应创建失败: {}", finalRequestId, e.getMessage(), e);
                    // 发送错误信息到客户端
                    try {
                        String errorData = "data: {\"error\": \"流式响应创建失败: " + e.getMessage() + "\"}\n\n";
                        outputStream.write(errorData.getBytes(StandardCharsets.UTF_8));
                        outputStream.flush();
                    } catch (IOException ioException) {
                        if (!isClientDisconnected(ioException)) {
                            log.error("[{}] 发送错误信息到客户端失败: {}", finalRequestId, ioException.getMessage());
                        }
                    }
                }
            };

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("[{}] Agent服务聊天流式响应创建成功 - 耗时: {}ms",
                    requestId, duration);

            // 返回流式响应
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_EVENT_STREAM_VALUE)
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .header("Connection", "keep-alive")
                    .header("Keep-Alive", "timeout=300, max=1000")
                    .header("X-Accel-Buffering", "no") // 禁用Nginx缓冲
                    .header("Transfer-Encoding", "chunked")
                    .body(responseBody);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.error("[{}] 转发聊天请求到Agent服务失败 - 耗时: {}ms, 错误: {}",
                    requestId, duration, e.getMessage(), e);

            String errorMessage = getErrorMessage(e);
            log.error("[{}] 聊天错误详情 - 类型: {}, 消息: {}",
                    requestId, e.getClass().getSimpleName(), errorMessage);

            // 返回错误响应
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_EVENT_STREAM_VALUE)
                    .body(outputStream -> {
                        try {
                            String errorData = "data: {\"error\": \"Agent服务暂时不可用: " + errorMessage + "\"}\n\n";
                            outputStream.write(errorData.getBytes(StandardCharsets.UTF_8));
                            outputStream.flush();
                        } catch (IOException ioException) {
                            log.error("[{}] 发送错误信息到客户端失败: {}", requestId, ioException.getMessage());
                        }
                    });
        }
    }

    /**
     * 转发POST请求到Agent服务
     * 需要登录权限
     */
    @PostMapping("/**")
    @RequiresLogin
    public ResponseEntity<Object> proxyPost(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) String body) {
        log.info("通用POST请求代理 - 用户: {}, 路径: {}, 请求体大小: {} bytes",
                SecurityUtils.getUsername(), request.getRequestURI(),
                body != null ? body.getBytes().length : 0);
        ResponseEntity<Object> result = proxyRequest(request, response, HttpMethod.POST, body);
        return result != null ? result : ResponseEntity.ok().build();
    }

    /**
     * 转发PUT请求到Agent服务
     * 需要登录权限
     */
    @PutMapping("/**")
    @RequiresLogin
    public ResponseEntity<Object> proxyPut(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) String body) {
        log.info("通用PUT请求代理 - 用户: {}, 路径: {}, 请求体大小: {} bytes",
                SecurityUtils.getUsername(), request.getRequestURI(),
                body != null ? body.getBytes().length : 0);
        ResponseEntity<Object> result = proxyRequest(request, response, HttpMethod.PUT, body);
        return result != null ? result : ResponseEntity.ok().build();
    }

    /**
     * 转发DELETE请求到Agent服务
     * 需要登录权限
     */
    @DeleteMapping("/**")
    @RequiresLogin
    public ResponseEntity<Object> proxyDelete(HttpServletRequest request, HttpServletResponse response) {
        log.info("通用DELETE请求代理 - 用户: {}, 路径: {}",
                SecurityUtils.getUsername(), request.getRequestURI());
        ResponseEntity<Object> result = proxyRequest(request, response, HttpMethod.DELETE, null);
        return result != null ? result : ResponseEntity.ok().build();
    }

    /**
     * 处理OPTIONS请求 - CORS预检请求
     * 不需要登录权限，让Spring Security的CORS配置处理
     */
    @RequestMapping(value = "/**", method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleOptions(HttpServletRequest request) {
        log.info("CORS预检请求 - 路径: {}, Origin: {}",
                request.getRequestURI(), request.getHeader("Origin"));

        // 返回空响应，让Spring Security的CORS配置添加必要的CORS头
        return ResponseEntity.ok().build();
    }

    /**
     * 知识库文件列表接口 - 需要特定权限
     */
    @GetMapping("/api/kbFile/file/list")
    @RequiresPermissions("knowledgebase:file:list")
    public ResponseEntity<Object> proxyKbFileList(HttpServletRequest request,
                                                  @RequestParam(value = "kbId", required = false) String kbId) {
        log.info("知识库文件列表接口被调用 - 用户: {}, 权限: knowledgebase:file:list, kbId: {}",
                SecurityUtils.getUsername(), kbId);

        // 验证必需参数
        if (StringUtils.isEmpty(kbId)) {
            log.warn("知识库文件列表接口调用失败 - 缺少必需参数kbId");
            return ResponseEntity.badRequest()
                    .body(AjaxResult.error("缺少必需参数：kbId"));
        }

        return proxyRequest(request, HttpMethod.GET, null);
    }

    /**
     * 添加文件到知识库接口 - 需要特定权限
     */
    @PostMapping("/api/kbFile/add")
    @RequiresPermissions("knowledgebase:file:add")
    public ResponseEntity<Object> proxyKbFileAdd(HttpServletRequest request, @RequestBody(required = false) String body) {
        log.info("添加文件到知识库接口被调用 - 用户: {}, 权限: knowledgebase:file:add, 请求体大小: {} bytes",
                SecurityUtils.getUsername(), body != null ? body.getBytes().length : 0);
        return proxyRequest(request, HttpMethod.POST, body);
    }

    /**
     * 从知识库删除文件接口 - 需要特定权限
     */
    @DeleteMapping("/api/kbFile/delete")
    @RequiresPermissions("knowledgebase:file:remove")
    public ResponseEntity<Object> proxyKbFileDelete(HttpServletRequest request) {
        log.info("从知识库删除文件接口被调用 - 用户: {}, 权限: knowledgebase:file:remove",
                SecurityUtils.getUsername());
        return proxyRequest(request, HttpMethod.DELETE, null);
    }

    /**
     * 获取文件信息接口 - 需要特定权限
     */
    @GetMapping("/api/kbFile/getFileInfo")
    @RequiresPermissions("knowledgebase:file:query")
    public ResponseEntity<Object> proxyKbFileInfo(HttpServletRequest request) {
        log.info("获取文件信息接口被调用 - 用户: {}, 权限: knowledgebase:file:query",
                SecurityUtils.getUsername());
        return proxyRequest(request, HttpMethod.GET, null);
    }

    /**
     * 人才信息改进接口 - 不需要登录权限（在安全白名单中）
     */
    @GetMapping("/api/talent/improveInfo")
    public ResponseEntity<Object> proxyTalentImproveInfo(HttpServletRequest request, @RequestBody(required = false) String body) {
        log.info("人才信息改进接口被调用 - 路径: {}, 请求体大小: {} bytes",
                request.getRequestURI(), body != null ? body.getBytes().length : 0);
        return proxyRequest(request, HttpMethod.GET, body);
    }

    /**
     * 通用请求转发方法
     */
    private ResponseEntity<Object> proxyRequest(HttpServletRequest request, HttpMethod method, String body) {
        return proxyRequest(request, null, method, body);
    }

    /**
     * 通用请求转发方法（支持响应写入）
     */
    private ResponseEntity<Object> proxyRequest(HttpServletRequest request, HttpServletResponse response, HttpMethod method, String body) {
        long startTime = System.currentTimeMillis();
        String requestId = generateRequestId();

        // 判断是否是文件相关请求，选择合适的RestTemplate
        boolean isFileRequest = isFileRelatedRequest(request);
        RestTemplate restTemplate = isFileRequest ? fileProxyRestTemplate : agentRestTemplate;

        // 判断是否是multipart请求
        boolean isMultipartRequest = request instanceof MultipartHttpServletRequest;

        try {
            // 安全获取当前登录用户信息（可能为空，用于白名单接口）
            String currentUsername = "anonymous";
            Long currentUserId = 0L;

            try {
                currentUsername = SecurityUtils.getUsername();
                currentUserId = SecurityUtils.getUserId();
            } catch (Exception e) {
                log.debug("[{}] 未获取到用户认证信息，使用匿名用户 - 错误: {}", requestId, e.getMessage());
            }

            // 如果是multipart请求，使用专门的处理逻辑
            if (isMultipartRequest) {
                return handleMultipartRequest(request, response, method, requestId, currentUsername, currentUserId);
            }

            // 获取请求路径和参数
            String requestUri = request.getRequestURI();
            String queryString = request.getQueryString();

            // 处理URL编码的查询参数
            if (queryString != null) {
                try {
                    // 对查询字符串进行URL解码
                    queryString = java.net.URLDecoder.decode(queryString, StandardCharsets.UTF_8);
                    log.debug("[{}] URL解码后的查询参数: {}", requestId, queryString);
                } catch (Exception e) {
                    log.warn("[{}] URL解码失败，使用原始查询参数: {}", requestId, e.getMessage());
                }
            }

            String fullUrl = requestUri + (queryString != null ? "?" + queryString : "");

            log.info("[{}] 开始转发请求到Agent服务 - 用户: {}, 用户ID: {}, 方法: {}, URL: {}",
                    requestId, currentUsername, currentUserId, method, fullUrl);

            // 获取请求路径
            // Agent服务期望的路径是 /agentService/api/xxx，所以不需要移除 /agentService 前缀
            String agentPath = requestUri;

            // 构建目标URL - Agent服务期望的路径是 /agentService/api/xxx
            String targetUrl = agentServiceUrl + agentPath;
            if (queryString != null) {
                targetUrl += "?" + queryString;
            }

            log.info("[{}] 转发请求到Agent服务: {} {}", requestId, method, targetUrl);

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            Enumeration<String> headerNames = request.getHeaderNames();
            int headerCount = 0;
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);

                // 跳过一些不需要转发的头部
                if (!shouldSkipHeader(headerName)) {
                    headers.add(headerName, headerValue);
                    headerCount++;
                }
            }

            // 添加代理标识头
            headers.add("X-Proxy-By", "SpringBoot");
            headers.add("X-Original-IP", getClientIpAddress(request));
            headers.add("X-Request-ID", requestId);

            // 添加用户信息头（用于Agent服务内部权限验证）
            headers.add("X-User-Id", currentUserId.toString());
            headers.add("X-Username", currentUsername);

            log.debug("[{}] 请求头信息 - 总数: {}, 用户ID: {}, 用户名: {}, 客户端IP: {}",
                    requestId, headerCount, currentUserId, currentUsername, getClientIpAddress(request));

            // 构建请求实体
            HttpEntity<String> entity = new HttpEntity<>(body, headers);

            // 记录请求体大小（如果有）
            if (body != null) {
                log.debug("[{}] 请求体大小: {} bytes", requestId, body.getBytes().length);
            }

            // 判断是否是下载请求
            boolean isDownloadRequest = isDownloadRequest(request, method);

            if (isDownloadRequest && response != null) {
                // 文件下载请求，直接写入响应流
                handleFileDownload(restTemplate, targetUrl, method, entity, response, requestId);
                return null; // 重要：返回null，表示响应已经直接写入
            } else {
                // 普通请求，返回字符串响应
                ResponseEntity<String> agentResponse = restTemplate.exchange(
                        targetUrl,
                        method,
                        entity,
                        String.class
                );

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                log.info("[{}] Agent服务响应成功 - 状态: {}, 耗时: {}ms",
                        requestId, agentResponse.getStatusCode(), duration);

                // 记录响应头信息
                if (log.isDebugEnabled()) {
                    HttpHeaders responseHeaders = agentResponse.getHeaders();
                    log.debug("[{}] 响应头信息 - 总数: {}", requestId, responseHeaders.size());
                    responseHeaders.forEach((key, values) ->
                            log.debug("[{}] 响应头 {}: {}", requestId, key, values));
                }

                return ResponseEntity.status(agentResponse.getStatusCode())
                        .headers(agentResponse.getHeaders())
                        .body(agentResponse.getBody());
            }

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.error("[{}] 转发请求到Agent服务失败 - 耗时: {}ms, 错误: {}",
                    requestId, duration, e.getMessage(), e);

            // 根据异常类型记录不同的错误信息
            String errorMessage = getErrorMessage(e);
            log.error("[{}] 错误详情 - 类型: {}, 消息: {}",
                    requestId, e.getClass().getSimpleName(), errorMessage);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(AjaxResult.error("Agent服务暂时不可用: " + errorMessage));
        }
    }

    /**
     * 处理流式响应相关的异常
     */
    @ExceptionHandler(AsyncRequestTimeoutException.class)
    public ResponseEntity<StreamingResponseBody> handleAsyncTimeout(AsyncRequestTimeoutException e, HttpServletRequest request) {
        String requestId = generateRequestId();
        log.warn("[{}] 异步请求超时: {}", requestId, e.getMessage());

        return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_EVENT_STREAM_VALUE)
                .body(outputStream -> {
                    try {
                        String errorData = "data: {\"error\": \"请求超时，请稍后重试\"}\n\n";
                        outputStream.write(errorData.getBytes(StandardCharsets.UTF_8));
                        outputStream.flush();
                    } catch (IOException ioException) {
                        log.error("[{}] 发送超时错误信息失败: {}", requestId, ioException.getMessage());
                    }
                });
    }

    /**
     * 处理客户端断开连接的异常
     */
    @ExceptionHandler({ClientAbortException.class, IOException.class})
    public ResponseEntity<Void> handleClientDisconnection(Exception e, HttpServletRequest request) {
        String requestId = generateRequestId();
        log.info("[{}] 客户端断开连接: {}", requestId, e.getMessage());

        // 对于客户端断开连接，直接返回空响应
        return ResponseEntity.ok().build();
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "REQ_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 获取友好的错误信息
     */
    private String getErrorMessage(Exception e) {
        if (e instanceof org.springframework.web.client.ResourceAccessException) {
            return "Agent服务连接失败，请检查服务是否启动";
        } else if (e instanceof org.springframework.web.client.HttpClientErrorException) {
            org.springframework.web.client.HttpClientErrorException httpEx =
                    (org.springframework.web.client.HttpClientErrorException) e;
            return "Agent服务返回错误: " + httpEx.getStatusCode() + " - " + httpEx.getStatusText();
        } else if (e instanceof org.springframework.web.client.HttpServerErrorException) {
            org.springframework.web.client.HttpServerErrorException httpEx =
                    (org.springframework.web.client.HttpServerErrorException) e;
            return "Agent服务内部错误: " + httpEx.getStatusCode() + " - " + httpEx.getStatusText();
        } else if (e instanceof java.net.ConnectException) {
            return "无法连接到Agent服务，请检查网络连接和服务状态";
        } else if (e instanceof java.net.SocketTimeoutException) {
            return "Agent服务响应超时，请稍后重试";
        } else {
            return e.getMessage();
        }
    }

    /**
     * 判断是否跳过某个请求头
     */
    private boolean shouldSkipHeader(String headerName) {
        String lowerHeaderName = headerName.toLowerCase();
        return lowerHeaderName.equals("host") ||
                lowerHeaderName.equals("content-length") ||
                lowerHeaderName.startsWith("x-forwarded-");
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headers = {
                "X-Forwarded-For",
                "X-Real-IP",
                "X-Client-IP",
                "Proxy-Client-IP",
                "WL-Proxy-Client-IP"
        };

        for (String header : headers) {
            String ip = request.getHeader(header);
            if (StringUtils.isNotEmpty(ip) && !"unknown".equalsIgnoreCase(ip)) {
                return ip.split(",")[0].trim();
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * 检查是否是客户端断开连接异常
     */
    private boolean isClientDisconnected(IOException e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }

        return message.contains("Broken pipe") ||
                message.contains("Connection reset") ||
                message.contains("The current thread was interrupted") ||
                message.contains("Failed write") ||
                message.contains("Connection timed out") ||
                message.contains("Connection refused") ||
                e instanceof java.net.SocketException ||
                e instanceof org.apache.catalina.connector.ClientAbortException;
    }

    /**
     * 判断是否是文件相关请求
     * 文件相关请求需要使用专门的RestTemplate来处理multipart/form-data和二进制流
     */
    private boolean isFileRelatedRequest(HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String contentType = request.getContentType();

        // 检查Content-Type是否是multipart/form-data
        boolean isMultipartRequest = contentType != null &&
                contentType.toLowerCase().contains("multipart/form-data");

        // 检查是否是二进制流请求
        boolean isBinaryRequest = contentType != null &&
                (contentType.toLowerCase().contains("application/octet-stream") ||
                        contentType.toLowerCase().contains("application/pdf") ||
                        contentType.toLowerCase().contains("application/vnd.ms-excel") ||
                        contentType.toLowerCase().contains("application/vnd.openxmlformats"));

        boolean isFileRequest = isMultipartRequest || isBinaryRequest;

        if (isFileRequest) {
            log.debug("检测到文件相关请求 - URI: {}, Content-Type: {}, 使用文件代理RestTemplate",
                    requestUri, contentType);
        }

        return isFileRequest;
    }

    /**
     * 判断是否是下载请求
     */
    private boolean isDownloadRequest(HttpServletRequest request, HttpMethod method) {
        if (method != HttpMethod.GET && method != HttpMethod.POST) {
            return false;
        }

        String requestUri = request.getRequestURI().toLowerCase();
        return requestUri.contains("/download") ||
                requestUri.contains("/export") ||
                requestUri.contains("template") ||
                requestUri.endsWith(".pdf") ||
                requestUri.endsWith(".xlsx") ||
                requestUri.endsWith(".docx") ||
                requestUri.endsWith(".zip");
    }

    /**
     * 处理文件下载请求
     */
    private void handleFileDownload(RestTemplate restTemplate, String targetUrl,
                                   HttpMethod method, HttpEntity<String> entity,
                                   HttpServletResponse response, String requestId) {
        try {
            // 创建专门的RestTemplate来处理文件下载
            RestTemplate fileDownloadTemplate = createFileDownloadRestTemplate();

            // 发送请求并获取字节数组响应
            ResponseEntity<byte[]> fileResponse = fileDownloadTemplate.exchange(
                    targetUrl,
                    method,
                    entity,
                    byte[].class
            );

            // 设置响应头
            HttpHeaders responseHeaders = fileResponse.getHeaders();
            if (responseHeaders.getContentType() != null) {
                response.setContentType(responseHeaders.getContentType().toString());
            }
            if (responseHeaders.getContentLength() > 0) {
                response.setContentLength((int) responseHeaders.getContentLength());
            }

            // 设置文件下载头
            String contentDisposition = responseHeaders.getFirst("Content-Disposition");
            if (contentDisposition != null) {
                response.setHeader("Content-Disposition", contentDisposition);
            }

            // 写入响应体
            byte[] fileData = fileResponse.getBody();
            if (fileData != null) {
                try (java.io.OutputStream outputStream = response.getOutputStream()) {
                    outputStream.write(fileData);
                    outputStream.flush();
                }
            }

            log.info("[{}] 文件下载成功 - 文件大小: {} bytes",
                    requestId, fileData != null ? fileData.length : 0);

        } catch (Exception e) {
            log.error("[{}] 文件下载失败: {}", requestId, e.getMessage(), e);

            try {
                response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
                response.getWriter().write("文件下载失败: " + e.getMessage());
            } catch (Exception ioException) {
                log.error("[{}] 写入错误响应失败: {}", requestId, ioException.getMessage());
            }
        }
    }

    /**
     * 创建专门用于文件下载的RestTemplate
     */
    private RestTemplate createFileDownloadRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();

        // 清除默认的消息转换器
        restTemplate.getMessageConverters().clear();

        // 添加字节数组消息转换器，支持所有类型
        org.springframework.http.converter.ByteArrayHttpMessageConverter byteArrayConverter =
            new org.springframework.http.converter.ByteArrayHttpMessageConverter();

        // 设置支持的媒体类型
        java.util.List<MediaType> supportedTypes = new java.util.ArrayList<>();
        supportedTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        supportedTypes.add(MediaType.valueOf("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
        supportedTypes.add(MediaType.valueOf("application/vnd.ms-excel"));
        supportedTypes.add(MediaType.APPLICATION_PDF);
        supportedTypes.add(MediaType.valueOf("application/msword"));
        supportedTypes.add(MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document"));
        supportedTypes.add(MediaType.IMAGE_JPEG);
        supportedTypes.add(MediaType.IMAGE_PNG);
        supportedTypes.add(MediaType.IMAGE_GIF);
        supportedTypes.add(MediaType.valueOf("application/zip"));
        supportedTypes.add(MediaType.valueOf("application/x-rar-compressed"));
        supportedTypes.add(MediaType.ALL);

        byteArrayConverter.setSupportedMediaTypes(supportedTypes);
        restTemplate.getMessageConverters().add(byteArrayConverter);

        // 添加字符串转换器作为备用
        org.springframework.http.converter.StringHttpMessageConverter stringConverter =
            new org.springframework.http.converter.StringHttpMessageConverter(java.nio.charset.StandardCharsets.UTF_8);
        restTemplate.getMessageConverters().add(stringConverter);

        return restTemplate;
    }

    /**
     * 处理multipart请求（文件上传）
     */
    private ResponseEntity<Object> handleMultipartRequest(HttpServletRequest request, HttpServletResponse response,
                                                         HttpMethod method, String requestId,
                                                         String currentUsername, Long currentUserId) {
        try {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;

            // 构建目标URL
            String requestUri = request.getRequestURI();
            String queryString = request.getQueryString();
            String targetUrl = agentServiceUrl + requestUri;
            if (queryString != null) {
                targetUrl += "?" + queryString;
            }

            log.info("[{}] 处理multipart请求 - URL: {}", requestId, targetUrl);

            // 构建multipart请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 处理文件参数
            java.util.Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
            for (java.util.Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
                MultipartFile file = entry.getValue();
                if (file != null && !file.isEmpty()) {
                    // 创建文件资源
                    ByteArrayResource fileResource = new ByteArrayResource(file.getBytes()) {
                        @Override
                        public String getFilename() {
                            return file.getOriginalFilename();
                        }
                    };
                    body.add(entry.getKey(), fileResource);

                    log.info("[{}] 添加文件: {} = {}, 大小: {} bytes",
                        requestId, entry.getKey(), file.getOriginalFilename(), file.getSize());
                }
            }

            // 处理普通参数
            Enumeration<String> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                String[] paramValues = request.getParameterValues(paramName);
                for (String paramValue : paramValues) {
                    body.add(paramName, paramValue);
                    log.debug("[{}] 添加参数: {} = {}", requestId, paramName, paramValue);
                }
            }

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 添加用户信息头
            headers.add("X-User-Id", currentUserId.toString());
            headers.add("X-Username", currentUsername);
            headers.add("X-Request-ID", requestId);
            headers.add("X-Proxy-By", "SpringBoot-MultipartProxy");
            headers.add("X-Original-IP", getClientIpAddress(request));

            // 发送请求
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            log.info("[{}] 发送multipart请求到: {}", requestId, targetUrl);

            ResponseEntity<String> agentResponse = fileProxyRestTemplate.exchange(
                targetUrl,
                method,
                requestEntity,
                String.class
            );

            log.info("[{}] multipart请求成功 - 状态: {}", requestId, agentResponse.getStatusCode());

            return ResponseEntity.status(agentResponse.getStatusCode())
                .headers(agentResponse.getHeaders())
                .body(agentResponse.getBody());

        } catch (Exception e) {
            log.error("[{}] multipart请求处理失败: {}", requestId, e.getMessage(), e);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(AjaxResult.error("文件上传失败: " + e.getMessage()));
        }
    }
}