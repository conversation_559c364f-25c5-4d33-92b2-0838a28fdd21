package com.ruoyi.gateway.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.annotation.RequiresLogin;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.gateway.utils.FileProxyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Enumeration;

/**
 * 文件代理控制器
 * 专门处理Python服务的文件上传下载代理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agentService")
public class FileProxyController {

    private static final Logger log = LoggerFactory.getLogger(FileProxyController.class);

    @Autowired
    @Qualifier("fileProxyRestTemplate")
    private RestTemplate fileProxyRestTemplate;

    @Value("${agent.base-url}")
    private String agentServiceUrl;

    @Autowired
    private FileProxyUtils fileProxyUtils;

    /**
     * 代理文件上传接口
     * 支持multipart/form-data格式的文件上传
     */
    @PostMapping("/api/kbUselessKeywords/importExcel")
    @RequiresLogin
    public ResponseEntity<Object> proxyFileUpload(
            HttpServletRequest request,
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam(value = "files", required = false) MultipartFile[] files) {
        
        String requestId = generateRequestId();
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取当前登录用户信息
            String currentUsername = SecurityUtils.getUsername();
            Long currentUserId = SecurityUtils.getUserId();
            
            // 获取请求路径
            String requestUri = request.getRequestURI();
            String queryString = request.getQueryString();
            
            log.info("[{}] 文件上传代理请求 - 用户: {}, 路径: {}, 单文件: {}, 多文件: {}",
                requestId, currentUsername, requestUri,
                file != null ? file.getOriginalFilename() : "无",
                files != null ? files.length + "个文件" : "无");

            // 验证文件
            if (file != null && !file.isEmpty()) {
                FileProxyUtils.FileValidationResult result = fileProxyUtils.validateFile(file);
                if (!result.isValid()) {
                    log.warn("[{}] 文件验证失败: {}", requestId, result.getMessage());
                    return ResponseEntity.badRequest()
                        .body(AjaxResult.error("文件验证失败: " + result.getMessage()));
                }
            }

            if (files != null && files.length > 0) {
                FileProxyUtils.FileValidationResult result = fileProxyUtils.validateFiles(files);
                if (!result.isValid()) {
                    log.warn("[{}] 文件验证失败: {}", requestId, result.getMessage());
                    return ResponseEntity.badRequest()
                        .body(AjaxResult.error("文件验证失败: " + result.getMessage()));
                }
            }

            // 构建目标URL
            String targetUrl = agentServiceUrl + requestUri;
            if (queryString != null) {
                targetUrl += "?" + queryString;
            }

            // 构建multipart请求
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            
            // 添加单个文件
            if (file != null && !file.isEmpty()) {
                ByteArrayResource fileResource = new ByteArrayResource(file.getBytes()) {
                    @Override
                    public String getFilename() {
                        return file.getOriginalFilename();
                    }
                };
                body.add("file", fileResource);
                log.info("[{}] 添加单个文件: {}, 大小: {} bytes", 
                    requestId, file.getOriginalFilename(), file.getSize());
            }
            
            // 添加多个文件
            if (files != null && files.length > 0) {
                for (MultipartFile multipartFile : files) {
                    if (!multipartFile.isEmpty()) {
                        ByteArrayResource fileResource = new ByteArrayResource(multipartFile.getBytes()) {
                            @Override
                            public String getFilename() {
                                return multipartFile.getOriginalFilename();
                            }
                        };
                        body.add("files", fileResource);
                        log.info("[{}] 添加文件: {}, 大小: {} bytes", 
                            requestId, multipartFile.getOriginalFilename(), multipartFile.getSize());
                    }
                }
            }
            
            // 添加其他表单参数
            Enumeration<String> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                if (!"file".equals(paramName) && !"files".equals(paramName)) {
                    String[] paramValues = request.getParameterValues(paramName);
                    for (String paramValue : paramValues) {
                        body.add(paramName, paramValue);
                        log.debug("[{}] 添加参数: {} = {}", requestId, paramName, paramValue);
                    }
                }
            }

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            // 添加用户信息头
            headers.add("X-User-Id", currentUserId.toString());
            headers.add("X-Username", currentUsername);
            headers.add("X-Request-ID", requestId);
            headers.add("X-Proxy-By", "SpringBoot-FileProxy");

            // 创建请求实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            log.info("[{}] 发送文件上传请求到: {}", requestId, targetUrl);

            // 发送请求
            ResponseEntity<String> response = fileProxyRestTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            long endTime = System.currentTimeMillis();
            log.info("[{}] 文件上传代理成功 - 状态: {}, 耗时: {}ms", 
                requestId, response.getStatusCode(), endTime - startTime);

            return ResponseEntity.status(response.getStatusCode())
                .headers(response.getHeaders())
                .body(response.getBody());

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("[{}] 文件上传代理失败 - 耗时: {}ms, 错误: {}", 
                requestId, endTime - startTime, e.getMessage(), e);
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(AjaxResult.error("文件上传失败: " + e.getMessage()));
        }
    }

    /**
     * 代理文件下载接口
     * 支持二进制流下载
     */
    @GetMapping("/api/kbFile/download/**")
    @RequiresLogin
    public void proxyFileDownload(HttpServletRequest request, HttpServletResponse response) {
        String requestId = generateRequestId();
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取当前登录用户信息
            String currentUsername = SecurityUtils.getUsername();
            Long currentUserId = SecurityUtils.getUserId();
            
            // 获取请求路径
            String requestUri = request.getRequestURI();
            String queryString = request.getQueryString();
            
            log.info("[{}] 文件下载代理请求 - 用户: {}, 路径: {}", 
                requestId, currentUsername, requestUri);

            // 构建目标URL
            String targetUrl = agentServiceUrl + requestUri;
            if (queryString != null) {
                targetUrl += "?" + queryString;
            }

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.add("X-User-Id", currentUserId.toString());
            headers.add("X-Username", currentUsername);
            headers.add("X-Request-ID", requestId);
            headers.add("X-Proxy-By", "SpringBoot-FileProxy");

            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            log.info("[{}] 发送文件下载请求到: {}", requestId, targetUrl);

            // 发送请求并获取字节数组响应
            ResponseEntity<byte[]> fileResponse = fileProxyRestTemplate.exchange(
                targetUrl,
                HttpMethod.GET,
                requestEntity,
                byte[].class
            );

            // 设置响应头
            HttpHeaders responseHeaders = fileResponse.getHeaders();
            if (responseHeaders.getContentType() != null) {
                response.setContentType(responseHeaders.getContentType().toString());
            }
            if (responseHeaders.getContentLength() > 0) {
                response.setContentLength((int) responseHeaders.getContentLength());
            }
            
            // 设置文件下载头
            String contentDisposition = responseHeaders.getFirst("Content-Disposition");
            if (contentDisposition != null) {
                response.setHeader("Content-Disposition", contentDisposition);
            }

            // 写入响应体
            byte[] fileData = fileResponse.getBody();
            if (fileData != null) {
                try (OutputStream outputStream = response.getOutputStream()) {
                    outputStream.write(fileData);
                    outputStream.flush();
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("[{}] 文件下载代理成功 - 文件大小: {} bytes, 耗时: {}ms", 
                requestId, fileData != null ? fileData.length : 0, endTime - startTime);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("[{}] 文件下载代理失败 - 耗时: {}ms, 错误: {}", 
                requestId, endTime - startTime, e.getMessage(), e);
            
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            try {
                response.getWriter().write("文件下载失败: " + e.getMessage());
            } catch (IOException ioException) {
                log.error("[{}] 写入错误响应失败: {}", requestId, ioException.getMessage());
            }
        }
    }

    /**
     * 代理文件导出接口
     * 支持Excel等文件导出
     */
    @PostMapping("/api/kbFile/export/**")
    @RequiresPermissions("knowledgebase:file:export")
    public void proxyFileExport(HttpServletRequest request, HttpServletResponse response, 
                               @RequestBody(required = false) String body) {
        String requestId = generateRequestId();
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取当前登录用户信息
            String currentUsername = SecurityUtils.getUsername();
            Long currentUserId = SecurityUtils.getUserId();
            
            // 获取请求路径
            String requestUri = request.getRequestURI();
            String queryString = request.getQueryString();
            
            log.info("[{}] 文件导出代理请求 - 用户: {}, 路径: {}", 
                requestId, currentUsername, requestUri);

            // 构建目标URL
            String targetUrl = agentServiceUrl + requestUri;
            if (queryString != null) {
                targetUrl += "?" + queryString;
            }

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("X-User-Id", currentUserId.toString());
            headers.add("X-Username", currentUsername);
            headers.add("X-Request-ID", requestId);
            headers.add("X-Proxy-By", "SpringBoot-FileProxy");

            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(body, headers);

            log.info("[{}] 发送文件导出请求到: {}", requestId, targetUrl);

            // 发送请求并获取字节数组响应
            ResponseEntity<byte[]> fileResponse = fileProxyRestTemplate.exchange(
                targetUrl,
                HttpMethod.POST,
                requestEntity,
                byte[].class
            );

            // 设置响应头
            HttpHeaders responseHeaders = fileResponse.getHeaders();
            if (responseHeaders.getContentType() != null) {
                response.setContentType(responseHeaders.getContentType().toString());
            }
            if (responseHeaders.getContentLength() > 0) {
                response.setContentLength((int) responseHeaders.getContentLength());
            }
            
            // 设置文件下载头
            String contentDisposition = responseHeaders.getFirst("Content-Disposition");
            if (contentDisposition != null) {
                response.setHeader("Content-Disposition", contentDisposition);
            } else {
                // 默认设置为附件下载
                response.setHeader("Content-Disposition", "attachment; filename=export.xlsx");
            }

            // 写入响应体
            byte[] fileData = fileResponse.getBody();
            if (fileData != null) {
                try (OutputStream outputStream = response.getOutputStream()) {
                    outputStream.write(fileData);
                    outputStream.flush();
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("[{}] 文件导出代理成功 - 文件大小: {} bytes, 耗时: {}ms", 
                requestId, fileData != null ? fileData.length : 0, endTime - startTime);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("[{}] 文件导出代理失败 - 耗时: {}ms, 错误: {}", 
                requestId, endTime - startTime, e.getMessage(), e);
            
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            try {
                response.getWriter().write("文件导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                log.error("[{}] 写入错误响应失败: {}", requestId, ioException.getMessage());
            }
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "FILE_REQ_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
}
