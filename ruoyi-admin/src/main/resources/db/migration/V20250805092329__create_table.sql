CREATE TABLE `pd_task_info` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `type` tinyint(1) DEFAULT NULL COMMENT '类型0:待办 1:任务',
  `title` VARCHAR(255) DEFAULT NULL COMMENT '标题',
  `user_id` BIGINT(11) DEFAULT NULL COMMENT '用户Id',
  `dept_id` BIGINT(11) DEFAULT NULL COMMENT '部门Id',
  `icon` TEXT DEFAULT NULL COMMENT '图标',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态 0成功 1失败',
  `read_flag` tinyint(1) DEFAULT NULL COMMENT '已读标识',
  `event_id` varchar(100) DEFAULT NULL COMMENT '事件Id',
  `event_type` tinyint(2) DEFAULT NULL COMMENT '事件类型',
  `group_id` BIGINT(11) DEFAULT NULL COMMENT '事件组Id',
  `event_time` DATETIME DEFAULT NULL COMMENT '事件记录时间',
  `create_by` VARCHAR(64) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
  `update_by` VARCHAR(64) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME DEFAULT NULL COMMENT '更新时间',
  UNIQUE KEY uk_event (event_id, event_type),
  PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=1 ROW_FORMAT=DYNAMIC COMMENT='待办任务信息';