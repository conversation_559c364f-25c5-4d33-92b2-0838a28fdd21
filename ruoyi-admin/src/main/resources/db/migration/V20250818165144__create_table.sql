-- 增加职位信息表
CREATE TABLE `tbp_position_base` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(50) DEFAULT NULL COMMENT '名称',
  `open_flag` TINYINT(1) DEFAULT NULL COMMENT '开放标识 0:开放 1:不开放',
  `description` text DEFAULT NULL COMMENT '职位描述和职位要求',
  `sort` INT(11) DEFAULT 1 COMMENT '排序',
  `sort_time` bigint DEFAULT NULL COMMENT '排序时间戳',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='职位库';

-- 人才信息表追加字段
ALTER TABLE tb_personal_info
    ADD COLUMN base_id      BIGINT      DEFAULT NULL COMMENT '职位库Id',
    ADD COLUMN pinyin       VARCHAR(255)         COMMENT '姓名拼音',
    ADD COLUMN attention_flag TINYINT(1) DEFAULT 1 COMMENT '是否关注 0关注 1不关注',
    ADD COLUMN suitable_flag  TINYINT(1) DEFAULT 0 COMMENT '是否合格 0合适 1不合适',
    ADD COLUMN read_flag      TINYINT(1) DEFAULT 0 COMMENT '是否已读 0未读 1已读',
    ADD COLUMN task_id     BIGINT DEFAULT NULL COMMENT '任务Id';

-- 人才信息
CREATE TABLE `tbp_resume_evaluate` (
  `talent_id` bigint NOT NULL AUTO_INCREMENT COMMENT '人才信息Id',
  `minimum_education_score` double(10,1) DEFAULT NULL COMMENT '最低学历分',
  `work_experience_score` double(10,1) DEFAULT NULL COMMENT '工作经验分',
  `job_hopping_rate_score` double(10,1) DEFAULT NULL COMMENT '跳槽频率分',
  `salary_range_score` double(10,1) DEFAULT NULL COMMENT '薪资范围分',
  `total_score` double(10,1) DEFAULT NULL COMMENT '总分',
  `education_evaluation` text DEFAULT NULL COMMENT '教育信息评价',
  `work_experience_evaluation` text DEFAULT NULL COMMENT '工作经验评价',
  `job_hopping_rate_evaluation` text DEFAULT NULL COMMENT '跳槽频率评价',
  `salary_range_evaluation` text DEFAULT NULL COMMENT '薪资范围评价',
  `comprehensive_evaluation` text DEFAULT NULL COMMENT '综合评价',
  PRIMARY KEY (`talent_id`) USING BTREE
) ENGINE=InnoDB COMMENT='简历评估信息';

-- 寻才表
ALTER TABLE tb_task_posting
    ADD COLUMN base_id      BIGINT      DEFAULT NULL COMMENT '职位库Id';
-- 任务表
ALTER TABLE tb_analyse_task
    ADD COLUMN base_id      BIGINT      DEFAULT NULL COMMENT '职位库Id';
