# 开发环境配置
spring:
  main:
    web-application-type: SERVLET
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    # 动态数据源配置
    dynamic:
      primary: master
      strict: false
      datasource:
        # 主库数据源
        master:
          url: *******************************************************************************************************************************************************
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
        # 从库数据源
        slave:
          url: *******************************************************************************************************************************************************
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
    # druid 配置
    druid:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # redis配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  # Flyway开发环境配置
  flyway:
    # 启用Flyway
    enabled: true
    # 允许clean操作（仅开发环境）
    clean-disabled: false
    # 允许无序迁移（仅开发环境）
    out-of-order: true
    # 临时禁用验证（解决校验和不匹配问题）
    validate-on-migrate: false
    # 基线迁移
    baseline-on-migrate: true
    # 迁移脚本位置
    locations: classpath:db/migration
    # 表名
    table: flyway_schema_history
    # 编码格式
    encoding: UTF-8
    # 迁移脚本前缀
    sql-migration-prefix: V
    # 迁移脚本分隔符
    sql-migration-separator: __
    # 迁移脚本后缀
    sql-migration-suffixes: .sql
    # 迁移脚本版本格式
    sql-migration-version-format: yyyyMMddHHmmss
    # 禁用占位符替换
    placeholder-replacement: false
    # 验证迁移脚本命名
    validate-migration-naming: true
    # 连接重试次数
    connect-retries: 3
    # 连接重试间隔（秒）
    connect-retries-interval: 15
    # 开发环境详细日志
    loggers: slf4j

# Agent配置
agent:
  base-url: http://localhost:8000
python:
  base-url: http://*************:8888
  ssl:
    # 是否信任所有SSL证书（用于解决自签名证书问题）
    trust-all: false

# 开发环境跨域配置
cors:
  # 是否启用跨域支持
  enabled: false

  # 允许的跨域域名列表（开发环境）
  allowed-origins: https://localhost,http://*************:8002,https://127.0.0.1,https://************,http://************:8080,http://*************:8080,http://*************:5173,http://************:8080,http://*************:8081,http://**************:8080,http://*************:8080,http://************:8000

  # 是否允许发送凭证（cookies等）
  allow-credentials: true
  
  # 允许的HTTP方法
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  
  # 允许的请求头
  allowed-headers:
    - Authorization
    - Content-Type
    - X-Requested-With
    - X-Token
    - X-User-Id
    - X-Company-Id
  
  # 暴露的响应头
  exposed-headers:
    - Content-Disposition
    - Content-Length
    - X-Total-Count
    - X-Page-Size
    - X-Current-Page
  
  # 预检请求缓存时间（秒）
  max-age: 3600
  
  # 需要跨域支持的路径
  paths:
    - /**
  
  # 跨域安全检查配置
  security-check:
    # 是否启用跨域安全检查
    enabled: true
    
    # 是否记录跨域访问日志
    log-enabled: true

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn
    # Flyway详细日志
    org.flywaydb: debug
    # SQL日志
    com.ruoyi.**.mapper: debug
    # CORS拦截器日志
    com.ruoyi.common.security.interceptor: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/ruoyi-cloud-ai-kb.log

# Minio配置
minio:
  url: http://172.16.20.87:9000
  saveUrl: http://172.16.20.87:9000
  accessKey: ZOIEImQTgu10lnAnvSAs
  secretKey: 5M6se3tVOzWc2j41lSsLkG6ingOBRppVmpkcdTsP
  bucketName: aikb