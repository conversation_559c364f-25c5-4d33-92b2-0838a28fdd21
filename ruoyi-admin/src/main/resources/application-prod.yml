# 生产环境配置
spring:
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    # 动态数据源配置
    dynamic:
      primary: master
      strict: false
      datasource:
        # 主库数据源
        master:
          url: jdbc:mysql://${HOST_IP}:3316/ai_kb_box?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
          username: aikbbox
          password: aikbboxadmin
          driver-class-name: com.mysql.cj.jdbc.Driver
        # 从库数据源
#        slave:
#          url: *******************************************************************************************************************************************************
#          username: root
#          password: 123456
#          driver-class-name: com.mysql.cj.jdbc.Driver
    # druid 配置
    druid:
      # 初始连接数
      initialSize: 10
      # 最小连接池数量
      minIdle: 20
      # 最大连接池数量
      maxActive: 50
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: false
      statViewServlet:
        enabled: false
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 2000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # redis配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6380
    # 数据库索引
    database: 0
    # 密码
    password: redis@admin123
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  # Flyway生产环境配置
  flyway:
    # 启用Flyway
    enabled: true
    # 禁用clean操作（生产环境必须禁用）
    clean-disabled: true
    # 禁止无序迁移（生产环境必须禁用）
    out-of-order: false
    # 临时禁用验证（解决校验和不匹配问题）
    validate-on-migrate: false
    # 基线迁移
    baseline-on-migrate: true
    # 迁移脚本位置
    locations: classpath:db/migration
    # 表名
    table: flyway_schema_history
    # 编码格式
    encoding: UTF-8
    # 迁移脚本前缀
    sql-migration-prefix: V
    # 迁移脚本分隔符
    sql-migration-separator: __
    # 迁移脚本后缀
    sql-migration-suffixes: .sql
    # 迁移脚本版本格式
    sql-migration-version-format: yyyyMMddHHmmss
    # 禁用占位符替换
    placeholder-replacement: false
    # 验证迁移脚本命名
    validate-migration-naming: true
    # 连接重试次数
    connect-retries: 3
    # 连接重试间隔（秒）
    connect-retries-interval: 15

# Agent配置
agent:
  base-url: http://${HOST_IP}:8000
python:
  base-url: http://interview.qiyundian.com:8888
  ssl:
    # 是否信任所有SSL证书（生产环境建议设置为false，除非使用自签名证书）
    trust-all: false

# 生产环境跨域配置
cors:
  # 是否启用跨域支持
  enabled: true
  
  # 允许的跨域域名列表（生产环境 - 包含当前使用的IP）
  allowed-origins: https://localhost,https://127.0.0.1,https://${HOST_IP},http://${HOST_IP}:8080
  
  # 是否允许发送凭证（cookies等）
  allow-credentials: true
  
  # 允许的HTTP方法（生产环境限制）
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  
  # 允许的请求头（添加缺失的请求头）
  allowed-headers:
    - Authorization
    - Content-Type
    - X-Requested-With
    - X-Token
    - X-User-Id
    - X-Company-Id
    - requesttime
    - Accept
    - Origin
    - Access-Control-Request-Method
    - Access-Control-Request-Headers
  
  # 暴露的响应头
  exposed-headers:
    - Content-Disposition
    - Content-Length
    - X-Total-Count
    - X-Page-Size
    - X-Current-Page
  
  # 预检请求缓存时间（秒）- 生产环境可以设置更长
  max-age: 86400
  
  # 需要跨域支持的路径（扩展以包含所有必要路径）
  paths:
    - /**
  
  # 跨域安全检查配置
  security-check:
    # 是否启用跨域安全检查（生产环境必须启用）
    enabled: true
    
    # 是否记录跨域访问日志（生产环境建议启用以便监控）
    log-enabled: true

# 日志配置
logging:
  level:
    com.ruoyi: info
    org.springframework: warn
    # Flyway日志级别
    org.flywaydb: info
    # SQL日志（生产环境关闭）
    com.ruoyi.**.mapper: warn

# Minio配置
minio:
  url: http://${HOST_IP}:9007
  saveUrl: https://${HOST_IP}/minio-images
  accessKey: minio
  secretKey: minio@admin1234
  bucketName: aikb