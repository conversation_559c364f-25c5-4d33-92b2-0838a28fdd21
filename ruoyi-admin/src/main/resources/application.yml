# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi-Cloud-AI-KB
  # 版本
  version: 1.0.0.0903
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当设置为0时，默认为大小是无限的
    max-http-post-size: 0
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 16
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256
  # Tomcat配置 - 支持长连接和流式响应
  tomcat:
    # 连接超时时间（毫秒）
    connection-timeout: 60000
    # 最大连接数
    max-connections: 8192
    # 接受连接队列大小
    accept-count: 100
    # 最大线程数
    max-threads: 200
    # 最小空闲线程数
    min-spare-threads: 10
    # 连接保持活跃时间（毫秒）- 增加到10分钟
    keep-alive-timeout: 600000
    # 最大保持活跃请求数
    max-keep-alive-requests: 1000
    # 禁用压缩以提高流式响应性能
    compression: false
    # 设置socket超时时间（毫秒）- 增加到10分钟
    socket-timeout: 600000
    # 异步请求超时时间（毫秒）- 设置为30分钟
    async-request-timeout: 1800000

# Spring配置
spring:
  # 激活的配置文件
  profiles:
#    active: prod
    active: dev
    # 包含的配置文件（跨域配置）
    include: ddos
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小 - 增加到100MB
      max-file-size: 100MB
      # 设置总上传的文件大小 - 增加到200MB
      max-request-size: 200MB
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 延迟解析文件
      resolve-lazily: false
  # Flyway数据库迁移配置
  flyway:
    # 启用Flyway
    enabled: true
    # 禁用clean操作（生产环境必须设置为true）
    clean-disabled: true
    # 编码格式
    encoding: UTF-8
    # 迁移脚本位置
    locations: classpath:db/migration
    # 基线迁移（当数据库不为空时，设置为true）
    baseline-on-migrate: true
    # 禁用占位符替换
    placeholder-replacement: false
    # 临时禁用验证（解决校验和不匹配问题）
    validate-on-migrate: false
    # 验证迁移脚本的校验和
    validate-migration-naming: true
    # 迁移脚本的前缀
    sql-migration-prefix: V
    # 迁移脚本的分隔符
    sql-migration-separator: __
    # 迁移脚本的后缀
    sql-migration-suffixes: .sql
    # 迁移脚本的版本格式
    sql-migration-version-format: yyyyMMddHHmmss
    # 表名（存储迁移历史）
    table: flyway_schema_history
    # 是否允许无序迁移
    out-of-order: false
    # 迁移超时时间（秒）
    connect-retries: 3
    connect-retries-interval: 15
  # 服务模块
  main:
    # 允许class之间循环调用
    allow-circular-references: true
    # 允许bean定义覆盖
    allow-bean-definition-overriding: true

# 流式响应配置
streaming:
  # 最大响应时间（毫秒）- 设置为30分钟
  max-response-time: 1800000
  # 缓冲区大小（字节）- 增加到16KB
  buffer-size: 16384

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 代码生成
gen:
  # 作者
  author: ruoyi
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.ruoyi.system
  # 自动去除表前缀，默认是false
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，如果使用此配置，请确保生成的表名去掉前缀后仍然为类名）
  tablePrefix: sys_

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn
    # Flyway日志级别
    org.flywaydb: info
    # CORS拦截器日志
    com.ruoyi.common.security.interceptor: debug
    # DDoS拦截器日志
    com.ruoyi.common.security.interceptor.DdosProtectionInterceptor: debug
    # Redis连接和操作日志
    org.springframework.data.redis: debug
    io.lettuce.core: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/ruoyi-cloud-ai-kb.log

# Seata配置 - 禁用分布式事务（单体应用不需要）
seata:
  enabled: false
  auto-data-source-proxy: false
  enable-auto-data-source-proxy: false

# 禁用一些可能导致启动失败的功能
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS

# Dify配置
dify:
  base-url: http://localhost:8080
  api-key: your-dify-api-key-here
  api-chat-key: your-dify-chat-api-key-here

# 本地文件上传
file:
  domain: http://127.0.0.1:9300
  path: D:/ruoyi/uploadPath
  prefix: /statics
  # 文件代理配置
  proxy:
    enabled: true
    max-file-size: 104857600  # 100MB
    max-request-size: 209715200  # 200MB
    connect-timeout: 60000  # 60秒
    read-timeout: 600000  # 10分钟
    enable-file-type-check: true
    enable-file-size-check: true
    allowed-file-types:
      - pdf
      - doc
      - docx
      - xls
      - xlsx
      - ppt
      - pptx
      - txt
      - md
      - csv
      - json
      - xml
      - html
      - htm
      - jpg
      - jpeg
      - png
      - gif
      - bmp
      - svg
      - mp3
      - mp4
      - avi
      - mov
      - wmv
      - flv
      - zip
      - rar
      - 7z
      - tar
      - gz

# FastDFS配置
fdfs:
  domain: http://************
  soTimeout: 3000
  connectTimeout: 2000
  trackerList: ************:22122

