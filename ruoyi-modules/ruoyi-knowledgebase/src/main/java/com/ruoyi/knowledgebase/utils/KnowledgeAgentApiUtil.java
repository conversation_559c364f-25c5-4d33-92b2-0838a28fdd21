package com.ruoyi.knowledgebase.utils;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.utils.agent.FileInfo;
import com.ruoyi.common.core.utils.agent.FileInfoResponse;
import com.ruoyi.common.core.utils.agent.FileTrunkQuery;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Agent API工具类
 * 用于与知识库API进行交互
 */
@Component
public class KnowledgeAgentApiUtil {
    private static final Logger log = LoggerFactory.getLogger(KnowledgeAgentApiUtil.class);

    @Value("${agent.base-url}")
    private String baseUrl;

    // 创建OkHttpClient实例
    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)    // 连接超时：30秒
            .readTimeout(120, TimeUnit.SECONDS)      // 读取超时：120秒（AI评价生成需要更长时间）
            .writeTimeout(30, TimeUnit.SECONDS)      // 写入超时：30秒
            .build();

    /**
     * 向知识库添加文件
     *
     * @param kbId  知识库ID
     * @param files 文件列表
     * @return 文件ID列表
     */
    public List<Map<String, String>> addFilesToKb(Long kbId, List<FileInfo> files) {
        try {
            String url = baseUrl + "/agentService/api/kbFile/add";
            log.info("正在向知识库添加文件。URL: {}", url);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("kb_id", kbId);
            requestBody.put("files", files);

            // 创建请求
            Request request = new Request.Builder()
                    .url(url)
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(requestBody)))
                    .build();

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    Map<String, Object> responseMap = JSON.parseObject(responseBody, Map.class);

                    // Check if the response indicates success
                    if (responseMap.get("code") != null && (int) responseMap.get("code") == 200) {
                        // Get the data object
                        @SuppressWarnings("unchecked")
                        Map<String, Object> data = (Map<String, Object>) responseMap.get("data");

                        // Get the inserted_files array
                        @SuppressWarnings("unchecked")
                        List<Map<String, String>> insertedFiles = (List<Map<String, String>>) data.get("inserted_files");

                        if (insertedFiles != null && !insertedFiles.isEmpty()) {
                            log.info("成功添加文件，返回的文件信息: {}", insertedFiles);
                            return insertedFiles;
                        } else {
                            log.warn("添加文件成功但未返回文件信息");
                            return new ArrayList<>();
                        }
                    } else {
                        log.error("添加文件失败，错误码: {}, 消息: {}", responseMap.get("code"), responseMap.get("message"));
                        return null;
                    }
                } else {
                    log.error("添加文件失败，响应状态: {}", response.code());
                    if (response.body() != null) {
                        log.error("错误响应内容: {}", response.body().string());
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("添加文件到知识库时发生异常", e);
            return null;
        }
    }

    /**
     * 从知识库删除文件
     *
     * @param fileId 文件ID（可选）
     * @param kbId   知识库ID（可选）
     * @return 删除的文件数量，失败返回-1
     */
    public int deleteFileFromKb(String fileId, String kbId) {
        try {
            String url = baseUrl + "/agentService/api/kbFile/delete";
            log.info("正在从知识库删除文件。URL: {}", url);

            // 构建请求参数
            HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
            if (fileId != null && !fileId.trim().isEmpty()) {
                urlBuilder.addQueryParameter("file_ids", fileId);
            }
            if (kbId != null && !kbId.trim().isEmpty()) {
                urlBuilder.addQueryParameter("kb_ids", kbId);
            }

            // 检查是否至少有一个参数
            if (!urlBuilder.build().queryParameterNames().contains("file_ids") &&
                    !urlBuilder.build().queryParameterNames().contains("kb_ids")) {
                log.error("删除文件失败：必须提供file_ids或kb_ids参数");
                return -1;
            }

            // 创建请求
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .delete()
                    .build();

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    Map<String, Object> responseMap = JSON.parseObject(responseBody, Map.class);

                    // Check if the response indicates success
                    if (responseMap.get("code") != null && (int) responseMap.get("code") == 200) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
                        if (data != null && data.get("deleted_count") != null) {
                            return ((Number) data.get("deleted_count")).intValue();
                        }
                        return 0;
                    } else {
                        log.error("删除文件失败，错误码: {}, 消息: {}", responseMap.get("code"), responseMap.get("message"));
                        return -1;
                    }
                } else {
                    log.error("删除文件失败，响应状态: {}", response.code());
                    if (response.body() != null) {
                        log.error("错误响应内容: {}", response.body().string());
                    }
                    return -1;
                }
            }
        } catch (Exception e) {
            log.error("从知识库删除文件时发生异常", e);
            return -1;
        }
    }

    /**
     * 获取文件详情信息
     *
     * @param fileIds 文件ID列表，用逗号分隔
     * @return 文件详情信息列表
     */
    public List<FileInfoResponse> getFileInfo(String fileIds) {
        try {
            String url = baseUrl + "/agentService/api/kbFile/getFileInfo";
            log.info("正在查询文件详情。URL: {}", url);

            // 构建请求参数
            HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
            urlBuilder.addQueryParameter("file_ids", fileIds);

            // 创建请求
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .get()
                    .header("Content-Type", "application/json")
                    .build();

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    Map<String, Object> responseMap = JSON.parseObject(responseBody, Map.class);

                    // 检查响应是否成功
                    if (responseMap.get("code") != null && (int) responseMap.get("code") == 200) {
                        // 获取data对象
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> dataList = (List<Map<String, Object>>) responseMap.get("data");

                        // 转换为FileInfoResponse对象列表
                        List<FileInfoResponse> fileInfos = new ArrayList<>();
                        for (Map<String, Object> data : dataList) {
                            FileInfoResponse fileInfo = new FileInfoResponse();
                            fileInfo.setFileId((String) data.get("file_id"));
                            fileInfo.setFileName((String) data.get("file_name"));
                            fileInfo.setFileOriginalName((String) data.get("file_original_name"));
                            fileInfo.setContent((String) data.get("content"));
                            fileInfo.setFileExtension((String) data.get("file_extension"));
                            fileInfo.setAllowDownload((Integer) data.get("allow_download"));
                            fileInfo.setFileSize(((Number) data.get("file_size")).longValue());
                            fileInfo.setFileState((Integer) data.get("file_state"));
                            fileInfo.setFileEnable((Integer) data.get("file_enable"));
                            fileInfo.setUpdateTime((String) data.get("update_time"));
                            fileInfo.setUploadTime((String) data.get("upload_time"));
                            fileInfo.setKbId(((Number) data.get("kb_id")).longValue());
                            if (data.get("trunk_count") != null) {
                                fileInfo.setTrunkCount(((Number) data.get("trunk_count")).longValue());
                            } else {
                                fileInfo.setTrunkCount(0L);
                            }
                            fileInfos.add(fileInfo);
                        }

                        return fileInfos;
                    } else {
                        log.error("查询文件详情失败，错误码: {}, 消息: {}", responseMap.get("code"), responseMap.get("message"));
                        return null;
                    }
                } else {
                    log.error("查询文件详情失败，响应状态: {}", response.code());
                    if (response.body() != null) {
                        log.error("错误响应内容: {}", response.body().string());
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("查询文件详情时发生异常", e);
            return null;
        }
    }

    /**
     * 获取文件分块列表
     *
     * @param query 查询参数
     * @return 分块列表信息
     */
    public Map<String, Object> getFileTrunkList(FileTrunkQuery query) {
        try {
            String url = baseUrl + "/agentService/api/kbFile/getFileTrunkList";
            log.info("正在获取文件分块列表。URL: {}", url);

            // 构建请求参数
            HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
            if (query.getFileId() != null && !query.getFileId().trim().isEmpty()) {
                urlBuilder.addQueryParameter("file_id", query.getFileId());
            }
            if (query.getTrunkState() != null && !query.getTrunkState().trim().isEmpty()) {
                urlBuilder.addQueryParameter("trunk_state", query.getTrunkState());
            }
            if (query.getPageNumber() != null && !query.getPageNumber().trim().isEmpty()) {
                urlBuilder.addQueryParameter("pageNumber", query.getPageNumber());
            }
            if (query.getPageSize() != null && !query.getPageSize().trim().isEmpty()) {
                urlBuilder.addQueryParameter("pageSize", query.getPageSize());
            }

            // 创建请求
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .get()
                    .header("Content-Type", "application/json")
                    .build();

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    Map<String, Object> responseMap = JSON.parseObject(responseBody, Map.class);

                    // 检查响应是否成功
                    if (responseMap.get("code") != null && (int) responseMap.get("code") == 200) {
                        return responseMap;
                    } else {
                        log.error("获取文件分块列表失败，错误码: {}, 消息: {}", responseMap.get("code"), responseMap.get("message"));
                        return null;
                    }
                } else {
                    log.error("获取文件分块列表失败，响应状态: {}", response.code());
                    if (response.body() != null) {
                        log.error("错误响应内容: {}", response.body().string());
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("获取文件分块列表时发生异常", e);
            return null;
        }
    }

    /**
     * 重试解析文件
     *
     * @param fileId 文件ID
     * @return 重试解析结果
     */
    public Map<String, Object> retryParseFile(String fileId) {
        try {
            String url = baseUrl + "/agentService/api/kbFile/retryParse/" + fileId;
            log.info("正在重试解析文件。URL: {}", url);

            // 创建请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(MediaType.parse("application/json"), ""))
                    .build();

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    Map<String, Object> responseMap = JSON.parseObject(responseBody, Map.class);

                    // 检查响应是否成功
                    if (responseMap.get("code") != null && (int) responseMap.get("code") == 200) {
                        return responseMap;
                    } else {
                        log.error("重试解析文件失败，错误码: {}, 消息: {}", responseMap.get("code"), responseMap.get("message"));
                        return null;
                    }
                } else {
                    log.error("重试解析文件失败，响应状态: {}", response.code());
                    if (response.body() != null) {
                        log.error("错误响应内容: {}", response.body().string());
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("重试解析文件时发生异常", e);
            return null;
        }
    }

    /**
     * 查询知识库文件列表
     *
     * @param kbId 知识库ID
     * @return 文件总数，失败返回-1
     */
    public int getKbFileList(Long kbId) {
        try {
            String url = baseUrl + "/agentService/api/kbFile/file/list";
            log.info("正在查询知识库文件列表。URL: {}", url);

            // 构建请求参数
            HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
            urlBuilder.addQueryParameter("kbId", String.valueOf(kbId));

            // 创建请求
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .get()
                    .header("Content-Type", "application/json")
                    .build();

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    Map<String, Object> responseMap = JSON.parseObject(responseBody, Map.class);

                    // 检查响应是否成功
                    if (responseMap.get("code") != null && (int) responseMap.get("code") == 200) {
                        // 获取total字段
                        if (responseMap.get("data") != null) {
                            Map<String, Object> dataMap = JSON.parseObject(responseMap.get("data").toString(), Map.class);
                            return ((Number) dataMap.get("total")).intValue();
                        }
                        return 0;
                    } else {
                        log.error("查询知识库文件列表失败，错误码: {}, 消息: {}", responseMap.get("code"), responseMap.get("message"));
                        return -1;
                    }
                } else {
                    log.error("查询知识库文件列表失败，响应状态: {}", response.code());
                    if (response.body() != null) {
                        log.error("错误响应内容: {}", response.body().string());
                    }
                    return -1;
                }
            }
        } catch (Exception e) {
            log.error("查询知识库文件列表时发生异常", e);
            return -1;
        }
    }
}