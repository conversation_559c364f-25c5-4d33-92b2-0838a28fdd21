package com.ruoyi.knowledgebase.domain.dto;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 知识库对象 kbb_knowledge_base
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */

@Data
public class KbbKnowledgeBaseDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 知识库ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 知识库名称 */
    @Excel(name = "知识库名称")
    private String kbName;

    private String pinyin;

    /** 排序 */
    @Excel(name = "排序")
    private Integer kbSort;

    /** 是否默认知识库（0否 1是） */
    @Excel(name = "是否默认知识库", readConverterExp = "0=否,1=是")
    private String isDefault;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 标签ID数组，用于新增和修改时传递标签 */
    private Long[] tagIds;

    /** 标签ID列表，用于查询条件 */
    private Long[] searchTagIds;

    /** 知识库归属（0：全部，1：我的知识库，2：授权的知识库） */
    private Integer ownershipType;

    /** 排序规则（0：知识库名称，1：创建时间，2：修改时间） */
    private Integer sortType;

    /** 排序方向（asc/desc） */
    private String sortOrder;

    /** 被指派的用户ID集合 */
    private Long[] userIds;
}
