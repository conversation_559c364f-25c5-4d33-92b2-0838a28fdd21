package com.ruoyi.file.service;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.file.domain.UploadResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件上传接口
 * 
 * <AUTHOR>
 */
public interface ISysFileService
{
    /**
     * 文件上传接口
     * 
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    public UploadResult uploadFile(MultipartFile file) throws Exception;

    /**
     * 文件下载接口
     * @param fileName
     * @param response
     * @throws Exception
     */
    public void downloadFile(String fileName, HttpServletResponse response) throws Exception;

    /**
     * 删除文件
     * @param fileUrl
     * @return
     */
    int deleteFile(String fileUrl);

    /**
     * 批量上传文件
     * @param files
     * @return
     */
    List<UploadResult> uploadFiles(MultipartFile[] files);
}
