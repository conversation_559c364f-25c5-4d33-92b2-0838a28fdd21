package com.ruoyi.file.service.impl;

import com.ruoyi.file.domain.UploadResult;
import com.ruoyi.file.service.ISysFileService;
import com.ruoyi.file.utils.FileUploadUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 本地文件存储
 * 
 * <AUTHOR>
 */

@Service
public class LocalSysFileServiceImpl implements ISysFileService
{
    /**
     * 资源映射路径 前缀
     */
    @Value("${file.prefix}")
    public String localFilePrefix;

    /**
     * 域名或本机访问地址
     */
    @Value("${file.domain}")
    public String domain;

    /**
     * 上传文件存储在本地的根路径
     */
    @Value("${file.path}")
    private String localFilePath;

    /**
     * 本地文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    @Override
    public UploadResult uploadFile(MultipartFile file) throws Exception
    {
        String name = FileUploadUtils.upload(localFilePath, file);
        String url = domain + localFilePrefix + name;
        UploadResult uploadResult = new UploadResult();
        uploadResult.setUrl(url);
        return uploadResult;
    }

    @Override
    public void downloadFile(String fileName, HttpServletResponse response) throws Exception {

    }

    @Override
    public int deleteFile(String fileUrl) {
        return 0;
    }

    @Override
    public List<UploadResult> uploadFiles(MultipartFile[] files) {
        return null;
    }
}
