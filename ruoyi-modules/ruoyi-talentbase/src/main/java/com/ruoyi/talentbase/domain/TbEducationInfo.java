package com.ruoyi.talentbase.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.utils.DateUtils;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import static com.ruoyi.common.core.utils.DateUtils.YYYY_MM;

/**
 * 教育信息对象 tb_education_info
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
public class TbEducationInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 基本信息Id */
    @Excel(name = "基本信息Id")
    private Long personalId;

    /** 学历 */
    @Excel(name = "学历")
    private String educationLevel;

    /** 毕业院校 */
    @Excel(name = "毕业院校")
    private String graduationSchool;

    /** 简介 */
    @Excel(name = "简介")
    private String introduction;

    /** 专业 */
    @Excel(name = "专业")
    private String major;

    /** 起止时间 */
    @JsonFormat(pattern = "yyyy-MM")
    @Excel(name = "起止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 截至时间 */
    @JsonFormat(pattern = "yyyy-MM")
    @Excel(name = "截至时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    public String infoStr() {
        return "学历：" + educationLevel
                + "，毕业院校：" + (graduationSchool == null ? "" : graduationSchool)
                + "，专业：" + (major == null ? "" : major)
                + "，起止时间：" + (startTime == null ? "" : DateUtils.dateTimeFormat(startTime, YYYY_MM)) + "至" + (endTime == null ? "" : DateUtils.dateTimeFormat(endTime, YYYY_MM))
                + "，简介：" + (introduction == null ? "" : introduction);
    }
}
