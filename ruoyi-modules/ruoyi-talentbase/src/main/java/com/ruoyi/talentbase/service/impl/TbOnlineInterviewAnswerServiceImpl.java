package com.ruoyi.talentbase.service.impl;

import com.ruoyi.talentbase.domain.TbOnlineInterviewAnswer;
import com.ruoyi.talentbase.mapper.TbOnlineInterviewAnswerMapper;
import com.ruoyi.talentbase.service.ITbOnlineInterviewAnswerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class TbOnlineInterviewAnswerServiceImpl implements ITbOnlineInterviewAnswerService {

    @Autowired
    private TbOnlineInterviewAnswerMapper mapper;

    @Override
    public int insertTbOnlineInterviewAnswer(TbOnlineInterviewAnswer answer) {
        if (answer == null) {
            return 0;
        }
        return mapper.insertTbOnlineInterviewAnswer(answer);
    }

    @Override
    public int batchInsert(List<TbOnlineInterviewAnswer> list) {
        if (list == null || list.isEmpty()) {
            return 0;
        }
        int count = 0;
        for (TbOnlineInterviewAnswer answer : list) {
            count += mapper.insertTbOnlineInterviewAnswer(answer);
        }
        return count;
    }

    @Override
    public List<TbOnlineInterviewAnswer> selectByInterviewId(Long interviewId) {
        if (interviewId == null) {
            return null;
        }
        return mapper.selectByInterviewId(interviewId);
    }
} 