package com.ruoyi.talentbase.mapper;

import java.util.List;
import com.ruoyi.talentbase.domain.TbpPositionBase;
import com.ruoyi.talentbase.domain.vo.TbpPositionBaseCount;
import org.apache.ibatis.annotations.Param;

/**
 * 职位库Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-18
 */
public interface TbpPositionBaseMapper 
{
    /**
     * 查询职位库
     * 
     * @param id 职位库主键
     * @return 职位库
     */
    public TbpPositionBase selectTbpPositionBaseById(Long id);

    /**
     * 查询职位库列表
     * 
     * @param tbpPositionBase 职位库
     * @return 职位库集合
     */
    public List<TbpPositionBase> selectTbpPositionBaseList(TbpPositionBase tbpPositionBase);

    /**
     * 新增职位库
     * 
     * @param tbpPositionBase 职位库
     * @return 结果
     */
    public int insertTbpPositionBase(TbpPositionBase tbpPositionBase);

    /**
     * 修改职位库
     * 
     * @param tbpPositionBase 职位库
     * @return 结果
     */
    public int updateTbpPositionBase(TbpPositionBase tbpPositionBase);

    /**
     * 删除职位库
     * 
     * @param id 职位库主键
     * @return 结果
     */
    public int deleteTbpPositionBaseById(Long id);

    /**
     * 批量删除职位库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbpPositionBaseByIds(Long[] ids);

    /**
     * 更新排序
     *
     * @param id 职位库主键
     * @param targetSeqNo 目标排序
     * @param oldSeqNo 当前排序
     * @param reduce 是否减少 0是 1否
     */
    void updateSort(@Param("id") Long id,
                    @Param("targetSeqNo") Integer targetSeqNo,
                    @Param("oldSeqNo") Integer oldSeqNo,
                    @Param("reduce") Integer reduce,
                    @Param("openFlag") Integer openFlag);

    /**
     * 获取目前最大的序号
     * @param openFlag 开放标识
     * @return 最大序号
     */
    Integer getMaxSortCode(@Param("openFlag") Integer openFlag);

    /**
     * 职位库统计
     * @return 统计结果
     */
    TbpPositionBaseCount getPositionBaseCount(TbpPositionBase tbpPositionBase);

}