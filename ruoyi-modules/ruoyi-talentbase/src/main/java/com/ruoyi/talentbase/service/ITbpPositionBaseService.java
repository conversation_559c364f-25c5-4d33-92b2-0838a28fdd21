package com.ruoyi.talentbase.service;

import java.util.List;
import com.ruoyi.talentbase.domain.TbpPositionBase;
import com.ruoyi.talentbase.domain.vo.TbpPositionBaseCount;

/**
 * 职位库Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-18
 */
public interface ITbpPositionBaseService 
{
    /**
     * 查询职位库
     * 
     * @param id 职位库主键
     * @return 职位库
     */
    public TbpPositionBase selectTbpPositionBaseById(Long id);

    /**
     * 查询职位库列表
     * 
     * @param tbpPositionBase 职位库
     * @return 职位库集合
     */
    public List<TbpPositionBase> selectTbpPositionBaseList(TbpPositionBase tbpPositionBase);

    /**
     * 新增职位库
     * 
     * @param tbpPositionBase 职位库
     * @return 结果
     */
    public int insertTbpPositionBase(TbpPositionBase tbpPositionBase);

    /**
     * 修改职位库
     * 
     * @param tbpPositionBase 职位库
     * @return 结果
     */
    public int updateTbpPositionBase(TbpPositionBase tbpPositionBase);

    /**
     * 批量删除职位库
     * 
     * @param ids 需要删除的职位库主键集合
     * @return 结果
     */
    public int deleteTbpPositionBaseByIds(Long[] ids);

    /**
     * 删除职位库信息
     *
     * @param id 职位库主键
     * @return 结果
     */
    public int deleteTbpPositionBaseById(Long id);

    /**
     * 改变开放标识
     */
    int editOpenFlag(Long id, Integer openFlag);

    /**
     * 获取分类下最大排序字段
     */
    Integer getMaxSortCode(Integer openFlag);

    /**
     * 改变置顶标识
     */
    int editTop(Long id, Integer topFlag);

    /**
     * 职位库统计
     */
    TbpPositionBaseCount getPositionBaseCount(TbpPositionBase tbpPositionBase);

}