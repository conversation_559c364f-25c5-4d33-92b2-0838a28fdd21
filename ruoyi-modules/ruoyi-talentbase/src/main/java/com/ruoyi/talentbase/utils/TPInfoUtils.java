package com.ruoyi.talentbase.utils;

import com.ruoyi.common.core.enums.JobPostingStatus;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.entity.domain.PdTaskInfo;
import com.ruoyi.common.entity.domain.TPInfoEventEnum;
import com.ruoyi.common.entity.domain.TodoTaskDto;
import com.ruoyi.common.entity.domain.talent.*;
import com.ruoyi.talentbase.service.IPdTaskInfoService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 待办信息工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TPInfoUtils {


    // 待办标识
    public static final int PENDING_FLAG = 0;
    // 任务标识
    public static final int TASK_FLAG = 1;

    // 未读标识
    public static final int UNREAD_FLAG = 0;
    // 已读标识
    public static final int READ_FLAG = 1;

    @NotNull
    private static IPdTaskInfoService service() {
        return SpringUtils.getBean(IPdTaskInfoService.class);
    }

    private static void addOrEdit(@NotNull PdTaskInfo info) {
        if (info.getEventId() == null) {
            // 没有id就写负数类型
            info.setEventId(String.valueOf(-info.getEventType()));
        }
        if (info.getStatus() == null) {
            info.setStatus(0);
        }
        info.setReadFlag(UNREAD_FLAG);
        PdTaskInfo oldInfo = service().selectPdTaskInfoByEventType(info.getEventId(), info.getEventType());
        if (oldInfo != null) {
            info.setId(oldInfo.getId());
            service().updatePdTaskInfo(info);
        } else {
            service().insertPdTaskInfo(info);
        }
    }

    public static void remove(@NotNull String eventId, @NotNull TPInfoEventEnum eventType) {
        PdTaskInfo oldInfo = service().selectPdTaskInfoByEventType(eventId, eventType.getCode());
        if (oldInfo != null) {
            service().deletePdTaskInfoById(oldInfo.getId());
        }
    }


    // 增加合同到期待办信息
    // flag 1:离职扣减
    public static void addContractExpireTodo(TPInfoEventEnum eventType, Integer count, Integer flag) {
        // 组装信息
        int code = eventType.getCode();
        PdTaskInfo info = new PdTaskInfo();
        info.setTitle("有" + count + "人的劳务合同需及时安排签约！");
        info.setUserId(-(long) code);
        info.setEventType(code);
        if (flag != 1) {
            info.setEventTime(new Date());
        }
        info.setType(PENDING_FLAG);
        addOrEdit(info);
    }

    // 增加线上面试待办信息
    public static void addOnlineInterviewTodo(@NotNull TbOnlineInterview onlineInterview) {
        log.info("增加线上面试待办信息,onlineInterview:{}", onlineInterview.toString());
        PdTaskInfo info = new PdTaskInfo();
        String name = onlineInterview.getName();
        InterviewStatusEnum statusEnum = InterviewStatusEnum.fromCode(onlineInterview.getInterviewStatus());
        if (statusEnum != null && (statusEnum.equals(InterviewStatusEnum.COMPLETED) || statusEnum.equals(InterviewStatusEnum.EXPIRED))) {
            // 拼接待办标题
            String title = name + "线上面试" + statusEnum.getDesc();
            info.setTitle(title);
            info.setUserId(onlineInterview.getOperatorId());
            info.setEventId(String.valueOf(onlineInterview.getId()));
            info.setCreateTime(new Date());
            info.setEventType(TPInfoEventEnum.ONLINE_INTERVIEW.getCode());
            info.setStatus(statusEnum.equals(InterviewStatusEnum.COMPLETED) ? 0 : 1);
            info.setEventTime(new Date());
            info.setType(PENDING_FLAG);
            addOrEdit(info);
        }
    }

    // 移除线上面试待办信息
    public static void removeOnlineInterviewTodo(Long eventId) {
        remove(String.valueOf(eventId), TPInfoEventEnum.ONLINE_INTERVIEW);
    }

    // 增加简历解析的任务信息
    public static void addResumeParseTask(@NotNull TbAnalyseTask analyseTask) {
        PdTaskInfo info = new PdTaskInfo();
        if (analyseTask.getStatus() == TaskStatusEnum.COMPLETED.getCode()) {
            info.setTitle("'" + analyseTask.getTaskName() + "'已完成");
        }else{
            info.setTitle("'" + analyseTask.getTaskName() + "'进行中");
        }
        info.setUserId(analyseTask.getUserId());
        info.setEventId(analyseTask.getId().toString());
        info.setEventTime(new Date());
        info.setEventType(TPInfoEventEnum.RESUME_ANALYSIS_PROGRESS.getCode());
        info.setStatus(analyseTask.getStatus());
        info.setType(TASK_FLAG);
        addOrEdit(info);
    }

    // 移除简历解析任务
    public static void removeResumeParseTask(Long eventId) {
        remove(String.valueOf(eventId), TPInfoEventEnum.RESUME_ANALYSIS_PROGRESS);
    }

    // 增加寻才任务的待办信息
    public static void addFindTalentTask(@NotNull TbTaskPosting taskPosting) {
        Integer status = taskPosting.getStatus();
        if (status == null) {
            log.info("寻才任务状态为空");
            return;
        }
        if (status.equals(JobPostingStatus.DRAFT.getCode()) || status.equals(JobPostingStatus.PAUSED.getCode())) {
            log.info("寻才任务状态为草稿或暂停，不添加待办");
            return;
        }
        PdTaskInfo info = new PdTaskInfo();
        info.setUserId(taskPosting.getUserId());
        if (taskPosting.getStatus().equals(JobPostingStatus.COMPLETED.getCode())) {
            info.setTitle(taskPosting.getTaskName() + "完成");
        } else if (taskPosting.getStatus().equals(JobPostingStatus.FAILED.getCode())) {
            info.setTitle(taskPosting.getTaskName() + ",任务失败");
        } else {
            info.setTitle(taskPosting.getTaskName());
        }
        if (taskPosting.getStartTime() != null) {
            info.setEventTime(taskPosting.getStartTime());
        } else {
            info.setEventTime(new Date());
        }
        info.setEventId(taskPosting.getId().toString());
        info.setEventType(TPInfoEventEnum.TALENT_TASK_PROGRESS.getCode());
        info.setStatus(status);
        info.setType(TASK_FLAG);
        addOrEdit(info);
    }

    // 移除寻才任务
    public static void removeFindTalentTask(Long eventId) {
        remove(String.valueOf(eventId), TPInfoEventEnum.TALENT_TASK_PROGRESS);
    }

    // 知识库文件解析待办
    public static void addKnowledgeFileParseTodo(TodoTaskDto dto) {
        // 组装对象
        PdTaskInfo info = new PdTaskInfo();
        // title 必须在agent传输时定义好，不能在这定义
        info.setTitle(dto.getTitle());
        info.setUserId(dto.getUserId());
        info.setEventId(dto.getEventId());
        info.setEventTime(dto.getEventTime());
        if (dto.getKbId() != null) {
            info.setGroupId(dto.getKbId().toString());
        }
        info.setEventType(TPInfoEventEnum.KNOWLEDGE.getCode());
        if (dto.getStatus() != null) {
            info.setStatus(dto.getStatus());
        }
        info.setType(PENDING_FLAG);
        addOrEdit(info);
    }

    // 合同分析待办
    public static void addContractAnalysisTodo(TodoTaskDto dto) {
        // 组装对象
        PdTaskInfo info = new PdTaskInfo();
        // title 必须在agent传输时定义好，不能在这定义
        info.setTitle(dto.getTitle());
        info.setUserId(dto.getUserId());
        info.setEventId(dto.getEventId());
        info.setEventTime(dto.getEventTime());
        info.setEventType(TPInfoEventEnum.CONTRACT.getCode());
        Integer status = dto.getStatus();
        if (status != null) {
            info.setStatus(status);
        }
        info.setType(PENDING_FLAG);
        addOrEdit(info);
    }

}
