package com.ruoyi.talentbase.utils;

import com.alibaba.fastjson.JSON;
import com.ruoyi.talentbase.domain.dto.InterviewEvaluationResponse;
import com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion;
import com.ruoyi.talentbase.domain.vo.ResumeInfoVo;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.ConnectException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Agent API工具类
 * 用于与知识库API进行交互
 */
@Component
public class TalentAgentApiUtil {
    private static final Logger log = LoggerFactory.getLogger(TalentAgentApiUtil.class);

    @Value("${agent.base-url}")
    private String baseUrl;

    // 创建OkHttpClient实例
    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();

    /**
     * 生成面试评价
     *
     * @param text 面试文本内容
     * @return 生成的评价内容
     */
    public InterviewEvaluationResponse generateEvaluation(String text) {
        try {
            String url = baseUrl + "/agentService/api/interview/generateEvaluation";
            log.info("正在生成面试评价。URL: {}", url);
            if ("null".equals(text)) {
                text = "";
            }

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("text", text);

            // 创建请求
            Request request = new Request.Builder()
                    .url(url)
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(requestBody)))
                    .build();

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    Map<String, Object> responseMap = JSON.parseObject(responseBody, Map.class);

                    // 检查响应是否成功
                    if (responseMap.get("code") != null && (int) responseMap.get("code") == 200) {
                        // 修改：使用JSON.parseObject正确转换对象
                        log.info("生成面试评价成功，结果: {}", responseMap.get("data"));
                        InterviewEvaluationResponse evaluation = JSON.parseObject(JSON.toJSONString(responseMap.get("data")), InterviewEvaluationResponse.class);

                        // 计算evaluationInfo中所有score的总和
                        if (evaluation != null && evaluation.getEvaluationInfo() != null) {
                            try {
                                Map<String, Object> evaluationInfoMap = JSON.parseObject(evaluation.getEvaluationInfo(), Map.class);

                                // 只统计children子节点的score，不包含根节点
                                int totalScore = calculateChildrenScore(evaluationInfoMap);

                                evaluation.setScore(totalScore);
                                log.info("计算得到的总评分: {}", totalScore);
                            } catch (Exception e) {
                                log.error("计算评分总和时发生异常", e);
                            }
                        }

                        return evaluation;
                    } else {
                        log.error("生成面试评价失败，错误码: {}, 消息: {}", responseMap.get("code"), responseMap.get("message"));
                        return null;
                    }
                } else {
                    log.error("生成面试评价失败，响应状态: {}", response.code());
                    if (response.body() != null) {
                        log.error("错误响应内容: {}", response.body().string());
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("生成面试评价时发生异常", e);
            return null;
        }
    }

    /**
     * 生成线上面试AI评价
     *
     * @param conversationRecord 面试对话记录
     * @param interviewQuestions 线上面试试题列表
     * @return 生成的评价内容
     */
    public InterviewEvaluationResponse generateOnlineInterviewEvaluation(String conversationRecord, List<TbInterviewJobPaperQuestion> interviewQuestions) {
        try {
            String url = baseUrl + "/agentService/api/interview/generateOnlineInterviewEvaluation";
            log.info("正在生成线上面试AI评价。URL: {}", url);
            
            if ("null".equals(conversationRecord)) {
                conversationRecord = "";
            }

            // 构建面试试题信息
            StringBuilder questionsInfo = new StringBuilder();
            if (interviewQuestions != null && !interviewQuestions.isEmpty()) {
                questionsInfo.append("面试试题信息：\n");
                for (int i = 0; i < interviewQuestions.size(); i++) {
                    TbInterviewJobPaperQuestion question = interviewQuestions.get(i);
                    questionsInfo.append("题目").append(i + 1).append("：").append(question.getQuestion()).append("\n");
                    if (StringUtils.isNotBlank(question.getAnswer())) {
                        questionsInfo.append("标准答案：").append(question.getAnswer()).append("\n");
                    }
                    if (StringUtils.isNotBlank(question.getRemark())) {
                        questionsInfo.append("备注：").append(question.getRemark()).append("\n");
                    }
                    questionsInfo.append("\n");
                }
            }

            // 构建完整的评价内容
            String fullContent = questionsInfo.toString() + "\n面试对话记录：\n" + conversationRecord;

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("conversationRecord", conversationRecord);
            requestBody.put("interviewQuestions", interviewQuestions);
            requestBody.put("fullContent", fullContent);

            // 创建请求
            Request request = new Request.Builder()
                    .url(url)
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(requestBody)))
                    .build();

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    Map<String, Object> responseMap = JSON.parseObject(responseBody, Map.class);

                    // 检查响应是否成功
                    if (responseMap.get("code") != null && (int) responseMap.get("code") == 200) {
                        log.info("生成线上面试AI评价成功，结果: {}", responseMap.get("data"));

                        InterviewEvaluationResponse evaluation;

                        Object dataObj = responseMap.get("data");
                        if (dataObj instanceof Map) {
                            Map<String, Object> dataMap = (Map<String, Object>) dataObj;
                            // 如果返回数据中包含text节点，则取其内部的summary和score
                            Object textObj = dataMap.get("text");
                            if (textObj instanceof Map) {
                                Map<String, Object> textMap = (Map<String, Object>) textObj;
                                evaluation = new InterviewEvaluationResponse();
                                evaluation.setSummary((String) textMap.get("summary"));
                                Object scoreObj = textMap.get("score");
                                if (scoreObj instanceof Number) {
                                    evaluation.setScore(((Number) scoreObj).intValue());
                                }
                                // evaluationInfo 可选
                                if (textMap.get("evaluationInfo") != null) {
                                    evaluation.setEvaluationInfo(JSON.toJSONString(textMap.get("evaluationInfo")));
                                }
                            } else {
                                // 兼容旧结构
                                evaluation = JSON.parseObject(JSON.toJSONString(dataObj), InterviewEvaluationResponse.class);
                            }
                        } else {
                            evaluation = null;
                        }
 
                        // 计算evaluationInfo中所有score的总和
                        if (evaluation != null && evaluation.getEvaluationInfo() != null) {
                            try {
                                Map<String, Object> evaluationInfoMap = JSON.parseObject(evaluation.getEvaluationInfo(), Map.class);

                                // 只统计children子节点的score，不包含根节点
                                int totalScore = calculateChildrenScore(evaluationInfoMap);

                                evaluation.setScore(totalScore);
                                log.info("计算得到的线上面试总评分: {}", totalScore);
                            } catch (Exception e) {
                                log.error("计算线上面试评分总和时发生异常", e);
                            }
                        }

                        return evaluation;
                    } else {
                        log.error("生成线上面试AI评价失败，错误码: {}, 消息: {}", responseMap.get("code"), responseMap.get("message"));
                        return null;
                    }
                } else {
                    log.error("生成线上面试AI评价失败，响应状态: {}", response.code());
                    if (response.body() != null) {
                        log.error("错误响应内容: {}", response.body().string());
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("生成线上面试AI评价时发生异常", e);
            return null;
        }
    }

    // 查询服务版本信息
    public Map<String, Object> getVersion() {
        try {
            Request request = new Request.Builder()
                    .url(baseUrl + "/agentService/api/conversation/version")
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    return JSON.parseObject(responseBody, Map.class);
                } else {
                    log.error("查询agent版本信息失败，响应码: {}", response.code());
                    return null;
                }
            }
        } catch (ConnectException e) {
            log.error("查询版本信息失败，接口连接异常");
            return null;
        } catch (Exception e) {
            log.error("请求接口异常，异常信息：", e);
            return null;
        }
    }

    /**
     * 递归计算evaluationInfo中所有score的总和
     *
     * @param node 当前节点
     * @return 总分
     */
    private int calculateTotalScore(Map<String, Object> node) {
        int totalScore = 0;

        // 获取当前节点的score
        if (node.containsKey("score")) {
            Object scoreObj = node.get("score");
            if (scoreObj instanceof Number) {
                totalScore += ((Number) scoreObj).intValue();
            }
        }

        // 递归处理children
        if (node.containsKey("children")) {
            Object childrenObj = node.get("children");
            if (childrenObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> children = (List<Map<String, Object>>) childrenObj;
                for (Map<String, Object> child : children) {
                    totalScore += calculateTotalScore(child);
                }
            }
        }

        return totalScore;
    }

    /**
     * 只统计children子节点的score，不包含根节点
     *
     * @param node 根节点
     * @return children子节点的score总和
     */
    private int calculateChildrenScore(Map<String, Object> node) {
        int totalScore = 0;

        // 只处理children，不统计根节点的score
        if (node.containsKey("children")) {
            Object childrenObj = node.get("children");
            if (childrenObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> children = (List<Map<String, Object>>) childrenObj;
                for (Map<String, Object> child : children) {
                    totalScore += calculateTotalScore(child);
                }
            }
        }

        return totalScore;
    }

    /**
     * 解析简历文件
     * GET /agentService/api/talent/parse?fileUrl=xxx
     *
     * @param fileUrl 文件URL
     * @param taskId  任务ID
     * @param id      用户ID
     * @return 调用结果map，失败返回null
     */
    public Map<String, Object> parseTalent(String fileUrl, Long taskId, Long id) {
        try {
            String url = baseUrl + "/agentService/api/talent/analyse";
            log.info("解析简历文件，URL:{}, fileUrl:{}, taskId:{}, id:{}", url, fileUrl, taskId, id);
            HttpUrl.Builder builder = HttpUrl.parse(url).newBuilder()
                    .addQueryParameter("fileUrl", fileUrl);
            if (taskId != null) {
                builder.addQueryParameter("taskId", String.valueOf(taskId));
            }
            if (id != null) {
                builder.addQueryParameter("id", String.valueOf(id));
            }
            HttpUrl httpUrl = builder.build();
            Request request = new Request.Builder()
                    .url(httpUrl)
                    .get()
                    .header("Content-Type", "application/json")
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String respStr = response.body().string();
                    @SuppressWarnings("unchecked")
                    Map<String, Object> map = JSON.parseObject(respStr, Map.class);
                    return map;
                }
                log.error("解析简历文件失败, code:{}", response.code());
                return null;
            }
        } catch (Exception e) {
            log.error("调用解析简历接口异常", e);
            return null;
        }
    }

    /**
     * 重新生成面试评价
     */
    public boolean reEvaluate(ResumeInfoVo vo) {
        try {
            if (vo.getCondition()== null) {
                vo.setCondition("");
            }
            String url = baseUrl + "/agentService/api/talent/reEvaluate";
            // 创建请求
            Request request = new Request.Builder()
                    .url(url)
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(vo)))
                    .build();
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String respStr = response.body().string();
                    log.info("重新生成面试评价成功, respStr:{}", respStr);
                    return true;
                }
                log.error("重新生成面试评价失败, code:{}", response.code());
            }
        } catch (Exception e) {
            log.error("生成面试评价时发生异常", e);
            return false;
        }
        return false;
    }

}