package com.ruoyi.talentbase.domain.vo;

public class ResumeInfoVo {

    // 人才Id
    private Long id;

    // 任务Id
    private Long taskId;

    // 任务类型
    private Integer taskType;

    // 简历内容
    private String content;

    // 必须包含：评价条件
    private String condition;

    // Get<PERSON> and Setters (必须提供)
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    public String getCondition() { return condition; }
    public void setCondition(String condition) { this.condition = condition; }
    public Integer getTaskType() { return taskType; }
    public void setTaskType(Integer taskType) { this.taskType = taskType; }
    public Long getTaskId() { return taskId; }
    public void setTaskId(Long taskId) { this.taskId = taskId; }
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
}
