package com.ruoyi.talentbase.domain.dto;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.talentbase.domain.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TbPersonalInfoDto {

    /**
     * id
     */
    private Long id;

    /**
     * 姓名拼音
     */
    private String pinyin;

    /**
     * 寻才简历Id
     */
    @Excel(name = "寻才简历Id")
    private Long taskPersonalId;

    /**
     * 任务Id
     */
    @Excel(name = "任务Id")
    private Long taskId;

    /**
     * 职位库Id
     */
    private Long baseId;

    /**
     * 个人ID来源（1-寻才库，2-简历库）
     */
    @Excel(name = "个人ID来源")
    private Integer personalIdSource;

    /**
     * 用户姓名
     */
    @Excel(name = "用户姓名")
    private String userName;

    /**
     * 用户性别（0男 1女 2未知）
     */
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    private Integer age;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCard;

    /**
     * 民族
     */
    @Excel(name = "民族")
    private String ethnicity;

    /**
     * 婚姻状态（0未婚 1已婚）
     */
    @Excel(name = "婚姻状态", readConverterExp = "0=未婚,1=已婚")
    private String marriageStatus;

    /**
     * 学历
     */
    @Excel(name = "学历")
    private String education;

    /**
     * 职位
     */
    @Excel(name = "职位")
    private String position;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String phone;

    /**
     * 技能
     */
    @Excel(name = "技能")
    private String skills;

    /**
     * 用户邮箱
     */
    @Excel(name = "用户邮箱")
    private String email;

    /**
     * 照片地址
     */
    @Excel(name = "照片地址")
    private String avatar;

    /**
     * 当前地址
     */
    @Excel(name = "当前地址")
    private String currentAddress;

    /**
     * 政治面貌
     */
    @Excel(name = "政治面貌")
    private String politicalStatus;

    /**
     * 工作经验
     */
    @Excel(name = "工作经验")
    private Integer yearsOfExperience;

    /**
     * 入职状态(0:未入职 1:兼职 2:试用 3:实习 4:正式员工 5:离职 6:劳务派遣)
     */
    @Excel(name = "入职状态")
    private String employmentStatus;

    /**
     * 当前工作状态
     */
    @Excel(name = "入职前工作状态")
    private String oldWorkStatus;

    /**
     * 个人简介
     */
    @Excel(name = "个人简介")
    private String introduction;

    /**
     * 外语水平
     */
    @Excel(name = "外语水平")
    private String foreignProficiency;

    /**
     * 专业水平
     */
    @Excel(name = "专业水平")
    private String professionalLevel;

    /**
     * 求职意向
     */
    @Excel(name = "求职意向")
    private String jobIntent;

    /**
     * 期望薪资
     */
    @Excel(name = "期望薪资")
    private String salaryExpectation;

    /**
     * 招聘渠道
     */
    @Excel(name = "招聘渠道")
    private String recruitmentChannel;

    /**
     * 证书
     */
    private String certificate;

    /**
     * 毕业院校
     */
    @Excel(name = "毕业院校")
    private String schoolName;

    /**
     * 所学专业
     */
    @Excel(name = "所学专业")
    private String major;

    /**
     * 入库展示(入库展示 0:展示 1:不展示)
     */
    @Excel(name = "入库展示")
    private Integer storageDisplay;

    /**
     * 是否建档(0:未建档 1:已建档)
     */
    private Integer filedFlag;


    /**
     * 家庭信息
     */
    private List<TbFamilyInfo> familyInfoList;

    /**
     * 个人档案
     */
    private List<TbPersonalFile> personalFileList;

    /**
     * 教育信息
     */
    private List<TbEducationInfo> educationInfoList;

    /**
     * 工作经历
     */
    private List<TbWorkExperience> workExperienceList;

    /**
     * 项目经历
     */
    private List<TbProjectExperience> projectExperienceList;

    /**
     * 入职信息
     */
    private TbUserInfo userInfo;

    private Double minimumEducationScore;

    private Double workExperienceScore;

    private Double jobHoppingRateScore;

    private Double salaryRangeScore;

    private Double totalScore;


}
