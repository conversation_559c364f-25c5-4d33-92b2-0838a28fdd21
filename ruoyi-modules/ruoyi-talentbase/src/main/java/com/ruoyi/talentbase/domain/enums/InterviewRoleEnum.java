package com.ruoyi.talentbase.domain.enums;

/**
 * 面试角色枚举
 *
 * <AUTHOR>
 */
public enum InterviewRoleEnum {
    
    ASSISTANT("assistant", "面试官"),
    USER("user", "面试者");
    
    private final String code;
    private final String description;
    
    InterviewRoleEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static InterviewRoleEnum getByCode(String code) {
        for (InterviewRoleEnum role : values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     */
    public static String getDescriptionByCode(String code) {
        InterviewRoleEnum role = getByCode(code);
        return role != null ? role.getDescription() : "";
    }
} 