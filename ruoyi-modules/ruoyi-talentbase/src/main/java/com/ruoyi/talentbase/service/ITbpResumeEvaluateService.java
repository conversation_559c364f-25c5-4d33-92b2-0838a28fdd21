package com.ruoyi.talentbase.service;

import java.util.List;
import com.ruoyi.talentbase.domain.TbpResumeEvaluate;

/**
 * 简历评估信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-18
 */
public interface ITbpResumeEvaluateService 
{
    /**
     * 查询简历评估信息
     * 
     * @param talentId 简历评估信息主键
     * @return 简历评估信息
     */
    public TbpResumeEvaluate selectTbpResumeEvaluateByTalentId(Long talentId);

    /**
     * 查询简历评估信息列表
     * 
     * @param tbpResumeEvaluate 简历评估信息
     * @return 简历评估信息集合
     */
    public List<TbpResumeEvaluate> selectTbpResumeEvaluateList(TbpResumeEvaluate tbpResumeEvaluate);

    /**
     * 新增简历评估信息
     * 
     * @param tbpResumeEvaluate 简历评估信息
     * @return 结果
     */
    public int insertTbpResumeEvaluate(TbpResumeEvaluate tbpResumeEvaluate);

    /**
     * 修改简历评估信息
     * 
     * @param tbpResumeEvaluate 简历评估信息
     * @return 结果
     */
    public int updateTbpResumeEvaluate(TbpResumeEvaluate tbpResumeEvaluate);

    /**
     * 批量删除简历评估信息
     * 
     * @param talentIds 需要删除的简历评估信息主键集合
     * @return 结果
     */
    public int deleteTbpResumeEvaluateByTalentIds(Long[] talentIds);

    /**
     * 删除简历评估信息信息
     * 
     * @param talentId 简历评估信息主键
     * @return 结果
     */
    public int deleteTbpResumeEvaluateByTalentId(Long talentId);

    /**
     * 重新生成简历评价
     */
    void reEvaluate(Long talentId);

}