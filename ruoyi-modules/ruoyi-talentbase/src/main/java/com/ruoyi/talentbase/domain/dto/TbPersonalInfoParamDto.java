package com.ruoyi.talentbase.domain.dto;


import com.ruoyi.common.core.utils.StringUtils;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TbPersonalInfoParamDto {

    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 用户性别
     */
    private String sex;
    /**
     * 任务Id
     */
    private Long taskId;

    /**
     * 库类型 0.人才库 1.寻才库 2.简历库
     */
    private Integer personalIdSource;
    /**
     * 库类型
     */
    private Long baseId;
    /**
     * 学历
     */
    private Integer education;

    /**
     * 排序字段 0评分 1经验 2年龄 3名字 4 创建时间
     */
    private Integer orderField;

    /**
     * 排序 0升序 1降序
     */
    private Integer sort;

    /**
     * 选中列表记录Id
     */
    private List<TbPersonalSelectedDto> selectedList;

    /**
     * 入库展示(入库展示 0:展示 1:不展示)
     */
    private Integer storageDisplay;

    /**
     * 是否建档(0:未建档 1:已建档)
     */
    private Integer filedFlag;

    /**
     * 是否关注 0关注 1不关注
     */
    private Integer attentionFlag;

    /**
     * 是否合格 0合适 1不合适
     */
    private Integer suitableFlag;

    /**
     * 是否已读 0未读 1已读
     */
    private Integer readFlag;

    /**
     * 年龄下限
     */
    private Integer ageLowerBound;

    /**
     * 年龄上限
     */
    private Integer ageUpperBound;

    /**
     * 经验要求字典值
     */
    private String experienceCode;

    /**
     * 工作经验下限
     */
    private Integer workExperienceLowerBound;

    /**
     * 工作经验上限
     */
    private Integer workExperienceUpperBound;

    /**
     * 简历分析状态（0成功，1待解析，2失败，3解析中）
     */
    private Integer parseStatus;


    // 经验转换方法
    public void workExConvert() {
        if (StringUtils.isEmpty(experienceCode) || experienceCode.equals("all")) {
            return;
        }
        try {
            String[] params = experienceCode.split("_");
            if (params.length == 2) {
                if (params[0].equals("gt")) {
                    workExperienceLowerBound = Integer.parseInt(params[1]);
                } else if (params[0].equals("lt")) {
                    workExperienceUpperBound = Integer.parseInt(params[1]);
                } else {
                    workExperienceLowerBound = Integer.parseInt(params[0]);
                    workExperienceUpperBound = Integer.parseInt(params[1]);
                }
            }
        } catch (Exception e) {
            return;
        }

    }

}
