package com.ruoyi.talentbase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.talentbase.domain.TbPersonalInfo;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoDto;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoParamDto;
import com.ruoyi.talentbase.domain.vo.ResumeListVO;
import com.ruoyi.talentbase.domain.vo.TbPersonalGetInfoVo;
import com.ruoyi.talentbase.domain.vo.TbPersonalInfoVo;
import com.ruoyi.talentbase.domain.vo.TbPersonalTopicCount;

import java.util.List;

/**
 * 个人简历信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface ITbPersonalInfoService extends IService<TbPersonalInfo> {
    /**
     * 查询个人简历信息
     *
     * @param id 个人简历信息主键
     * @return 个人简历信息
     */
    public TbPersonalInfo selectTbPersonalInfoById(Long id);

    /**
     * 查询个人简历信息列表
     *
     * @param tbPersonalInfo 个人简历信息
     * @return 个人简历信息集合
     */
    public List<TbPersonalInfo> selectTbPersonalInfoList(TbPersonalInfo tbPersonalInfo);

    /**
     * 修改个人简历信息
     *
     * @param tbPersonalInfo 个人简历信息
     * @return 结果
     */
    public int updateTbPersonalInfo(TbPersonalInfo tbPersonalInfo);

    /**
     * 批量删除个人简历信息
     *
     * @param ids 需要删除的个人简历信息主键集合
     * @return 结果
     */
    public int deleteTbPersonalInfoByIds(Long[] ids);

    /**
     * 根据id查询个人简历信息
     *
     * @param id
     * @return
     */
    TbPersonalGetInfoVo selectTbPersonalInfoVoById(Long id);

    /**
     * 新增个人简历信息
     *
     * @param infoDto
     * @return
     */
    int insertTbPersonalInfoDto(TbPersonalInfoDto infoDto);

    /**
     * 更新简历标识
     *
     * @param personalInfo
     */
    int updateFlagById(TbPersonalInfo personalInfo);

    /**
     * 根据id查询个人简历信息
     *
     * @param ids
     * @return
     */
    List<TbPersonalInfo> selectByIds(Long[] ids);

    /**
     * 批量更新简历标识
     *
     * @param personalInfo
     * @param ids
     * @return
     */
    int updateFlagByIds(TbPersonalInfo personalInfo, Long[] ids);

    int updateByTaskPid(TbPersonalInfo updateData);

    void updateAge();

    int editBase(TbPersonalInfoDto infoDto);

    int editEducation(TbPersonalInfoDto infoDto);

    int editWork(TbPersonalInfoDto infoDto);

    int selectInfoByTaskIds(Long[] ids, Integer personalIdSource);

    /**
     * 根据任务ID和来源查询个人信息
     *
     * @param id 任务ID
     * @param personalIdSource 个人ID来源（1-寻才库，2-简历库）
     * @return
     */
    TbPersonalInfo selectTbPersonalInfoByTaskPidAndSource(Long id, Integer personalIdSource);

    /**
     * 查询个人简历信息列表
     * @param infoDto 个人简历信息
     * @return 个人简历信息集合
     */
    List<TbPersonalInfoVo> selectTbPersonalInfoVoList(TbPersonalInfoParamDto infoDto);


    // 查询列表
    List<TbPersonalInfo> selectList(TbPersonalInfo personalInfo);

    // 删除人才信息和相关数据
    int removeAll(Long infoId);

    TbPersonalInfo selectTbPersonalInfoByType(Long sourceId, Integer sourceType);

    int updateFamily(TbPersonalInfoDto infoDto);

    int editSkill(TbPersonalInfoDto infoDto);

    int editProject(TbPersonalInfoDto infoDto);

    /**
     * 查询简历列表
     * @param infoDto
     * @return
     */
    List<ResumeListVO> selectVOList(TbPersonalInfoParamDto infoDto);

    /**
     * 根据职位库id查询数量
     * @param personalInfo
     * @return
     */
    int selectCountByBaseId(TbPersonalInfo personalInfo);

    /**
     * 查询职位库人才统计
     * @param infoDto
     * @return
     */
    TbPersonalTopicCount selectTopicCount(TbPersonalInfoParamDto infoDto);
}
