package com.ruoyi.talentbase.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

import static com.ruoyi.common.core.utils.DateUtils.YYYY_MM;

/**
 * 工作经历对象 tb_work_experience
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
public class TbWorkExperience extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 基本信息Id */
    @Excel(name = "基本信息Id")
    private Long personalId;

    /** 工作单位 */
    @Excel(name = "工作单位")
    private String companyName;

    /** 职位 */
    @Excel(name = "职位")
    private String position;

    /** 离职原因 */
    @Excel(name = "离职原因")
    private String resignationReason;

    /** 工作介绍 */
    @Excel(name = "工作介绍")
    private String workIntroduction;

    /** 起止时间 */
    @JsonFormat(pattern = "yyyy-MM")
    @Excel(name = "起止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 截至时间 */
    @JsonFormat(pattern = "yyyy-MM")
    @Excel(name = "截至时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;


    public String infoStr() {
        // 判断字段是否为null,如果为null,则""
        return "工作单位：" + (companyName == null ? "" : companyName)
                + "，职位：" + (position == null ? "" : position)
                + "，工作时间：" + (startTime == null ? "" : DateUtils.dateTimeFormat(startTime, YYYY_MM)) + "至" + (endTime == null ? "" : DateUtils.dateTimeFormat(endTime, YYYY_MM))
                + "，工作介绍：" + (workIntroduction == null ? "" : workIntroduction)
                + "，离职原因：" + (resignationReason == null ? "" : resignationReason);
    }

}
