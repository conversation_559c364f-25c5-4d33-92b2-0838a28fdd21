package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.talentbase.domain.TbpPositionBase;
import com.ruoyi.talentbase.domain.vo.TbpPositionBaseCount;
import com.ruoyi.talentbase.mapper.TbpPositionBaseMapper;
import com.ruoyi.talentbase.service.ITbpPositionBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 职位库Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-18
 */
@Service
public class TbpPositionBaseServiceImpl implements ITbpPositionBaseService {
    @Autowired
    private TbpPositionBaseMapper tbpPositionBaseMapper;
    /**
     * 查询职位库
     *
     * @param id 职位库主键
     * @return 职位库
     */
    @Override
    public TbpPositionBase selectTbpPositionBaseById(Long id) {
        return tbpPositionBaseMapper.selectTbpPositionBaseById(id);
    }

    /**
     * 查询职位库列表
     *
     * @param tbpPositionBase 职位库
     * @return 职位库
     */
    @Override
    public List<TbpPositionBase> selectTbpPositionBaseList(TbpPositionBase tbpPositionBase) {
        return tbpPositionBaseMapper.selectTbpPositionBaseList(tbpPositionBase);
    }

    /**
     * 新增职位库
     *
     * @param tbpPositionBase 职位库
     * @return 结果
     */
    @Override
    public int insertTbpPositionBase(TbpPositionBase tbpPositionBase) {
        tbpPositionBase.setCreateTime(DateUtils.getNowDate());
        tbpPositionBase.setSortTime(System.currentTimeMillis());
        tbpPositionBase.setSort(1);
        return tbpPositionBaseMapper.insertTbpPositionBase(tbpPositionBase);
    }

    /**
     * 修改职位库
     *
     * @param tbpPositionBase 职位库
     * @return 结果
     */
    @Override
    public int updateTbpPositionBase(TbpPositionBase tbpPositionBase) {
        tbpPositionBase.setUpdateTime(DateUtils.getNowDate());
        return tbpPositionBaseMapper.updateTbpPositionBase(tbpPositionBase);
    }

    /**
     * 批量删除职位库
     *
     * @param ids 需要删除的职位库主键
     * @return 结果
     */
    @Override
    public int deleteTbpPositionBaseByIds(Long[] ids) {
        return tbpPositionBaseMapper.deleteTbpPositionBaseByIds(ids);
    }

    /**
     * 删除职位库信息
     *
     * @param id 职位库主键
     * @return 结果
     */
    @Override
    public int deleteTbpPositionBaseById(Long id) {
        return tbpPositionBaseMapper.deleteTbpPositionBaseById(id);
    }

    /**
     * 改变开放标识
     */
    @Override
    public int editOpenFlag(Long id, Integer openFlag) {
        TbpPositionBase base = new TbpPositionBase();
        base.setId(id);
        base.setOpenFlag(openFlag);
        base.setUpdateTime(DateUtils.getNowDate());
        return tbpPositionBaseMapper.updateTbpPositionBase(base);
    }

    /**
     * 获取分类下最大排序字段
     */
    @Override
    public Integer getMaxSortCode(Integer openFlag) {
        return tbpPositionBaseMapper.getMaxSortCode(openFlag);
    }

    /**
     * 改变置顶标识
     */
    @Override
    public int editTop(Long id, Integer topFlag) {
        TbpPositionBase base = new TbpPositionBase();
        base.setId(id);
        base.setSort(topFlag);
        base.setUpdateTime(DateUtils.getNowDate());
        base.setSortTime(System.currentTimeMillis());
        return tbpPositionBaseMapper.updateTbpPositionBase(base);

    }

    /**
     * 职位库统计
     */
    @Override
    public TbpPositionBaseCount getPositionBaseCount(TbpPositionBase tbpPositionBase) {
        return tbpPositionBaseMapper.getPositionBaseCount(tbpPositionBase);
    }
}