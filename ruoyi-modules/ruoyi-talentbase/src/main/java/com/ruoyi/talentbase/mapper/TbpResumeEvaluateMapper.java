package com.ruoyi.talentbase.mapper;

import java.util.List;
import com.ruoyi.talentbase.domain.TbpResumeEvaluate;

/**
 * 简历评估信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-18
 */
public interface TbpResumeEvaluateMapper 
{
    /**
     * 查询简历评估信息
     * 
     * @param talentId 人才信息Id
     * @return 简历评估信息
     */
    public TbpResumeEvaluate selectTbpResumeEvaluateById(Long talentId);

    /**
     * 查询简历评估信息列表
     * 
     * @param tbpResumeEvaluate 简历评估信息
     * @return 简历评估信息集合
     */
    public List<TbpResumeEvaluate> selectTbpResumeEvaluateList(TbpResumeEvaluate tbpResumeEvaluate);

    /**
     * 新增简历评估信息
     * 
     * @param tbpResumeEvaluate 简历评估信息
     * @return 结果
     */
    public int insertTbpResumeEvaluate(TbpResumeEvaluate tbpResumeEvaluate);

    /**
     * 修改简历评估信息
     * 
     * @param tbpResumeEvaluate 简历评估信息
     * @return 结果
     */
    public int updateTbpResumeEvaluate(TbpResumeEvaluate tbpResumeEvaluate);

    /**
     * 删除简历评估信息
     * 
     * @param talentId 人才信息Id
     * @return 结果
     */
    public int deleteTbpResumeEvaluateById(Long talentId);

    /**
     * 批量删除简历评估信息
     * 
     * @param talentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbpResumeEvaluateByIds(Long[] talentIds);
}