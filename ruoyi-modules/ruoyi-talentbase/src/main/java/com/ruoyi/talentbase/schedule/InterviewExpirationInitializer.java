package com.ruoyi.talentbase.schedule;

import com.ruoyi.talentbase.utils.TPInfoUtils;
import com.ruoyi.common.entity.domain.talent.TbInterviewJobPaper;
import com.ruoyi.common.entity.domain.talent.TbOnlineInterview;
import com.ruoyi.talentbase.domain.dto.OnlineInterviewQueryDTO;
import com.ruoyi.common.entity.domain.talent.InterviewStatusEnum;
import com.ruoyi.talentbase.service.ITbInterviewJobPaperService;
import com.ruoyi.talentbase.service.ITbOnlineInterviewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 面试过期状态初始化器
 * 系统启动时处理已过期的面试记录
 */
@Slf4j
@Component
public class InterviewExpirationInitializer implements CommandLineRunner {

    @Autowired
    private ITbOnlineInterviewService interviewService;
    
    @Autowired
    private ITbInterviewJobPaperService jobPaperService;
    
    @Autowired
    private InterviewExpirationScheduler expirationScheduler;

    public InterviewExpirationInitializer() {
        log.info("InterviewExpirationInitializer 构造函数被调用");
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化面试过期状态...");
        
        try {
            log.info("正在注入依赖服务...");
            
            // 检查依赖注入是否成功
            if (interviewService == null) {
                log.error("interviewService 依赖注入失败");
                return;
            }
            if (jobPaperService == null) {
                log.error("jobPaperService 依赖注入失败");
                return;
            }
            if (expirationScheduler == null) {
                log.error("expirationScheduler 依赖注入失败");
                return;
            }
            
            log.info("依赖注入成功，开始查询面试记录...");
            
            // 查询所有未开始和进行中的面试记录
            OnlineInterviewQueryDTO queryDTO = new OnlineInterviewQueryDTO();
            List<TbOnlineInterview> interviews = interviewService.selectTbOnlineInterviewList(queryDTO);
            
            log.info("查询到面试记录数量: {}", interviews.size());
            
            int expiredCount = 0;
            int scheduledCount = 0;
            
            for (TbOnlineInterview interview : interviews) {
                try {
                    if (interview.getInviteTime() == null || interview.getJobPaperId() == null) {
                        log.debug("跳过无效面试记录，ID: {}", interview.getId());
                        continue;
                    }
                    
                    // 获取岗位试卷信息
                    TbInterviewJobPaper jobPaper = jobPaperService.selectTbInterviewJobPaperById(interview.getJobPaperId());
                    if (jobPaper == null || jobPaper.getValidDays() == null) {
                        log.debug("跳过无效岗位试卷，面试ID: {}, 试卷ID: {}", interview.getId(), interview.getJobPaperId());
                        continue;
                    }
                    
                    // 计算过期时间
                    Date expireTime = new Date(interview.getInviteTime().getTime() + jobPaper.getValidDays() * 24 * 60 * 60 * 1000L);
                    Date currentTime = new Date();
                    
                    log.debug("处理面试记录，ID: {}, 邀请时间: {}, 有效天数: {}, 过期时间: {}", 
                            interview.getId(), interview.getInviteTime(), jobPaper.getValidDays(), expireTime);
                    
                    // 检查是否已过期
                    if (currentTime.after(expireTime)) {
                        // 已过期，更新状态
                        if (InterviewStatusEnum.COMPLETED.getCode() != interview.getInterviewStatus() &&
                            InterviewStatusEnum.EXPIRED.getCode() != interview.getInterviewStatus()) {
                            interview.setInterviewStatus(InterviewStatusEnum.EXPIRED.getCode());
                            interview.setUpdateBy("system");
                            interview.setUpdateTime(currentTime);
                            interviewService.updateTbOnlineInterview(interview);
                            expiredCount++;
                            log.info("面试已过期，状态已更新，面试ID: {}", interview.getId());
                            // 记录待办
                            TPInfoUtils.addOnlineInterviewTodo(interview);
                        }
                    } else {
                        // 未过期，设置过期检查任务
                        expirationScheduler.scheduleInterviewExpiration(interview.getId(), interview.getInviteTime(), interview.getJobPaperId());
                        scheduledCount++;
                        log.debug("面试未过期，已设置过期检查任务，面试ID: {}", interview.getId());
                        
                        // 验证任务是否成功保存到数据库
                        expirationScheduler.checkTaskInDatabase(interview.getId());
                    }
                } catch (Exception e) {
                    log.error("处理面试记录时发生异常，面试ID: {}", interview.getId(), e);
                }
            }
            
            log.info("面试过期状态初始化完成，已过期: {} 条，已设置任务: {} 条", expiredCount, scheduledCount);
            
        } catch (Exception e) {
            log.error("初始化面试过期状态失败", e);
            throw e; // 重新抛出异常，确保启动失败
        }
    }
} 