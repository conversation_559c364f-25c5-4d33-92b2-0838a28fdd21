package com.ruoyi.talentbase.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PinyinUtils;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoDto;
import com.ruoyi.talentbase.domain.dto.TbPersonalInfoParamDto;
import com.ruoyi.talentbase.domain.enums.FileSourceEnum;
import com.ruoyi.talentbase.domain.enums.FiledFlagEnum;
import com.ruoyi.talentbase.domain.enums.StorageDisplayEnum;
import com.ruoyi.talentbase.domain.vo.ResumeListVO;
import com.ruoyi.talentbase.domain.vo.TbPersonalGetInfoVo;
import com.ruoyi.talentbase.domain.vo.TbPersonalInfoVo;
import com.ruoyi.talentbase.domain.vo.TbPersonalTopicCount;
import com.ruoyi.talentbase.mapper.TbPersonalInfoMapper;
import com.ruoyi.talentbase.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static com.ruoyi.talentbase.domain.enums.EmploymentEnum.EMPLOYMENT_STATUS;

/**
 * 个人简历信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Slf4j
@Service
public class TbPersonalInfoServiceImpl extends ServiceImpl<TbPersonalInfoMapper, TbPersonalInfo> implements ITbPersonalInfoService {
    @Autowired
    private ITbEducationInfoService tbEducationInfoService;
    @Autowired
    private ITbFamilyInfoService tbFamilyInfoService;
    @Autowired
    private ITbPersonalFileService tbPersonalFileService;
    @Autowired
    private ITbWorkExperienceService tbWorkExperienceService;
    @Autowired
    private ITbProjectExperienceService tbProjectExperienceService;

    /**
     * 查询个人简历信息
     *
     * @param id 个人简历信息主键
     * @return 个人简历信息
     */
    @Override
    public TbPersonalInfo selectTbPersonalInfoById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询个人简历信息列表
     *
     * @param tbPersonalInfo 个人简历信息
     * @return 个人简历信息
     */
    @Override
    public List<TbPersonalInfo> selectTbPersonalInfoList(TbPersonalInfo tbPersonalInfo) {
        return baseMapper.selectTbPersonalInfoList(tbPersonalInfo);
    }

    /**
     * 修改个人简历信息
     *
     * @param tbPersonalInfo 个人简历信息
     * @return 结果
     */
    @Override
    public int updateTbPersonalInfo(TbPersonalInfo tbPersonalInfo) {
        LambdaUpdateWrapper<TbPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TbPersonalInfo :: getId, tbPersonalInfo.getId());
        updateWrapper.set(TbPersonalInfo :: getUpdateTime, DateUtils.getNowDate());
        updateWrapper.set(TbPersonalInfo :: getUpdateBy, tbPersonalInfo.getUpdateBy());
        updateWrapper.set(tbPersonalInfo.getDelFlag() != null, TbPersonalInfo :: getDelFlag, tbPersonalInfo.getDelFlag());
        updateWrapper.set(tbPersonalInfo.getRemark() != null, TbPersonalInfo :: getRemark, tbPersonalInfo.getRemark());
        updateWrapper.set(tbPersonalInfo.getStorageDisplay() != null, TbPersonalInfo :: getStorageDisplay, tbPersonalInfo.getStorageDisplay());
        updateWrapper.set(tbPersonalInfo.getFiledFlag() != null, TbPersonalInfo :: getFiledFlag, tbPersonalInfo.getFiledFlag());
        updateWrapper.set(tbPersonalInfo.getPersonalIdSource() != null, TbPersonalInfo :: getPersonalIdSource, tbPersonalInfo.getPersonalIdSource());
        updateWrapper.set(tbPersonalInfo.getUserName() != null, TbPersonalInfo :: getUserName, tbPersonalInfo.getUserName());
        updateWrapper.set(tbPersonalInfo.getSex() != null, TbPersonalInfo :: getSex, tbPersonalInfo.getSex());
        updateWrapper.set(tbPersonalInfo.getAge() != null, TbPersonalInfo :: getAge, tbPersonalInfo.getAge());
        updateWrapper.set(tbPersonalInfo.getResumeFile() != null, TbPersonalInfo :: getResumeFile, tbPersonalInfo.getResumeFile());
        updateWrapper.set(tbPersonalInfo.getEvaluateStatus() != null, TbPersonalInfo :: getEvaluateStatus, tbPersonalInfo.getEvaluateStatus());
        updateWrapper.set(TbPersonalInfo :: getIdCard, tbPersonalInfo.getIdCard());
        updateWrapper.set(TbPersonalInfo :: getEthnicity, tbPersonalInfo.getEthnicity());
        updateWrapper.set(TbPersonalInfo :: getMarriageStatus, tbPersonalInfo.getMarriageStatus());
        updateWrapper.set(TbPersonalInfo :: getEducation, tbPersonalInfo.getEducation());
        updateWrapper.set(TbPersonalInfo :: getPosition, tbPersonalInfo.getPosition());
        updateWrapper.set(TbPersonalInfo :: getPhone, tbPersonalInfo.getPhone());
        updateWrapper.set(TbPersonalInfo :: getSkills, tbPersonalInfo.getSkills());
        updateWrapper.set(TbPersonalInfo :: getEmail, tbPersonalInfo.getEmail());
        updateWrapper.set(TbPersonalInfo :: getAvatar, tbPersonalInfo.getAvatar());
        updateWrapper.set(TbPersonalInfo :: getCurrentAddress, tbPersonalInfo.getCurrentAddress());
        updateWrapper.set(TbPersonalInfo :: getPoliticalStatus, tbPersonalInfo.getPoliticalStatus());
        updateWrapper.set(TbPersonalInfo :: getYearsOfExperience, tbPersonalInfo.getYearsOfExperience());
        updateWrapper.set(TbPersonalInfo :: getEmploymentStatus, tbPersonalInfo.getEmploymentStatus());
        updateWrapper.set(TbPersonalInfo :: getOldWorkStatus, tbPersonalInfo.getOldWorkStatus());
        updateWrapper.set(TbPersonalInfo :: getIntroduction, tbPersonalInfo.getIntroduction());
        updateWrapper.set(TbPersonalInfo :: getForeignProficiency, tbPersonalInfo.getForeignProficiency());
        updateWrapper.set(TbPersonalInfo :: getProfessionalLevel, tbPersonalInfo.getProfessionalLevel());
        updateWrapper.set(TbPersonalInfo :: getJobIntent, tbPersonalInfo.getJobIntent());
        updateWrapper.set(TbPersonalInfo :: getSalaryExpectation, tbPersonalInfo.getSalaryExpectation());
        updateWrapper.set(TbPersonalInfo :: getRecruitmentChannel, tbPersonalInfo.getRecruitmentChannel());
        updateWrapper.set(TbPersonalInfo :: getCertificate, tbPersonalInfo.getCertificate());
        updateWrapper.set(TbPersonalInfo :: getSchoolName, tbPersonalInfo.getSchoolName());
        updateWrapper.set(TbPersonalInfo :: getMajor, tbPersonalInfo.getMajor());
        updateWrapper.set(TbPersonalInfo :: getMinimumEducationScore, tbPersonalInfo.getMinimumEducationScore());
        updateWrapper.set(TbPersonalInfo :: getWorkExperienceScore, tbPersonalInfo.getWorkExperienceScore());
        updateWrapper.set(TbPersonalInfo :: getJobHoppingRateScore, tbPersonalInfo.getJobHoppingRateScore());
        updateWrapper.set(TbPersonalInfo :: getSalaryRangeScore, tbPersonalInfo.getSalaryRangeScore());
        updateWrapper.set(TbPersonalInfo :: getTotalScore, tbPersonalInfo.getTotalScore());
        updateWrapper.set(TbPersonalInfo :: getCreateArchivesTime, tbPersonalInfo.getCreateArchivesTime());
        return baseMapper.update(null, updateWrapper);
    }

    /**
     * 批量删除个人简历信息
     *
     * @param ids 需要删除的个人简历信息主键
     * @return 结果
     */
    @Override
    public int deleteTbPersonalInfoByIds(Long[] ids) {
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public TbPersonalGetInfoVo selectTbPersonalInfoVoById(Long id) {
        LambdaQueryWrapper<TbPersonalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbPersonalInfo :: getId, id);
        TbPersonalInfo personalInfo = baseMapper.selectOne(queryWrapper);
        if (personalInfo == null) {
            throw new RuntimeException("查询失败,未能获取到相关信息");
        }
        TbPersonalGetInfoVo infoVo = new TbPersonalGetInfoVo();
        BeanUtils.copyProperties(personalInfo, infoVo);
        Long infoVoId = infoVo.getId();
        // 查询学历信息
        TbEducationInfo educationInfo = new TbEducationInfo();
        educationInfo.setPersonalId(infoVoId);
        List<TbEducationInfo> tbEducationInfos = tbEducationInfoService.selectTbEducationInfoList(educationInfo);
        if (tbEducationInfos != null && !tbEducationInfos.isEmpty()) {
            infoVo.setEducationInfoList(tbEducationInfos);
        }
        // 查询家庭信息
        TbFamilyInfo familyInfo = new TbFamilyInfo();
        familyInfo.setPersonalId(infoVoId);
        List<TbFamilyInfo> tbFamilyInfos = tbFamilyInfoService.selectTbFamilyInfoList(familyInfo);
        if (tbFamilyInfos != null && !tbFamilyInfos.isEmpty()) {
            infoVo.setFamilyInfoList(tbFamilyInfos);
        }
        // 获取个人文件信息
        TbPersonalFile personalFile = new TbPersonalFile();
        personalFile.setPersonalId(infoVoId);
        personalFile.setFileSource(FileSourceEnum.BASIC_INFO.getCode());
        List<TbPersonalFile> tbPersonalFiles = tbPersonalFileService.selectTbPersonalFileList(personalFile);
        if (tbPersonalFiles != null && !tbPersonalFiles.isEmpty()) {
            infoVo.setPersonalFileList(tbPersonalFiles);
        }
        // 获取工作经历信息
        TbWorkExperience workExperience = new TbWorkExperience();
        workExperience.setPersonalId(infoVoId);
        List<TbWorkExperience> tbWorkExperiences = tbWorkExperienceService.selectTbWorkExperienceList(workExperience);
        if (tbWorkExperiences != null && !tbWorkExperiences.isEmpty()) {
            infoVo.setWorkExperienceList(tbWorkExperiences);
        }
        // 获取项目经历信息
        TbProjectExperience projectExperience = new TbProjectExperience();
        projectExperience.setPersonalId(infoVoId);
        List<TbProjectExperience> tbProjectExperiences = tbProjectExperienceService.selectTbProjectExperienceList(projectExperience);
        if (tbProjectExperiences != null && !tbProjectExperiences.isEmpty()) {
            infoVo.setProjectExperienceList(tbProjectExperiences);
        }
        return infoVo;
    }

    // 新增简历信息
    @Override
    public int insertTbPersonalInfoDto(TbPersonalInfoDto infoDto) {
        if (infoDto.getTaskPersonalId() != null && infoDto.getPersonalIdSource() != null) {
            LambdaQueryWrapper<TbPersonalInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TbPersonalInfo :: getTaskPersonalId, infoDto.getTaskPersonalId());
            queryWrapper.eq(TbPersonalInfo :: getPersonalIdSource, infoDto.getPersonalIdSource());
            TbPersonalInfo personalInfo = baseMapper.selectOne(queryWrapper);
            if (personalInfo != null) {
                throw new RuntimeException("该信息已经存在人才库中");
            }
        }

        TbPersonalInfo tbPersonalInfo = new TbPersonalInfo();
        BeanUtils.copyProperties(infoDto, tbPersonalInfo);
        tbPersonalInfo.setEmploymentStatus(String.valueOf(EMPLOYMENT_STATUS.getCode()));
        try {
            int row;
            tbPersonalInfo.setCreateTime(DateUtils.getNowDate());
            tbPersonalInfo.setPinyin(PinyinUtils.getPinyin(tbPersonalInfo.getUserName()));
            if (infoDto.getId() != null) {
                tbPersonalInfo.setId(infoDto.getId());
                row = baseMapper.updateById(tbPersonalInfo);
            } else {
                row = baseMapper.insert(tbPersonalInfo);
            }

            Long personalInfoId = tbPersonalInfo.getId();
            infoDto.setId(personalInfoId);
            // 批量插入学历信息
            List<TbEducationInfo> educationInfoList = infoDto.getEducationInfoList();
            if (educationInfoList != null && !educationInfoList.isEmpty()) {
                tbEducationInfoService.deleteAndInsert(educationInfoList, personalInfoId);
            }
            // 批量插入家庭信息
            List<TbFamilyInfo> familyInfoList = infoDto.getFamilyInfoList();
            if (familyInfoList != null && !familyInfoList.isEmpty()) {
                tbFamilyInfoService.deleteAndInsert(familyInfoList, personalInfoId);
            }
            // 批量插入个人文件信息
            List<TbPersonalFile> personalFileList = infoDto.getPersonalFileList();
            if (personalFileList != null && !personalFileList.isEmpty()) {
                // 删除并插入个人文件信息
                tbPersonalFileService.deleteAndInsert(personalFileList, personalInfoId, FileSourceEnum.BASIC_INFO);
            }
            // 批量插入工作经历
            List<TbWorkExperience> taskPersonalInfoList = infoDto.getWorkExperienceList();
            if (taskPersonalInfoList != null && !taskPersonalInfoList.isEmpty()) {
                // 删除并插入工作经历
                tbWorkExperienceService.deleteAndInsert(taskPersonalInfoList, personalInfoId);
            }
            // 批量更新项目经历
            List<TbProjectExperience> projectExperienceList = infoDto.getProjectExperienceList();
            if (projectExperienceList != null && !projectExperienceList.isEmpty()) {
                tbProjectExperienceService.deleteAndInsert(projectExperienceList, personalInfoId);
            }
            return row;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("操作失败,当前信息异常");
        }
    }

    @Override
    public int updateFlagById(TbPersonalInfo personalInfo) {
        LambdaUpdateWrapper<TbPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TbPersonalInfo :: getId, personalInfo.getId());
        updateWrapper.set(TbPersonalInfo :: getUpdateTime, DateUtils.getNowDate());
        updateWrapper.set(personalInfo.getCreateTime() != null, TbPersonalInfo :: getCreateTime, personalInfo.getCreateTime());
        updateWrapper.set(personalInfo.getStorageDisplay() != null, TbPersonalInfo :: getStorageDisplay, personalInfo.getStorageDisplay());
        updateWrapper.set(personalInfo.getFiledFlag() != null, TbPersonalInfo :: getFiledFlag, personalInfo.getFiledFlag());
        updateWrapper.set(personalInfo.getEmploymentStatus() != null, TbPersonalInfo :: getEmploymentStatus, personalInfo.getEmploymentStatus());
        return baseMapper.update(personalInfo, updateWrapper);
    }

    @Override
    public List<TbPersonalInfo> selectByIds(Long[] ids) {
        return baseMapper.selectBatchIds(Arrays.asList(ids));
    }

    @Override
    public int updateFlagByIds(TbPersonalInfo personalInfo, Long[] ids) {
        LambdaUpdateWrapper<TbPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(TbPersonalInfo :: getId, Arrays.asList(ids));
        updateWrapper.set(TbPersonalInfo :: getUpdateTime, DateUtils.getNowDate());
        updateWrapper.set(personalInfo.getStorageDisplay() != null, TbPersonalInfo :: getStorageDisplay, personalInfo.getStorageDisplay());
        if (personalInfo.getFiledFlag() != null) {
            updateWrapper.set(TbPersonalInfo :: getFiledFlag, personalInfo.getFiledFlag());
            if (personalInfo.getFiledFlag().equals(FiledFlagEnum.FILED_FLAG.getCode())) {
                updateWrapper.set(TbPersonalInfo :: getCreateArchivesTime, DateUtils.getNowDate());
            }
        }
        updateWrapper.set(personalInfo.getEmploymentStatus() != null, TbPersonalInfo :: getEmploymentStatus, personalInfo.getEmploymentStatus());
        return baseMapper.update(null, updateWrapper);
    }

    /**
     * 根据任务ID和来源查询个人信息
     *
     * @param id               任务ID
     * @param personalIdSource 个人ID来源（1-寻才库，2-简历库）
     * @return
     */
    @Override
    public TbPersonalInfo selectTbPersonalInfoByTaskPidAndSource(Long id, Integer personalIdSource) {
        LambdaQueryWrapper<TbPersonalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbPersonalInfo :: getTaskPersonalId, id);
        queryWrapper.eq(TbPersonalInfo :: getPersonalIdSource, personalIdSource);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<TbPersonalInfoVo> selectTbPersonalInfoVoList(TbPersonalInfoParamDto infoDto) {
        return baseMapper.selectTbPersonalInfoVoList(infoDto);
    }

    // 查询列表
    @Override
    public List<TbPersonalInfo> selectList(TbPersonalInfo personalInfo) {
        LambdaQueryWrapper<TbPersonalInfo> queryWrapper = new LambdaQueryWrapper<>(personalInfo);
        return baseMapper.selectList(queryWrapper);
    }

    // 物理删除所有人才信息数据
    @Override
    @Transactional
    public int removeAll(Long infoId) {
        try {
            // 删除教育信息
            tbEducationInfoService.deleteByPersonalId(infoId);
            // 删除家庭信息
            tbFamilyInfoService.deleteByPersonalId(infoId);
            // 删除个人文件信息
            tbPersonalFileService.deleteByPersonalId(infoId);
            // 删除工作经历
            tbWorkExperienceService.deleteByPersonalId(infoId);
            // 删除项目经历
            tbProjectExperienceService.deleteTbProjectExperienceByPersonalId(infoId);
            // 删除个人信息
            baseMapper.deleteById(infoId);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    @Override
    public TbPersonalInfo selectTbPersonalInfoByType(Long sourceId, Integer sourceType) {
        LambdaQueryWrapper<TbPersonalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbPersonalInfo :: getTaskPersonalId, sourceId);
        queryWrapper.eq(TbPersonalInfo :: getPersonalIdSource, sourceType);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public int updateFamily(TbPersonalInfoDto infoDto) {
        try {
            return tbFamilyInfoService.deleteAndInsert(infoDto.getFamilyInfoList(), infoDto.getId());
        } catch (Exception e) {
            log.error("操作失败,当前信息异常", e);
        }
        return 0;
    }

    @Override
    public int editSkill(TbPersonalInfoDto infoDto) {
        try {
            LambdaUpdateWrapper<TbPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TbPersonalInfo :: getId, infoDto.getId());
            updateWrapper.set(TbPersonalInfo :: getProfessionalLevel, infoDto.getProfessionalLevel());
            updateWrapper.set(TbPersonalInfo :: getForeignProficiency, infoDto.getForeignProficiency());
            updateWrapper.set(TbPersonalInfo :: getSkills, infoDto.getSkills());
            updateWrapper.set(TbPersonalInfo :: getCertificate, infoDto.getCertificate());
            updateWrapper.set(TbPersonalInfo :: getUpdateTime, DateUtils.getNowDate());
            baseMapper.update(null, updateWrapper);
            return tbPersonalFileService.deleteAndInsert(infoDto.getPersonalFileList(), infoDto.getId(), FileSourceEnum.BASIC_INFO);
        } catch (Exception e) {
            log.error("操作失败,当前信息异常", e);
        }
        return 0;
    }

    @Override
    public int editProject(TbPersonalInfoDto infoDto) {
        try {
            return tbProjectExperienceService.deleteAndInsert(infoDto.getProjectExperienceList(), infoDto.getId());
        } catch (Exception e) {
            log.error("操作失败,当前信息异常", e);
        }
        return 0;
    }

    @Override
    public List<ResumeListVO> selectVOList(TbPersonalInfoParamDto infoDto) {
        return baseMapper.selectVOList(infoDto);
    }

    /**
     * 根据职位库id查询数量
     * @param personalInfo
     * @return
     */
    @Override
    public int selectCountByBaseId(TbPersonalInfo personalInfo) {
        LambdaQueryWrapper<TbPersonalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(personalInfo.getBaseId() != null, TbPersonalInfo :: getBaseId, personalInfo.getBaseId());
        queryWrapper.eq(personalInfo.getAttentionFlag() != null, TbPersonalInfo :: getAttentionFlag, personalInfo.getAttentionFlag());
        queryWrapper.eq(personalInfo.getSuitableFlag() != null, TbPersonalInfo :: getSuitableFlag, personalInfo.getSuitableFlag());
        Long count = baseMapper.selectCount(queryWrapper);
        if (count == null) {
            return 0;
        }
        return count.intValue();
    }

    /**
     * 查询职位库人才统计
     * @param infoDto
     * @return
     */
    @Override
    public TbPersonalTopicCount selectTopicCount(TbPersonalInfoParamDto infoDto) {
        return baseMapper.selectTopicCount(infoDto);
    }

    @Override
    public int updateByTaskPid(TbPersonalInfo updateData) {
        LambdaUpdateWrapper<TbPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TbPersonalInfo :: getTaskPersonalId, updateData.getTaskPersonalId());
        if (updateData.getPersonalIdSource() != null) {
            updateWrapper.eq(TbPersonalInfo :: getPersonalIdSource, updateData.getPersonalIdSource());
        }
        updateWrapper.set(updateData.getStorageDisplay() != null, TbPersonalInfo :: getStorageDisplay, updateData.getStorageDisplay());
        if (updateData.getFiledFlag() != null) {
            updateWrapper.set(TbPersonalInfo :: getFiledFlag, updateData.getFiledFlag());
            if (updateData.getFiledFlag().equals(FiledFlagEnum.FILED_FLAG.getCode())) {
                updateWrapper.set(TbPersonalInfo :: getCreateArchivesTime, DateUtils.getNowDate());
            }
        }
        updateWrapper.set(updateData.getEmploymentStatus() != null, TbPersonalInfo :: getEmploymentStatus, updateData.getEmploymentStatus());
        updateWrapper.set(TbPersonalInfo :: getCreateTime, DateUtils.getNowDate());
        updateWrapper.set(TbPersonalInfo :: getUpdateTime, DateUtils.getNowDate());
        return baseMapper.update(updateData, updateWrapper);
    }

    @Override
    public void updateAge() {
        LambdaUpdateWrapper<TbPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.setSql("age = age + 1");
        updateWrapper.isNotNull(TbPersonalInfo :: getAge);
        baseMapper.update(null, updateWrapper);
    }

    // 基础信息更新
    @Override
    public int editBase(TbPersonalInfoDto infoDto) {
        LambdaUpdateWrapper<TbPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TbPersonalInfo :: getId, infoDto.getId());
        updateWrapper.set(infoDto.getUserName() != null, TbPersonalInfo :: getUserName, infoDto.getUserName());
        updateWrapper.set(infoDto.getSex() != null, TbPersonalInfo :: getSex, infoDto.getSex());
        updateWrapper.set(infoDto.getAge() != null, TbPersonalInfo :: getAge, infoDto.getAge());
        updateWrapper.set(TbPersonalInfo :: getAvatar, infoDto.getAvatar());
        updateWrapper.set(TbPersonalInfo :: getPhone, infoDto.getPhone());
        updateWrapper.set(TbPersonalInfo :: getEmail, infoDto.getEmail());
        updateWrapper.set(TbPersonalInfo :: getCurrentAddress, infoDto.getCurrentAddress());
        updateWrapper.set(TbPersonalInfo :: getEducation, infoDto.getEducation());
        updateWrapper.set(TbPersonalInfo :: getEthnicity, infoDto.getEthnicity());
        updateWrapper.set(TbPersonalInfo :: getMarriageStatus, infoDto.getMarriageStatus());
        updateWrapper.set(TbPersonalInfo :: getPoliticalStatus, infoDto.getPoliticalStatus());
        updateWrapper.set(TbPersonalInfo :: getMajor, infoDto.getMajor());
        updateWrapper.set(TbPersonalInfo :: getSchoolName, infoDto.getSchoolName());
        updateWrapper.set(TbPersonalInfo :: getUpdateTime, DateUtils.getNowDate());
        updateWrapper.set(TbPersonalInfo :: getIntroduction, infoDto.getIntroduction());
        updateWrapper.set(TbPersonalInfo :: getIdCard, infoDto.getIdCard());
        try {
            baseMapper.update(null, updateWrapper);
        } catch (Exception e) {
            log.error("操作失败,当前信息异常", e);
            return 0;
        }
        return 1;
    }

    // 教育信息更新
    @Override
    public int editEducation(TbPersonalInfoDto infoDto) {
        try {
            LambdaUpdateWrapper<TbPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TbPersonalInfo :: getId, infoDto.getId());
            updateWrapper.set(TbPersonalInfo :: getUpdateTime, DateUtils.getNowDate());
            baseMapper.update(null, updateWrapper);
            tbEducationInfoService.deleteAndInsert(infoDto.getEducationInfoList(), infoDto.getId());
        } catch (Exception e) {
            log.error("操作失败,当前信息异常", e);
            return 0;
        }
        return 1;
    }

    // 工作信息更新
    @Override
    public int editWork(TbPersonalInfoDto infoDto) {
        LambdaUpdateWrapper<TbPersonalInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TbPersonalInfo :: getId, infoDto.getId());
        updateWrapper.set(TbPersonalInfo :: getUpdateTime, DateUtils.getNowDate());
        try {
            baseMapper.update(null, updateWrapper);
            tbWorkExperienceService.deleteAndInsert(infoDto.getWorkExperienceList(), infoDto.getId());
        } catch (Exception e) {
            log.error("操作失败,当前信息异常", e);
            return 0;
        }
        return 1;
    }

    // 查询是否存有该信息
    @Override
    public int selectInfoByTaskIds(Long[] ids, Integer personalIdSource) {
        LambdaQueryWrapper<TbPersonalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TbPersonalInfo :: getTaskPersonalId, Arrays.asList(ids));
        queryWrapper.eq(TbPersonalInfo :: getPersonalIdSource, personalIdSource);
        queryWrapper.eq(TbPersonalInfo :: getStorageDisplay, StorageDisplayEnum.STORAGE_DISPLAY.getCode());
        Long count = baseMapper.selectCount(queryWrapper);
        return count.intValue();
    }
}
