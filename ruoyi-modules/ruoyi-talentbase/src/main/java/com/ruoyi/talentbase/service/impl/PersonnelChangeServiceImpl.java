package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.talentbase.domain.ChangeRecord;
import com.ruoyi.talentbase.domain.TbUserUndergo;
import com.ruoyi.talentbase.domain.enums.ChangeTypeEnum;
import com.ruoyi.talentbase.domain.enums.FileSourceEnum;
import com.ruoyi.talentbase.service.IPersonnelChangeService;
import com.ruoyi.talentbase.service.ITbUserUndergoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 人事变更服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class PersonnelChangeServiceImpl implements IPersonnelChangeService {
    
    @Autowired
    private ITbUserUndergoService tbUserUndergoService;
    
    public void recordChange(Long personalId, ChangeTypeEnum changeType, 
                           String before, String after, String operator, String operationType, String batchId) {
        if (personalId == null || changeType == null) {
            return;
        }
        TbUserUndergo undergo = new TbUserUndergo();
        undergo.setPersonalId(personalId);
        undergo.setAffairsName("在职信息调整");
        undergo.setOperateTime(DateUtils.resetting(DateUtils.getNowDate()));
        undergo.setChangeType(changeType.getCode());
        undergo.setChangeBefore(before);
        undergo.setChangeAfter(after);
        undergo.setRemark(String.format("%s：%s → %s", 
            changeType.getDesc(), 
            getDisplayValue(before), 
            getDisplayValue(after)));
        undergo.setCreateBy(operator);
        undergo.setCreateTime(new Date());
        undergo.setFileCode(FileSourceEnum.EXPERIENCE_INFO.getCode());
        undergo.setOperationType(operationType);
        undergo.setBatchId(batchId);
        tbUserUndergoService.insertTbUserUndergo(undergo);
    }

    @Override
    public void recordChanges(Long personalId, List<ChangeRecord> changes, String operator) {
        if (changes == null || changes.isEmpty()) {
            return;
        }
        for (ChangeRecord change : changes) {
            recordChange(personalId, change.getChangeType(), 
                       change.getBefore(), change.getAfter(), operator, change.getOperationType(), change.getBatchId());
        }
    }

    @Override
    public void recordChange(Long personalId, ChangeTypeEnum changeType, 
                           String before, String after, String operator) {
        recordChange(personalId, changeType, before, after, operator, null, null);
    }
    
    /**
     * 检查值是否发生变更（处理null值）
     */
    public boolean hasChanged(String oldValue, String newValue) {
        // 如果两个值都为null，认为没有变更
        if (oldValue == null && newValue == null) {
            return false;
        }
        // 如果只有一个为null，认为有变更
        if (oldValue == null || newValue == null) {
            return true;
        }
        // 如果都不为null，比较值
        return !oldValue.equals(newValue);
    }
    
    /**
     * 获取显示值（处理null值）
     */
    private String getDisplayValue(String value) {
        return value != null ? value : "无";
    }
} 