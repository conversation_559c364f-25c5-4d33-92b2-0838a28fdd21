package com.ruoyi.talentbase.mapper;

import com.ruoyi.common.entity.domain.talent.TbAnalyseTask;
import com.ruoyi.talentbase.domain.dto.AnalyseTaskQueryDTO;
import com.ruoyi.talentbase.domain.dto.ResumeListQueryDTO;
import com.ruoyi.talentbase.domain.vo.ResumeListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 简历分析任务Mapper接口
 */
@Mapper
public interface TbAnalyseTaskMapper {

    /**
     * 查询任务
     * @param id 主键
     * @return 任务
     */
    TbAnalyseTask selectTbAnalyseTaskById(Long id);

    /**
     * 查询任务列表
     * @param task 条件
     * @return 列表
     */
    List<TbAnalyseTask> selectTbAnalyseTaskList(TbAnalyseTask task);

    /**
     * 新增任务
     * @param task 任务
     * @return 结果
     */
    int insertTbAnalyseTask(TbAnalyseTask task);

    /**
     * 更新任务
     * @param task 任务
     * @return 结果
     */
    int updateTbAnalyseTask(TbAnalyseTask task);

    /**
     * 删除任务
     * @param id 主键
     * @return 结果
     */
    int deleteTbAnalyseTaskById(Long id);

    /**
     * 批量删除任务
     * @param ids 主键数组
     * @return 结果
     */
    int deleteTbAnalyseTaskByIds(Long[] ids);

    List<TbAnalyseTask> selectTbAnalyseTaskListByQuery(@Param("query") AnalyseTaskQueryDTO query);

    /**
     * 查询简历列表（统一接口，包含文件信息和简历信息）
     */
    List<ResumeListVO> selectResumeList(@Param("query") ResumeListQueryDTO queryDTO);

    /**
     * 查询任务数量
     */
    Integer selectTbAnalyseTaskCount(TbAnalyseTask query);

    /**
     * 根据职位库id查询任务
     */
    TbAnalyseTask selectTbAnalyseTaskByBaseId(TbAnalyseTask query);
}