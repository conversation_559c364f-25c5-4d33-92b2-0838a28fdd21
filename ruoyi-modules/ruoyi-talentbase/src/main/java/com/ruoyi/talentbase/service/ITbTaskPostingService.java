package com.ruoyi.talentbase.service;

import com.ruoyi.common.core.enums.JobPostingStatus;
import com.ruoyi.common.entity.domain.talent.TbTaskPosting;

import java.util.List;

/**
 * 查找人才任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface ITbTaskPostingService 
{
    /**
     * 查询查找人才任务
     * 
     * @param id 查找人才任务主键
     * @return 查找人才任务
     */
    public TbTaskPosting selectTbTaskPostingById(Long id);

    /**
     * 查询查找人才任务列表
     * 
     * @param tbJobPosting 查找人才任务
     * @return 查找人才任务集合
     */
    public List<TbTaskPosting> selectTbTaskPostingList(TbTaskPosting tbJobPosting);

    /**
     * 新增查找人才任务
     * 
     * @param tbJobPosting 查找人才任务
     * @return 结果
     */
    public int insertTbTaskPosting(TbTaskPosting tbJobPosting);

    /**
     * 修改查找人才任务
     * 
     * @param tbJobPosting 查找人才任务
     * @return 结果
     */
    public int updateTbTaskPosting(TbTaskPosting tbJobPosting);

    /**
     * 批量删除查找人才任务
     * 
     * @param ids 需要删除的查找人才任务主键集合
     * @return 结果
     */
    public int deleteTbTaskPostingByIds(Long[] ids);

    /**
     * 删除查找人才任务信息
     * 
     * @param id 查找人才任务主键
     * @return 结果
     */
    public int deleteTbTaskPostingById(Long id);

    /**
     * 根据用户id查询任务数量
     * @param jobPosting
     * @return
     */
    Integer selectTbTaskPostingCount(TbTaskPosting jobPosting);

    // 更新状态
    int updateTbTaskPostingStatus(TbTaskPosting jobPosting);


    /**
     * 根据职位库id查询任务
     * @param baseId
     * @return
     */
    TbTaskPosting selectTbTaskPostingByBaseId(Long baseId, JobPostingStatus status);

    int editStopByBaseId(Long id);
}
