package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.talentbase.domain.*;
import com.ruoyi.talentbase.domain.enums.PersonalIdSourceEnum;
import com.ruoyi.talentbase.domain.vo.ResumeInfoVo;
import com.ruoyi.talentbase.domain.vo.TbPersonalGetInfoVo;
import com.ruoyi.talentbase.mapper.TbpResumeEvaluateMapper;
import com.ruoyi.talentbase.service.ITbPersonalInfoService;
import com.ruoyi.talentbase.service.ITbpResumeEvaluateService;
import com.ruoyi.talentbase.utils.TalentAgentApiUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 简历评估信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-18
 */
@Service
public class TbpResumeEvaluateServiceImpl implements ITbpResumeEvaluateService {
    @Autowired
    private TbpResumeEvaluateMapper tbpResumeEvaluateMapper;
    @Autowired
    private ITbPersonalInfoService tbPersonalInfoService;
    @Autowired
    private TalentAgentApiUtil talentAgentApiUtil;

    /**
     * 查询简历评估信息
     *
     * @param talentId 简历评估信息主键
     * @return 简历评估信息
     */
    @Override
    public TbpResumeEvaluate selectTbpResumeEvaluateByTalentId(Long talentId) {
        return tbpResumeEvaluateMapper.selectTbpResumeEvaluateById(talentId);
    }

    /**
     * 查询简历评估信息列表
     *
     * @param tbpResumeEvaluate 简历评估信息
     * @return 简历评估信息
     */
    @Override
    public List<TbpResumeEvaluate> selectTbpResumeEvaluateList(TbpResumeEvaluate tbpResumeEvaluate) {
        return tbpResumeEvaluateMapper.selectTbpResumeEvaluateList(tbpResumeEvaluate);
    }

    /**
     * 新增简历评估信息
     *
     * @param tbpResumeEvaluate 简历评估信息
     * @return 结果
     */
    @Override
    public int insertTbpResumeEvaluate(TbpResumeEvaluate tbpResumeEvaluate) {
        return tbpResumeEvaluateMapper.insertTbpResumeEvaluate(tbpResumeEvaluate);
    }

    /**
     * 修改简历评估信息
     *
     * @param tbpResumeEvaluate 简历评估信息
     * @return 结果
     */
    @Override
    public int updateTbpResumeEvaluate(TbpResumeEvaluate tbpResumeEvaluate) {
        return tbpResumeEvaluateMapper.updateTbpResumeEvaluate(tbpResumeEvaluate);
    }

    /**
     * 批量删除简历评估信息
     *
     * @param talentIds 需要删除的简历评估信息主键
     * @return 结果
     */
    @Override
    public int deleteTbpResumeEvaluateByTalentIds(Long[] talentIds) {
        return tbpResumeEvaluateMapper.deleteTbpResumeEvaluateByIds(talentIds);
    }

    /**
     * 删除简历评估信息信息
     *
     * @param talentId 简历评估信息主键
     * @return 结果
     */
    @Override
    public int deleteTbpResumeEvaluateByTalentId(Long talentId) {
        return tbpResumeEvaluateMapper.deleteTbpResumeEvaluateById(talentId);
    }


    /**
     * 重新生成简历评价
     */
    @Transactional
    @Override
    public void reEvaluate(Long talentId) {
        TbPersonalGetInfoVo infoVo = tbPersonalInfoService.selectTbPersonalInfoVoById(talentId);
        if (infoVo == null) {
            throw new RuntimeException("人才不存在");
        }
        // 提取信息拼接成prompt
        StringBuilder prompt = new StringBuilder();
        prompt.append("根据以下信息生成简历评价：");
        prompt.append("应聘岗位：").append(infoVo.getPosition() == null ? "" : infoVo.getPosition()).append(",");
        prompt.append("工作经验：").append(infoVo.getYearsOfExperience() == null ? "" : infoVo.getYearsOfExperience()).append(",");
        prompt.append("期望薪资：").append(infoVo.getSalaryExpectation() == null ? "" : infoVo.getSalaryExpectation()).append(",");
        List<TbWorkExperience> workExperienceList = infoVo.getWorkExperienceList();
        if (workExperienceList != null) {
            for (int i = 0; i < workExperienceList.size(); i++) {
                String infoStr = workExperienceList.get(i).infoStr();
                prompt.append("工作经历").append(i + 1).append(":").append(infoStr).append(",");
            }
        }
        List<TbProjectExperience> projectExperienceList = infoVo.getProjectExperienceList();
        if (projectExperienceList != null) {
            for (int i = 0; i < projectExperienceList.size(); i++) {
                String infoStr = projectExperienceList.get(i).infoStr();
                prompt.append("项目经历").append(i + 1).append(":").append(infoStr).append(",");
            }
        }
        List<TbEducationInfo> educationInfoList = infoVo.getEducationInfoList();
        if (educationInfoList != null) {
            for (int i = 0; i < educationInfoList.size(); i++) {
                String infoStr = educationInfoList.get(i).infoStr();
                prompt.append("教育经历").append(i + 1).append(":").append(infoStr).append(",");
            }
        }
        ResumeInfoVo resumeInfoVo = new ResumeInfoVo();
        resumeInfoVo.setTaskId(infoVo.getTaskId());
        resumeInfoVo.setTaskType(infoVo.getPersonalIdSource());
        resumeInfoVo.setId(infoVo.getId());
        String promptString = prompt.toString();
        resumeInfoVo.setContent(promptString);
        if (infoVo.getPersonalIdSource() == null) {
            resumeInfoVo.setTaskType(PersonalIdSourceEnum.RECRUIT_POOL.getCode());
        }else if (infoVo.getPersonalIdSource().equals(PersonalIdSourceEnum.RECRUIT_POOL.getCode())){
            resumeInfoVo.setTaskId(0L);
        }
        // 调用重新生成评价
        boolean result = talentAgentApiUtil.reEvaluate(resumeInfoVo);
        // 删除评价内容
        this.deleteTbpResumeEvaluateByTalentId(talentId);
        // 更新评价状态
        TbPersonalInfo personalInfo = new TbPersonalInfo();
        personalInfo.setId(talentId);
        if (StringUtils.isEmpty(promptString) || !result) {
            personalInfo.setEvaluateStatus(2);
        } else {
            personalInfo.setEvaluateStatus(1);
        }
        tbPersonalInfoService.updateById(personalInfo);
    }
}