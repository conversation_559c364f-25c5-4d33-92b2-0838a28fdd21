package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 简历评估信息对象 tbp_resume_evaluate
 *
 * <AUTHOR>
 * @date 2024-08-18
 */
@Data
@TableName("tbp_resume_evaluate")
public class TbpResumeEvaluate extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 人才信息Id
     */
    @TableId
    private Long talentId;

    /**
     * 最低学历分
     */
    private Double minimumEducationScore;

    /**
     * 工作经验分
     */
    private Double workExperienceScore;

    /**
     * 跳槽频率分
     */
    private Double jobHoppingRateScore;

    /**
     * 薪资范围分
     */
    private Double salaryRangeScore;

    /**
     * 总分
     */
    private Double totalScore;

    /**
     * 教育信息评价
     */
    private String educationEvaluation;

    /**
     * 工作经验评价
     */
    private String workExperienceEvaluation;

    /**
     * 跳槽频率评价
     */
    private String jobHoppingRateEvaluation;

    /**
     * 薪资范围评价
     */
    private String salaryRangeEvaluation;

    /**
     * 综合评价
     */
    private String comprehensiveEvaluation;

    /** 学历评分权重 */
    @TableField(exist = false)
    private Integer educationWeight;

    /** 工作经验评分权重 */
    @TableField(exist = false)
    private Integer workExperienceWeight;

    /** 跳槽频率评分权重 */
    @TableField(exist = false)
    private Integer jobHoppingRateWeight;

    /** 薪资范围评分权重 */
    @TableField(exist = false)
    private Integer salaryRangeWeight;

}