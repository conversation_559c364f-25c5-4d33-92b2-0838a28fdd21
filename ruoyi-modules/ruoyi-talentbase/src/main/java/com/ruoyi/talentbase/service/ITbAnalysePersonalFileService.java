package com.ruoyi.talentbase.service;

import com.ruoyi.common.entity.domain.talent.TbAnalysePersonalFile;
import com.ruoyi.talentbase.domain.vo.TbAnalyseFileStateCount;

import java.util.List;

/**
 * 个人档案Service接口
 */
public interface ITbAnalysePersonalFileService {
    TbAnalysePersonalFile selectTbAnalysePersonalFileById(Long id);

    List<TbAnalysePersonalFile> selectTbAnalysePersonalFileList(TbAnalysePersonalFile file);

    int insertTbAnalysePersonalFile(TbAnalysePersonalFile file);

    int updateTbAnalysePersonalFile(TbAnalysePersonalFile file);

    int deleteTbAnalysePersonalFileById(Long id);

    int deleteTbAnalysePersonalFileByIds(Long[] ids);
    
    /**
     * 批量更新文件状态
     * @param fileIds 文件ID列表
     * @param status 状态
     * @return 更新结果
     */
    int batchUpdateStatus(List<Long> fileIds, Integer status);

    TbAnalysePersonalFile selectTbAnalysePersonalFileByPid(Long id);


    /**
     * 查询任务下的文件数量
     * @param taskId 任务ID
     * @return 文件数量
     */
    TbAnalyseFileStateCount selectCount(Long taskId);

}