package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.entity.domain.talent.TbAnalysePersonalFile;
import com.ruoyi.talentbase.domain.vo.TbAnalyseFileStateCount;
import com.ruoyi.talentbase.mapper.TbAnalysePersonalFileMapper;
import com.ruoyi.talentbase.service.ITbAnalysePersonalFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TbAnalysePersonalFileServiceImpl implements ITbAnalysePersonalFileService {

    @Autowired
    private TbAnalysePersonalFileMapper mapper;

    @Override
    public TbAnalysePersonalFile selectTbAnalysePersonalFileById(Long id) {
        return mapper.selectTbAnalysePersonalFileById(id);
    }

    @Override
    public List<TbAnalysePersonalFile> selectTbAnalysePersonalFileList(TbAnalysePersonalFile file) {
        return mapper.selectTbAnalysePersonalFileList(file);
    }

    @Override
    public int insertTbAnalysePersonalFile(TbAnalysePersonalFile file) {
        file.setCreateTime(DateUtils.getNowDate());
        file.setCreateBy(SecurityUtils.getUsername());
        return mapper.insertTbAnalysePersonalFile(file);
    }

    @Override
    public int updateTbAnalysePersonalFile(TbAnalysePersonalFile file) {
        return mapper.updateTbAnalysePersonalFile(file);
    }

    @Override
    public int deleteTbAnalysePersonalFileById(Long id) {
        return mapper.deleteTbAnalysePersonalFileById(id);
    }

    @Override
    public int deleteTbAnalysePersonalFileByIds(Long[] ids) {
        return mapper.deleteTbAnalysePersonalFileByIds(ids);
    }

    @Override
    public int batchUpdateStatus(List<Long> fileIds, Integer status) {
        return mapper.batchUpdateStatus(fileIds, status);
    }

    @Override
    public TbAnalysePersonalFile selectTbAnalysePersonalFileByPid(Long id) {
        return mapper.selectTbAnalysePersonalFileByPid(id);
    }

    @Override
    public TbAnalyseFileStateCount selectCount(Long taskId) {
        return mapper.selectCount(taskId);
    }
} 