package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonInclude;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class TbOfflineInterview extends BaseEntity {
    private Long id;
    private String avatarUrl;
    private Integer avatarRedDot;
    private String name;
    private String gender;
    private String education;
    @Dict(type = SysDictDataEnum.EDUCATION, field = "education")
    private String educationName;
    private Integer age;
    private String jobIntention;
    private String talentSource;
    private String interviewRound;
    private String operator;
    private Integer interviewStatus;
    private Date interviewTime;
    private Date interviewEndTime;
    private String remark;
    private Integer offlineInterviewSource; // 线下面试来源（0-人才表，1-寻才表）
    private Long offlineInterviewSourceId; // 线下面试来源ID
    private Long deptId; // 部门ID
    @TableField(exist = false)
    private String deptName; // 部门名称
    @TableField(exist = false)
    private Double aiScore; // AI评分
    private Long companyId; // 公司ID
    private String depts; // 公司部门路径
    private String jobDesc; // 岗位描述
} 