package com.ruoyi.talentbase.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.entity.domain.talent.TaskStatusEnum;
import com.ruoyi.common.entity.domain.talent.TbAnalyseTask;
import com.ruoyi.talentbase.domain.dto.AnalyseTaskQueryDTO;
import com.ruoyi.talentbase.domain.dto.ResumeListQueryDTO;
import com.ruoyi.talentbase.domain.enums.ParseStatusEnum;
import com.ruoyi.talentbase.domain.vo.ResumeListVO;
import com.ruoyi.talentbase.domain.vo.TbAnalyseFileStateCount;
import com.ruoyi.talentbase.mapper.TbAnalyseTaskMapper;
import com.ruoyi.talentbase.service.ITbAnalyseTaskService;
import com.ruoyi.talentbase.utils.TPInfoUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 简历分析任务Service业务层处理
 */
@Service
public class TbAnalyseTaskServiceImpl implements ITbAnalyseTaskService {

    @Autowired
    private TbAnalyseTaskMapper taskMapper;

    @Override
    public TbAnalyseTask selectTbAnalyseTaskById(Long id) {
        return taskMapper.selectTbAnalyseTaskById(id);
    }

    @Override
    public int insertTbAnalyseTask(TbAnalyseTask task) {
        task.setCreateTime(DateUtils.getNowDate());
        int row = taskMapper.insertTbAnalyseTask(task);
        if (row > 0) {
            TPInfoUtils.addResumeParseTask(task);
        }
        return row;
    }

    @Override
    public int updateTbAnalyseTask(TbAnalyseTask task) {
        task.setUpdateTime(DateUtils.getNowDate());
        return taskMapper.updateTbAnalyseTask(task);
    }

    @Override
    public int deleteTbAnalyseTaskByIds(Long[] ids) {
        return taskMapper.deleteTbAnalyseTaskByIds(ids);
    }

    @Override
    public int deleteTbAnalyseTaskById(Long id) {
        return taskMapper.deleteTbAnalyseTaskById(id);
    }

    @Override
    public List<TbAnalyseTask> selectTbAnalyseTaskList(AnalyseTaskQueryDTO query) {
        return taskMapper.selectTbAnalyseTaskListByQuery(query);
    }

    @Override
    public List<ResumeListVO> selectResumeList(ResumeListQueryDTO queryDTO) {
        return taskMapper.selectResumeList(queryDTO);
    }

    @Override
    public void updateTaskCount(TbAnalyseFileStateCount count) {
        TbAnalyseTask task = taskMapper.selectTbAnalyseTaskById(count.getId());
        if (task == null) {
            return;
        }
        TbAnalyseTask analyseTask = new TbAnalyseTask();
        analyseTask.setId(count.getId());
        analyseTask.setUserId(task.getUserId());
        analyseTask.setTaskName(task.getTaskName());
        analyseTask.setResumeCount(count.getResumeCount());
        analyseTask.setResumeSuccessCount(count.getResumeSuccessCount());
        analyseTask.setResumeFailCount(count.getResumeFailCount());
        // 失败的加成功的不等于总数则是进行中，否则是完成
        if (count.getResumeFailCount() + count.getResumeSuccessCount() != count.getResumeCount()) {
            analyseTask.setStatus(TaskStatusEnum.IN_PROGRESS.getCode());
        } else {
            analyseTask.setStatus(TaskStatusEnum.COMPLETED.getCode());
        }
        if (analyseTask.getStatus() != null) {
            TPInfoUtils.addResumeParseTask(analyseTask);
        }
        taskMapper.updateTbAnalyseTask(analyseTask);
    }

    @Override
    public Integer selectTbAnalyseTaskCount(TbAnalyseTask query) {
        return taskMapper.selectTbAnalyseTaskCount(query);
    }

    /**
     * 根据职位库id查询任务
     */
    @Override
    public TbAnalyseTask selectTbAnalyseTaskByBaseId(Long baseId, TaskStatusEnum status) {
        TbAnalyseTask query = new TbAnalyseTask();
        query.setBaseId(baseId);
        query.setStatus(status.getCode());
        return taskMapper.selectTbAnalyseTaskByBaseId(query);
    }
} 