package com.ruoyi.talentbase.service;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.talentbase.domain.vo.TbAnalyseFileStateCount;
import com.ruoyi.talentbase.utils.TalentAgentApiUtil;

import com.ruoyi.common.entity.domain.talent.TbAnalysePersonalFile;
import com.ruoyi.talentbase.domain.enums.ParseStatusEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 异步解析服务
 *
 * <AUTHOR>
 */
@Service
public class AsyncParseService {

    private static final Logger log = LoggerFactory.getLogger(AsyncParseService.class);

    @Autowired
    private TalentAgentApiUtil talentAgentApiUtil;

    @Autowired
    private ITbAnalysePersonalFileService analysePersonalFileService;

    @Autowired
    private ITbAnalyseTaskService analyseTaskService;

    @Autowired
    private RedisService redisService;

    private static final String REDIS_KEY_TASK_ID = "talent:analyse_personal:";
    
    // 字段
    private static final String CREATE_TASK_TIME = "createTaskTime";
    private static final String ID = "id";
    private static final String EXPIRE_TIME = "expireTime";
    

    /**
     * 异步调用简历解析接口
     */
    @Async
    public void asyncParseTalent(String fileUrl, Long taskId, Long fileId) {
        try {
            log.info("开始异步解析简历 - fileId: {}, taskId: {}", fileId, taskId);
            Map<String, Object> map = new HashMap<>();
            map.put(ID, fileId);
            log.info("设定五分钟过期时间");
            Date date = DateUtils.addSeconds(new Date(), 300);
            map.put(CREATE_TASK_TIME, date);
            log.info("设定两小时强制结束时间");
            Date expireTime = DateUtils.addHours(new Date(), 2);
            map.put(EXPIRE_TIME, expireTime);
            redisService.setCacheObject(REDIS_KEY_TASK_ID + taskId, map);
            //TODO: 查验agent调用java是否可行，如果可行就删除
//            talentAgentApiUtil.parseTalent(fileUrl, taskId, fileId);
            log.info("异步解析简历完成 - fileId: {}, taskId: {}", fileId, taskId);
        } catch (Exception ex) {
            log.error("异步解析简历失败 - fileId: {}, taskId: {}, error: {}", fileId, taskId, ex.getMessage(), ex);
        }
    }

    /**
     * 异步批量重试解析
     */
    @Async
    public void asyncRetryByTask(Long taskId, List<TbAnalysePersonalFile> failedFiles) {
        log.info("异步方法开始执行 - taskId: {}, 文件数量: {}", taskId, failedFiles.size());
        int success = 0;
        int failed = 0;

        // 提取文件ID列表
        List<Long> fileIds = new ArrayList<>();
        for (TbAnalysePersonalFile file : failedFiles) {
            fileIds.add(file.getId());
        }
        log.info("文件ID列表提取完成 - taskId: {}, ID数量: {}", taskId, fileIds.size());

        // 批量更新文件状态为解析中
        try {
            log.info("开始批量更新文件状态 - taskId: {}, 状态: {}", taskId, ParseStatusEnum.PARSING.getCode());
            int updateResult = analysePersonalFileService.batchUpdateStatus(fileIds, ParseStatusEnum.PARSING.getCode());
            log.info("批量更新文件状态完成 - taskId: {}, 更新数量: {}", taskId, updateResult);
        } catch (Exception ex) {
            log.error("批量更新文件状态失败 - taskId: {}, error: {}", taskId, ex.getMessage(), ex);
            return;
        }

        // 异步调用解析接口
        log.info("开始异步调用解析接口 - taskId: {}, 文件数量: {}", taskId, failedFiles.size());
        for (TbAnalysePersonalFile file : failedFiles) {
            try {
                log.debug("调用解析接口 - fileId: {}, taskId: {}, fileUrl: {}", file.getId(), taskId, file.getFileUrl());
                asyncParseTalent(file.getFileUrl(), file.getTaskId(), file.getId());
                success++;
            } catch (Exception ex) {
                failed++;
                log.error("重试解析失败 - fileId: {}, taskId: {}, error: {}", file.getId(), taskId, ex.getMessage(), ex);
            }
        }

        TbAnalyseFileStateCount count = analysePersonalFileService.selectCount(taskId);
        if (count != null) {
            count.setId(taskId);
            analyseTaskService.updateTaskCount(count);
        }
        log.info("任务重试解析完成 - taskId: {}, 成功: {}, 失败: {}", taskId, success, failed);
    }


    // 增加1分钟轮训一次的定时任务
    @Scheduled(cron = "0/30 * * * * ?")
    public void asyncCheckTaskStatus() {
        // 设置局部变量
        boolean checkResult = false;
        log.info("开始查询服务状态检测是否续约");
        Map<String, Object> objectMap = talentAgentApiUtil.getVersion();
        if (objectMap == null || objectMap.get(AjaxResult.CODE_TAG) == null || !objectMap.get(AjaxResult.CODE_TAG).equals(200)) {
            log.info("检测检测服务状态失败，续约失败");
        } else {
            checkResult = true;
        }
        log.info("开始检查是否存有未能正常更新状态的简历");
        Collection<String> list = redisService.keys(REDIS_KEY_TASK_ID + "*");
        for (String key : list) {
            log.info("检查任务状态 - key: {}", key);
            Map<String, Object> map = redisService.getCacheObject(key);
            Date createTaskTime = (Date) map.get(CREATE_TASK_TIME);
            Long id = (Long) map.get(ID);
            // 当前时间
            Date now = new Date();
            // 比较当前时间是否>创建时间
            if (now.getTime() > createTaskTime.getTime()) {
                // 任务时间超时，删除任务并更新状态
                log.info("任务时间超时- key: {}", key);
                // 判断服务状态和强制更新时间是否过期
                Date expireTime = (Date) map.get(EXPIRE_TIME);
                if (checkResult && now.getTime() < expireTime.getTime()) {
                    log.info("服务正常，执行续约 - key: {}", key);
                    Date newDate = DateUtils.addSeconds(now, 300);
                    map.put(CREATE_TASK_TIME, newDate);
                    redisService.setCacheObject(key, map);
                    continue;
                }
                // 更新文件状态
                TbAnalysePersonalFile personalFile = analysePersonalFileService.selectTbAnalysePersonalFileById(id);
                if (personalFile != null && !personalFile.getStatus().equals(ParseStatusEnum.SUCCESS.getCode())) {
                    TbAnalysePersonalFile file = new TbAnalysePersonalFile();
                    file.setId(id);
                    file.setStatus(ParseStatusEnum.FAIL.getCode());
                    analysePersonalFileService.updateTbAnalysePersonalFile(file);
                    // 调用获取所有的文件信息


                }
                redisService.deleteObject(key);
            }
        }
        log.info("检查任务状态完成");
    }


    // 项目启动初始化将所有解析中的文件加入redis中
    @Scheduled(initialDelay = 2000, fixedRate = Long.MAX_VALUE)
    public void initAnalysePersonalFile() {
        log.info("更新未能解析完成的简历文件列表");
        TbAnalysePersonalFile tbAnalysePersonalFile = new TbAnalysePersonalFile();
        tbAnalysePersonalFile.setStatus(ParseStatusEnum.PARSING.getCode());
        List<TbAnalysePersonalFile> list = analysePersonalFileService.selectTbAnalysePersonalFileList(tbAnalysePersonalFile);
        // 将其加入redis中
        for (TbAnalysePersonalFile personalFile : list) {
            log.info("将文件加入redis中 - fileId: {}, taskId: {}", personalFile.getId(), personalFile.getTaskId());
            Map<String, Object> map = new HashMap<>();
            map.put(ID, personalFile.getId());
            map.put(CREATE_TASK_TIME, new Date());
            // 初始化设定强制过期时间为15分钟
            map.put(EXPIRE_TIME, DateUtils.addMinutes(new Date(), 15));
            redisService.setCacheObject(REDIS_KEY_TASK_ID + personalFile.getId(), map);
        }
        log.info("更新未能解析完成的简历文件列表完成");
    }
} 