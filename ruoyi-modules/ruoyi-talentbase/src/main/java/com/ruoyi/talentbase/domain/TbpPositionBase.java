package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.common.entity.domain.talent.TbAnalyseTask;
import com.ruoyi.common.entity.domain.talent.TbTaskPosting;
import lombok.Data;

/**
 * 职位库对象 tbp_position_base
 * 
 * <AUTHOR>
 * @date 2024-08-18
 */
@Data
@TableName("tbp_position_base")
public class TbpPositionBase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 名称 */
    private String name;

    /** 开放标识 0:开放 1:不开放 */
    private Integer openFlag;

    /** 职位描述和职位要求 */
    private String description;

    /** 排序（置顶字段 0:置顶 1:不置顶） */
    private Integer sort;

    /** 排序时间戳 */
    private Long sortTime;

    /** 人员数量 */
    @TableField(exist = false)
    private Integer peopleCount;

    /** 任务 */
    @TableField(exist = false)
    private TbTaskPosting taskPosting;

    /** 简历分析 */
    @TableField(exist = false)
    private TbAnalyseTask analyseTask;

    /** 是否存有进行中的任务 */
    @TableField(exist = false)
    private Integer findCount;
    /** 是否存有进行中的简历分析 */
    @TableField(exist = false)
    private Integer resumeCount;
    /** 关注数量 */
    @TableField(exist = false)
    private Integer followCount;

    /**
     * 任务id
     */
    @TableField(exist = false)
    private Long taskId;
}