package com.ruoyi.talentbase.domain.vo;

import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import lombok.Data;

/**
 * 简历列表 VO
 */
@Data
public class ResumeListVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 简历ID
     */
    private Long resumeId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件名拼音
     */
    private String filePinyin;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件大小描述（格式化显示）
     */
    private String fileSizeDesc;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * personalId（简历基本信息ID）
     */
    private Long personalId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 姓名拼音
     */
    private String pinyin;

    /**
     * 性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 性别描述
     */
    @Dict(type = SysDictDataEnum.SYS_USER_SEX, field = "sex")
    private String sexDesc;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 学历
     */
    private String education;

    @Dict(type = SysDictDataEnum.EDUCATION, field = "education")
    private String educationName;

    /**
     * 工作经验
     */
    private Integer yearsOfExperience;

    /**
     * 在职/离职状态
     */
    private String workStatus;

    /**
     * 求职意向
     */
    private String jobIntent;

    /**
     * 薪资范围
     */
    private String salaryExpectation;

    /**
     * 简历评分
     */
    private Double totalScore;

    /**
     * 简历分析状态（0成功，1解析中，2失败）
     */
    private Integer parseStatus;

    /**
     * 简历分析状态描述
     */
    private String parseStatusDesc;

    /**
     * 上传时间
     */
    private String uploadTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 是否建档（0未建档 1已建档）
     */
    private Integer filedFlag;

    /**
     * 是否建档描述
     */
    private String filedFlagDesc;

    /**
     * 纳入人才库状态（0否 1是）
     */
    private Integer talentPoolStatus;

    /**
     * 纳入人才库状态描述
     */
    private String talentPoolStatusDesc;
    /**
     * 基础信息Id
     */
    private Long baseId;
    /**
     * 关注状态
     */
    private Integer attentionFlag;
    /**
     * 合适状态
     */
    private Integer suitableFlag;
    /**
     * 已读状态
     */
    private Integer readFlag;
    /**
     * 类型 0-人才库 1-寻才库，2-简历库
     */
    private Integer personalIdSource;

} 