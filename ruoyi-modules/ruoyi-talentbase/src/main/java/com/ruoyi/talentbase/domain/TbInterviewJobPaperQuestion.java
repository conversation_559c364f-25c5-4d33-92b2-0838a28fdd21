package com.ruoyi.talentbase.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class TbInterviewJobPaperQuestion extends BaseEntity {
    private Long id;
    private Long jobPaperId;    // 岗位试卷ID
    private String question;    // 题目
    private String answer;      // 答案
    private String remark;      // 备注
    private Integer sort;       // 排序
} 