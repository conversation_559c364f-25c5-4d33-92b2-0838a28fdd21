package com.ruoyi.talentbase.domain.dto;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.talentbase.domain.TbTaskEducationInfo;
import com.ruoyi.talentbase.domain.TbTaskPersonalInfo;
import com.ruoyi.talentbase.domain.TbTaskProjectExperience;
import com.ruoyi.talentbase.domain.TbTaskWorkExperience;
import com.ruoyi.talentbase.domain.enums.PersonalIdSourceEnum;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 【个人简历信息】对象 tb_task_personal_info
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@ToString
public class TbTaskPersonalInfoDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 文件Id
     */
    private Long fileId;
    /**
     * 任务Id
     */
    private Long jobId;

    /**
     * 职位库Id
     */
    private Long baseId;

    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 民族
     */
    private String ethnicity;
    /**
     * 婚姻状态（0未婚 1已婚）
     */
    private String marriageStatus;
    /**
     * 职位
     */
    private String position;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 照片地址
     */
    private String avatar;
    /**
     * 当前地址
     */
    private String currentAddress;
    /**
     * 政治面貌
     */
    private String politicalStatus;
    /**
     * 个人简介
     */
    private String introduction;
    /**
     * 外语水平
     */
    private String foreignProficiency;
    /**
     * 专业水平
     */
    private String professionalLevel;
    /**
     * 求职意向
     */
    private String jobIntent;
    /**
     * 期望薪资
     */
    private String salaryExpectation;
    /**
     * 学历
     */
    private String education;
    /**
     * 毕业院校
     */
    private String schoolName;

    /**
     * 所学专业
     */
    private String major;
    /**
     * 工作经验
     */
    private Integer yearsOfExperience;

    /**
     * 工作状态
     */
    private String workStatus;
    /**
     * 技能
     */
    private List<String> skillList;
    /**
     * 证书标签
     */
    private String certificate;

    // 工作经历
    private List<TbTaskWorkExperience> tbWorkExperienceList;

    // 教育经历
    private List<TbTaskEducationInfo> tbEducationInfoList;
    /**
     * 项目经历
     */
    private List<TbTaskProjectExperience> tbProjectExperienceList;

    private Double minimumEducationScore;
    private String educationEvaluation;
    private Double workExperienceScore;
    private String workExperienceEvaluation;
    private Double jobHoppingRateScore;
    private String jobHoppingRateEvaluation;
    private Double salaryRangeScore;
    private String salaryRangeEvaluation;
    private Double totalScore;
    private String comprehensiveEvaluation;

    private PersonalIdSourceEnum sourceEnum;

    /**
     * 招聘渠道
     */
    @Excel(name = "招聘渠道")
    private String recruitmentChannel;
}
