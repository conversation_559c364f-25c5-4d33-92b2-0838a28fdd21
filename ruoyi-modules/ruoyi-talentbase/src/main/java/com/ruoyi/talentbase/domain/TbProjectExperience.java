package com.ruoyi.talentbase.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

import static com.ruoyi.common.core.utils.DateUtils.YYYY_MM;

/**
 * 项目经历分析表 tb_analyse_project_experience
 */
@Data
public class TbProjectExperience extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /** 基本信息Id */
    private Long personalId;
    
    /** 项目名称 */
    private String projectName;
    
    /** 在项目担任职位 */
    private String jobTitle;
    
    /** 项目描述 */
    private String projectDescription;
    
    /** 职责内容 */
    private String responsibleContent;
    
    /** 项目起始时间 */
    @JsonFormat(pattern = "yyyy-MM")
    private Date startTime;
    
    /** 项目截至时间 */
    @JsonFormat(pattern = "yyyy-MM")
    private Date endTime;
    
    /** 所属公司 */
    private String companyName;
    
    /** 使用技术 */
    private String technologies;
    
    /** 项目规模 */
    private String projectScale;
    
    /** 角色描述 */
    private String roleDescription;
    
    /** 项目成果 */
    private String achievements;

    public String infoStr() {
        // 判断字段是否为null,如果为null,则""

        return "项目名称：" + (projectName == null ? "" : projectName)
                + "，在项目担任职位：" + (jobTitle == null ? "" : jobTitle)
                + "，项目描述：" + (projectDescription == null ? "" : projectDescription)
                + "，职责内容：" + (responsibleContent == null ? "" : responsibleContent)
                + "，项目起始时间：" + (startTime == null ? "" : DateUtils.dateTimeFormat(startTime, YYYY_MM))
                + "，项目截至时间：" + (endTime == null ? "" : DateUtils.dateTimeFormat(endTime, YYYY_MM))
                + "，所属公司：" + (companyName == null ? "" : companyName)
                + "，使用技术：" + (technologies == null ? "" : technologies)
                + "，项目规模：" + (projectScale == null ? "" : projectScale)
                + "，角色描述：" + (roleDescription == null ? "" : roleDescription)
                + "，项目成果：" + (achievements == null ? "" : achievements);
    }
}