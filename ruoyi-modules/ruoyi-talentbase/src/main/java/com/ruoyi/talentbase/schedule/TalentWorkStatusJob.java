package com.ruoyi.talentbase.schedule;

import com.ruoyi.talentbase.domain.TbPersonalInfo;
import com.ruoyi.talentbase.domain.TbUserInfo;
import com.ruoyi.talentbase.service.ITbPersonalInfoService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 更新状态检查任务
 */
@Slf4j
@Component
public class TalentWorkStatusJob implements Job {

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            // 从任务数据中获取更新状态ID
            Long infoId = context.getJobDetail().getJobDataMap().getLong("infoId");
            String status = context.getJobDetail().getJobDataMap().getString("status");


            log.info("开始执行更新状态检查任务，更新状态ID: {}", infoId);

            // 从Spring上下文获取服务
            ApplicationContext applicationContext = getApplicationContext(context);
            ITbPersonalInfoService tbPersonalInfoService = applicationContext.getBean(ITbPersonalInfoService.class);

            // 查询更新状态记录
            TbPersonalInfo personalInfo = tbPersonalInfoService.selectTbPersonalInfoById(infoId);
            if (personalInfo == null) {
                log.warn("未找到人才信息，ID: {}", infoId);
                return;
            }
            // 更新状态状态
            personalInfo.setId(infoId);
            personalInfo.setEmploymentStatus(status);
            personalInfo.setUpdateBy("system");
            personalInfo.setUpdateTime(new java.util.Date());
            tbPersonalInfoService.updateById(personalInfo);

            log.info("更新状态更新成功，更新状态ID: {}", infoId);

        } catch (Exception e) {
            log.error("执行更新状态检查任务失败", e);
            throw new JobExecutionException(e);
        }
    }

    /**
     * 从JobExecutionContext获取Spring ApplicationContext
     */
    private ApplicationContext getApplicationContext(JobExecutionContext context) throws JobExecutionException {
        try {
            return (ApplicationContext) context.getScheduler().getContext().get("applicationContext");
        } catch (Exception e) {
            log.error("获取Spring上下文失败", e);
            throw new JobExecutionException("无法获取Spring上下文", e);
        }
    }
} 