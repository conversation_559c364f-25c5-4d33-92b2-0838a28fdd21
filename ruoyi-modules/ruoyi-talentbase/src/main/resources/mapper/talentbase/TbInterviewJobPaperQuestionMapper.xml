<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbInterviewJobPaperQuestionMapper">
    <resultMap id="TbInterviewJobPaperQuestionResultMap" type="com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion">
        <id property="id" column="id"/>
        <result property="jobPaperId" column="job_paper_id"/>
        <result property="question" column="question"/>
        <result property="answer" column="answer"/>
        <result property="sort" column="sort"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectTbInterviewJobPaperQuestionById" resultMap="TbInterviewJobPaperQuestionResultMap">
        SELECT * FROM tb_interview_job_paper_question WHERE id = #{id}
    </select>

    <select id="selectTbInterviewJobPaperQuestionList" resultMap="TbInterviewJobPaperQuestionResultMap">
        SELECT * FROM tb_interview_job_paper_question
        <where>
            <if test="jobPaperId != null">AND job_paper_id = #{jobPaperId}</if>
            <if test="question != null and question != ''">AND question LIKE CONCAT('%', #{question}, '%')</if>
        </where>
    </select>

    <insert id="insertTbInterviewJobPaperQuestion" parameterType="com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion">
        INSERT INTO tb_interview_job_paper_question
        (job_paper_id, question, answer, sort, remark, create_by, create_time, update_by, update_time)
        VALUES
        (#{jobPaperId}, #{question}, #{answer}, #{sort}, #{remark}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime})
    </insert>

    <update id="updateTbInterviewJobPaperQuestion" parameterType="com.ruoyi.talentbase.domain.TbInterviewJobPaperQuestion">
        UPDATE tb_interview_job_paper_question
        <set>
            <if test="jobPaperId != null">job_paper_id = #{jobPaperId},</if>
            <if test="question != null">question = #{question},</if>
            <if test="answer != null">answer = #{answer},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteTbInterviewJobPaperQuestionById" parameterType="Long">
        DELETE FROM tb_interview_job_paper_question WHERE id = #{id}
    </delete>

    <delete id="deleteTbInterviewJobPaperQuestionByIds" parameterType="Long">
        DELETE FROM tb_interview_job_paper_question WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTbInterviewJobPaperQuestionByJobPaperId" parameterType="Long">
        DELETE FROM tb_interview_job_paper_question WHERE job_paper_id = #{jobPaperId}
    </delete>

    <select id="selectByIds" resultMap="TbInterviewJobPaperQuestionResultMap">
        SELECT * FROM tb_interview_job_paper_question WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper> 