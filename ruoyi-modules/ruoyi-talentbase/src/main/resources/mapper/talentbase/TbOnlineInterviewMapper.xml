<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbOnlineInterviewMapper">
    <resultMap id="TbOnlineInterviewResultMap" type="com.ruoyi.common.entity.domain.talent.TbOnlineInterview">
        <id property="id" column="id"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="avatarRedDot" column="avatar_red_dot"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="education" column="education"/>
        <result property="age" column="age"/>
        <result property="jobInterview" column="job_interview"/>
        <result property="jobIntention" column="job_intention"/>
        <result property="talentSource" column="talent_source"/>
        <result property="operator" column="operator"/>
        <result property="interviewStatus" column="interview_status"/>
        <result property="inviteTime" column="invite_time"/>
        <result property="inviteUrl" column="invite_url"/>
        <result property="inviteCode" column="invite_code"/>
        <result property="jobPaperId" column="job_paper_id"/>
        <result property="interviewTime" column="interview_time"/>
        <result property="interviewEndTime" column="interview_end_time"/>
        <result property="playUrl" column="play_url"/>
        <result property="onlineInterviewSource" column="online_interview_source"/>
        <result property="onlineInterviewSourceId" column="online_interview_source_id"/>
        <result property="operatorId" column="operator_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="selectTbOnlineInterviewById" resultMap="TbOnlineInterviewResultMap">
        SELECT * FROM tb_online_interview WHERE id = #{id}
    </select>

    <select id="selectTbOnlineInterviewList" parameterType="com.ruoyi.talentbase.domain.dto.OnlineInterviewQueryDTO" resultMap="TbOnlineInterviewResultMap">
        SELECT oi.* FROM tb_online_interview oi
        LEFT JOIN tb_interview_job_paper ijp ON oi.job_paper_id = ijp.id
        <where>
            <if test="name != null and name != ''">AND oi.name LIKE CONCAT('%', #{name}, '%')</if>
            <if test="jobInterview != null and jobInterview != ''">AND oi.job_interview LIKE CONCAT('%', #{jobInterview}, '%')</if>
            <if test="jobIntention != null and jobIntention != ''">AND ijp.job_name LIKE CONCAT('%', #{jobIntention}, '%')</if>
            <if test="interviewStatus != null">AND oi.interview_status = #{interviewStatus}</if>
            <if test="gender != null and gender != ''">AND oi.gender = #{gender}</if>
            <if test="education != null and education != ''">AND oi.education = #{education}</if>
            <if test="ageStart != null">AND oi.age &gt;= #{ageStart}</if>
            <if test="ageEnd != null">AND oi.age &lt;= #{ageEnd}</if>
            <if test="inviteTimeStart != null and inviteTimeStart != ''">AND oi.invite_time &gt;= #{inviteTimeStart}</if>
            <if test="inviteTimeEnd != null and inviteTimeEnd != ''">AND oi.invite_time &lt;= #{inviteTimeEnd}</if>
            <if test="interviewTimeStart != null and interviewTimeStart != ''">AND oi.interview_time &gt;= #{interviewTimeStart}</if>
            <if test="interviewTimeEnd != null and interviewTimeEnd != ''">AND oi.interview_time &lt;= #{interviewTimeEnd}</if>
            <if test="onlineInterviewSource != null">AND oi.online_interview_source = #{onlineInterviewSource}</if>
            <if test="onlineInterviewSourceId != null">AND oi.online_interview_source_id = #{onlineInterviewSourceId}</if>
            <if test="jobPaperId != null">AND oi.job_paper_id = #{jobPaperId}</if>
            <if test="deptId != null">AND ijp.dept_id = #{deptId}</if>
        </where>
        ORDER BY oi.create_time DESC
    </select>

    <insert id="insertTbOnlineInterview" parameterType="com.ruoyi.common.entity.domain.talent.TbOnlineInterview" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_online_interview
        (avatar_url, avatar_red_dot, name, gender, education, age, job_interview, job_intention, talent_source, operator, interview_status, invite_time, invite_url, invite_code, job_paper_id, interview_time, interview_end_time, play_url, online_interview_source, online_interview_source_id, operator_id, create_by, create_time, update_by, update_time, remark)
        VALUES
        (#{avatarUrl}, #{avatarRedDot}, #{name}, #{gender}, #{education}, #{age}, #{jobInterview}, #{jobIntention}, #{talentSource}, #{operator}, #{interviewStatus}, #{inviteTime}, #{inviteUrl}, #{inviteCode}, #{jobPaperId}, #{interviewTime}, #{interviewEndTime}, #{playUrl}, #{onlineInterviewSource}, #{onlineInterviewSourceId}, #{operatorId}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark})
    </insert>

    <update id="updateTbOnlineInterview" parameterType="com.ruoyi.common.entity.domain.talent.TbOnlineInterview">
        UPDATE tb_online_interview
        <set>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="avatarRedDot != null">avatar_red_dot = #{avatarRedDot},</if>
            <if test="name != null">name = #{name},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="education != null">education = #{education},</if>
            <if test="age != null">age = #{age},</if>
            <if test="jobInterview != null">job_interview = #{jobInterview},</if>
            <if test="jobIntention != null">job_intention = #{jobIntention},</if>
            <if test="talentSource != null">talent_source = #{talentSource},</if>
            <if test="operator != null">operator = #{operator},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="interviewStatus != null">interview_status = #{interviewStatus},</if>
            <if test="inviteTime != null">invite_time = #{inviteTime},</if>
            <if test="inviteUrl != null">invite_url = #{inviteUrl},</if>
            <if test="inviteCode != null">invite_code = #{inviteCode},</if>
            <if test="jobPaperId != null">job_paper_id = #{jobPaperId},</if>
            <if test="interviewTime != null">interview_time = #{interviewTime},</if>
            <if test="interviewEndTime != null">interview_end_time = #{interviewEndTime},</if>
            <if test="playUrl != null">play_url = #{playUrl},</if>
            <if test="onlineInterviewSource != null">online_interview_source = #{onlineInterviewSource},</if>
            <if test="onlineInterviewSourceId != null">online_interview_source_id = #{onlineInterviewSourceId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteTbOnlineInterviewById" parameterType="Long">
        DELETE FROM tb_online_interview WHERE id = #{id}
    </delete>

    <delete id="deleteTbOnlineInterviewByIds" parameterType="Long">
        DELETE FROM tb_online_interview WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByPInfoId">
        DELETE FROM tb_online_interview WHERE online_interview_source = #{sourceType} AND online_interview_source_id = #{infoId}
    </delete>

    <!-- 根据 inviteUrl 列表批量查询 -->
    <select id="selectByInviteUrls" resultMap="TbOnlineInterviewResultMap">
        SELECT * FROM tb_online_interview WHERE invite_url IN
        <foreach collection="urls" item="u" open="(" separator="," close=")">
            #{u}
        </foreach>
    </select>

    <!-- 批量更新为已过期 -->
    <update id="expireOnlineInterviewsBySource" parameterType="TbOnlineInterview">
        UPDATE tb_online_interview
        SET interview_status = #{interviewStatus},
        update_by = #{updateBy},
        update_time = #{updateTime}
        WHERE online_interview_source = #{onlineInterviewSource}
        AND online_interview_source_id = #{onlineInterviewSourceId}
        AND interview_status = 0
    </update>

    <!-- 统计指定人才的正在进行和已完成的线上面试次数 -->
    <select id="countActiveAndCompletedInterviews" resultType="int">
        SELECT COUNT(*)
        FROM tb_online_interview
        WHERE online_interview_source_id = #{sourceId}
        AND online_interview_source = #{sourceType}
        AND interview_status IN (1, 2)
    </select>
    <select id="selectInterviewListByPerId" resultType="com.ruoyi.common.entity.domain.talent.TbOnlineInterview">
        SELECT * FROM tb_online_interview
        WHERE online_interview_source_id = #{infoId} and online_interview_source = #{talentCode}
    </select>
</mapper>