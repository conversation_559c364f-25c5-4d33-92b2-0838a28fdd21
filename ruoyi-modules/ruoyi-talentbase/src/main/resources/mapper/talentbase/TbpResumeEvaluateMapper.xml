<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbpResumeEvaluateMapper">
    
    <resultMap type="TbpResumeEvaluate" id="TbpResumeEvaluateResult">
        <result property="talentId"    column="talent_id"    />
        <result property="minimumEducationScore"    column="minimum_education_score"    />
        <result property="workExperienceScore"    column="work_experience_score"    />
        <result property="jobHoppingRateScore"    column="job_hopping_rate_score"    />
        <result property="salaryRangeScore"    column="salary_range_score"    />
        <result property="totalScore"    column="total_score"    />
        <result property="educationEvaluation"    column="education_evaluation"    />
        <result property="workExperienceEvaluation"    column="work_experience_evaluation"    />
        <result property="jobHoppingRateEvaluation"    column="job_hopping_rate_evaluation"    />
        <result property="salaryRangeEvaluation"    column="salary_range_evaluation"    />
        <result property="comprehensiveEvaluation"    column="comprehensive_evaluation"    />
    </resultMap>

    <sql id="selectTbpResumeEvaluateVo">
        select talent_id, minimum_education_score, work_experience_score, job_hopping_rate_score, salary_range_score, total_score, education_evaluation, work_experience_evaluation, job_hopping_rate_evaluation, salary_range_evaluation, comprehensive_evaluation from tbp_resume_evaluate
    </sql>

    <select id="selectTbpResumeEvaluateList" parameterType="TbpResumeEvaluate" resultMap="TbpResumeEvaluateResult">
        <include refid="selectTbpResumeEvaluateVo"/>
        <where>  
            <if test="talentId != null"> and talent_id = #{talentId}</if>
            <if test="minimumEducationScore != null"> and minimum_education_score = #{minimumEducationScore}</if>
            <if test="workExperienceScore != null"> and work_experience_score = #{workExperienceScore}</if>
            <if test="jobHoppingRateScore != null"> and job_hopping_rate_score = #{jobHoppingRateScore}</if>
            <if test="salaryRangeScore != null"> and salary_range_score = #{salaryRangeScore}</if>
            <if test="totalScore != null"> and total_score = #{totalScore}</if>
        </where>
    </select>
    
    <select id="selectTbpResumeEvaluateById" parameterType="Long" resultMap="TbpResumeEvaluateResult">
        <include refid="selectTbpResumeEvaluateVo"/>
        where talent_id = #{talentId}
    </select>
        
    <insert id="insertTbpResumeEvaluate" parameterType="TbpResumeEvaluate">
        insert into tbp_resume_evaluate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="talentId != null">talent_id,</if>
            <if test="minimumEducationScore != null">minimum_education_score,</if>
            <if test="workExperienceScore != null">work_experience_score,</if>
            <if test="jobHoppingRateScore != null">job_hopping_rate_score,</if>
            <if test="salaryRangeScore != null">salary_range_score,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="educationEvaluation != null and educationEvaluation != ''">education_evaluation,</if>
            <if test="workExperienceEvaluation != null and workExperienceEvaluation != ''">work_experience_evaluation,</if>
            <if test="jobHoppingRateEvaluation != null and jobHoppingRateEvaluation != ''">job_hopping_rate_evaluation,</if>
            <if test="salaryRangeEvaluation != null and salaryRangeEvaluation != ''">salary_range_evaluation,</if>
            <if test="comprehensiveEvaluation != null and comprehensiveEvaluation != ''">comprehensive_evaluation,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="talentId != null">#{talentId},</if>
            <if test="minimumEducationScore != null">#{minimumEducationScore},</if>
            <if test="workExperienceScore != null">#{workExperienceScore},</if>
            <if test="jobHoppingRateScore != null">#{jobHoppingRateScore},</if>
            <if test="salaryRangeScore != null">#{salaryRangeScore},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="educationEvaluation != null and educationEvaluation != ''">#{educationEvaluation},</if>
            <if test="workExperienceEvaluation != null and workExperienceEvaluation != ''">#{workExperienceEvaluation},</if>
            <if test="jobHoppingRateEvaluation != null and jobHoppingRateEvaluation != ''">#{jobHoppingRateEvaluation},</if>
            <if test="salaryRangeEvaluation != null and salaryRangeEvaluation != ''">#{salaryRangeEvaluation},</if>
            <if test="comprehensiveEvaluation != null and comprehensiveEvaluation != ''">#{comprehensiveEvaluation},</if>
         </trim>
    </insert>

    <update id="updateTbpResumeEvaluate" parameterType="TbpResumeEvaluate">
        update tbp_resume_evaluate
        <trim prefix="SET" suffixOverrides=",">
            <if test="minimumEducationScore != null">minimum_education_score = #{minimumEducationScore},</if>
            <if test="workExperienceScore != null">work_experience_score = #{workExperienceScore},</if>
            <if test="jobHoppingRateScore != null">job_hopping_rate_score = #{jobHoppingRateScore},</if>
            <if test="salaryRangeScore != null">salary_range_score = #{salaryRangeScore},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="educationEvaluation != null and educationEvaluation != ''">education_evaluation = #{educationEvaluation},</if>
            <if test="workExperienceEvaluation != null and workExperienceEvaluation != ''">work_experience_evaluation = #{workExperienceEvaluation},</if>
            <if test="jobHoppingRateEvaluation != null and jobHoppingRateEvaluation != ''">job_hopping_rate_evaluation = #{jobHoppingRateEvaluation},</if>
            <if test="salaryRangeEvaluation != null and salaryRangeEvaluation != ''">salary_range_evaluation = #{salaryRangeEvaluation},</if>
            <if test="comprehensiveEvaluation != null and comprehensiveEvaluation != ''">comprehensive_evaluation = #{comprehensiveEvaluation},</if>
        </trim>
        where talent_id = #{talentId}
    </update>

    <delete id="deleteTbpResumeEvaluateById" parameterType="Long">
        delete from tbp_resume_evaluate where talent_id = #{talentId}
    </delete>

    <delete id="deleteTbpResumeEvaluateByIds" parameterType="Long">
        delete from tbp_resume_evaluate where talent_id in 
        <foreach collection="array" item="talentId" open="(" separator="," close=")">
            #{talentId}
        </foreach>
    </delete>
</mapper>