<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.talentbase.mapper.TbAnalysePersonalInfoMapper">
    <resultMap id="TbAnalysePersonalInfoResult" type="TbAnalysePersonalInfo">
        <result column="id" property="id"/>
        <result column="job_id" property="jobId"/>
        <result column="user_name" property="userName"/>
        <result column="pinyin" property="pinyin"/>
        <result column="sex" property="sex"/>
        <result column="age" property="age"/>
        <result column="id_card" property="idCard"/>
        <result column="ethnicity" property="ethnicity"/>
        <result column="marriage_status" property="marriageStatus"/>
        <result column="education" property="education"/>
        <result column="school_name" property="schoolName"/>
        <result column="major" property="major"/>
        <result column="position" property="position"/>
        <result column="phone" property="phone"/>
        <result column="skills" property="skills"/>
        <result column="certificate" property="certificate"/>
        <result column="email" property="email"/>
        <result column="avatar" property="avatar"/>
        <result column="current_address" property="currentAddress"/>
        <result column="political_status" property="politicalStatus"/>
        <result column="years_of_experience" property="yearsOfExperience"/>
        <result column="work_status" property="workStatus"/>
        <result column="introduction" property="introduction"/>
        <result column="foreign_proficiency" property="foreignProficiency"/>
        <result column="professional_level" property="professionalLevel"/>
        <result column="job_intent" property="jobIntent"/>
        <result column="salary_expectation" property="salaryExpectation"/>
        <result column="recruitment_channel" property="recruitmentChannel"/>
        <result column="minimum_education_score" property="minimumEducationScore"/>
        <result column="work_experience_score" property="workExperienceScore"/>
        <result column="job_hopping_rate_score" property="jobHoppingRateScore"/>
        <result column="salary_range_score" property="salaryRangeScore"/>
        <result column="total_score" property="totalScore"/>
        <result property="educationEvaluation" column="education_evaluation"/>
        <result property="workExperienceEvaluation" column="work_experience_evaluation"/>
        <result property="jobHoppingRateEvaluation" column="job_hopping_rate_evaluation"/>
        <result property="salaryRangeEvaluation" column="salary_range_evaluation"/>
        <result property="comprehensiveEvaluation" column="comprehensive_evaluation"/>
        <result column="talent_pool_status" property="talentPoolStatus"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="filed_flag" property="filedFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, job_id, user_name, pinyin, sex, age, id_card, ethnicity, marriage_status, education, school_name, major,
        position,
        phone, skills, certificate, email, avatar, current_address, political_status, years_of_experience, work_status,
        introduction, foreign_proficiency, professional_level, job_intent, salary_expectation, recruitment_channel,
        minimum_education_score, work_experience_score, job_hopping_rate_score, salary_range_score, total_score,
        talent_pool_status, error_msg, filed_flag, create_by, create_time, update_by, update_time, remark,
        education_evaluation, work_experience_evaluation, job_hopping_rate_evaluation, salary_range_evaluation,
        comprehensive_evaluation
    </sql>

    <select id="selectTbAnalysePersonalInfoList" parameterType="TbAnalysePersonalInfo"
            resultMap="TbAnalysePersonalInfoResult">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_analyse_personal_info
        <where>
            <if test="jobId!=null">AND job_id = #{jobId}</if>
            <if test="userName!=null and userName!=''">AND user_name like concat('%',#{userName},'%')</if>
            <if test="pinyin!=null and pinyin!=''">AND pinyin like concat('%',#{pinyin},'%')</if>
            <if test="sex!=null and sex!=''">AND sex = #{sex}</if>
            <if test="education!=null and education!=''">AND education = #{education}</if>
            <if test="workStatus!=null and workStatus!=''">AND work_status = #{workStatus}</if>
            <if test="filedFlag!=null">AND filed_flag = #{filedFlag}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectTbAnalysePersonalInfoById" parameterType="Long" resultMap="TbAnalysePersonalInfoResult">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_analyse_personal_info WHERE id = #{id}
    </select>

    <insert id="insertTbAnalysePersonalInfo" parameterType="TbAnalysePersonalInfo" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO tb_analyse_personal_info (
        job_id, user_name, pinyin, sex, age, id_card, ethnicity, marriage_status, education, school_name, major,
        position,
        phone, skills, certificate, email, avatar, current_address, political_status, years_of_experience, work_status,
        introduction, foreign_proficiency, professional_level, job_intent, salary_expectation, recruitment_channel,
        minimum_education_score, work_experience_score, job_hopping_rate_score, salary_range_score, total_score,
        talent_pool_status, error_msg, filed_flag, create_by, create_time, update_by, update_time, remark,
        education_evaluation, work_experience_evaluation, job_hopping_rate_evaluation, salary_range_evaluation,
        comprehensive_evaluation
        ) VALUES (
        #{jobId}, #{userName}, #{pinyin}, #{sex}, #{age}, #{idCard}, #{ethnicity}, #{marriageStatus}, #{education},
        #{schoolName}, #{major}, #{position},
        #{phone}, #{skills}, #{certificate}, #{email}, #{avatar}, #{currentAddress}, #{politicalStatus},
        #{yearsOfExperience}, #{workStatus},
        #{introduction}, #{foreignProficiency}, #{professionalLevel}, #{jobIntent}, #{salaryExpectation},
        #{recruitmentChannel},
        #{minimumEducationScore}, #{workExperienceScore}, #{jobHoppingRateScore}, #{salaryRangeScore}, #{totalScore},
        #{talentPoolStatus}, #{errorMsg}, #{filedFlag}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime},
        #{remark}, #{educationEvaluation}, #{workExperienceEvaluation}, #{jobHoppingRateEvaluation}, #{salaryRangeEvaluation},
        #{comprehensiveEvaluation}
        )
    </insert>

    <update id="updateTbAnalysePersonalInfo" parameterType="TbAnalysePersonalInfo">
        UPDATE tb_analyse_personal_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName!=null">user_name = #{userName},</if>
            <if test="pinyin!=null">pinyin = #{pinyin},</if>
            <if test="sex!=null">sex = #{sex},</if>
            <if test="age!=null">age = #{age},</if>
            <if test="idCard!=null">id_card = #{idCard},</if>
            <if test="ethnicity!=null">ethnicity = #{ethnicity},</if>
            <if test="marriageStatus!=null">marriage_status = #{marriageStatus},</if>
            <if test="education!=null">education = #{education},</if>
            <if test="schoolName!=null">school_name = #{schoolName},</if>
            <if test="major!=null">major = #{major},</if>
            <if test="position!=null">position = #{position},</if>
            <if test="phone!=null">phone = #{phone},</if>
            <if test="skills!=null">skills = #{skills},</if>
            <if test="certificate!=null">certificate = #{certificate},</if>
            <if test="email!=null">email = #{email},</if>
            <if test="avatar!=null">avatar = #{avatar},</if>
            <if test="currentAddress!=null">current_address = #{currentAddress},</if>
            <if test="politicalStatus!=null">political_status = #{politicalStatus},</if>
            <if test="yearsOfExperience!=null">years_of_experience = #{yearsOfExperience},</if>
            <if test="workStatus!=null">work_status = #{workStatus},</if>
            <if test="introduction!=null">introduction = #{introduction},</if>
            <if test="foreignProficiency!=null">foreign_proficiency = #{foreignProficiency},</if>
            <if test="professionalLevel!=null">professional_level = #{professionalLevel},</if>
            <if test="jobIntent!=null">job_intent = #{jobIntent},</if>
            <if test="salaryExpectation!=null">salary_expectation = #{salaryExpectation},</if>
            <if test="recruitmentChannel!=null">recruitment_channel = #{recruitmentChannel},</if>
            <if test="minimumEducationScore!=null">minimum_education_score = #{minimumEducationScore},</if>
            <if test="workExperienceScore!=null">work_experience_score = #{workExperienceScore},</if>
            <if test="jobHoppingRateScore!=null">job_hopping_rate_score = #{jobHoppingRateScore},</if>
            <if test="salaryRangeScore!=null">salary_range_score = #{salaryRangeScore},</if>
            <if test="educationEvaluation!=null">education_evaluation = #{educationEvaluation},</if>
            <if test="workExperienceEvaluation!=null">work_experience_evaluation = #{workExperienceEvaluation},</if>
            <if test="jobHoppingRateEvaluation!=null">job_hopping_rate_evaluation = #{jobHoppingRateEvaluation},</if>
            <if test="salaryRangeEvaluation!=null">salary_range_evaluation = #{salaryRangeEvaluation},</if>
            <if test="comprehensiveEvaluation!=null">comprehensive_evaluation = #{comprehensiveEvaluation},</if>
            <if test="totalScore!=null">total_score = #{totalScore},</if>
            <if test="talentPoolStatus!=null">talent_pool_status = #{talentPoolStatus},</if>
            <if test="errorMsg!=null">error_msg = #{errorMsg},</if>
            <if test="filedFlag!=null">filed_flag = #{filedFlag},</if>
            <if test="updateBy!=null">update_by = #{updateBy},</if>
            <if test="updateTime!=null">update_time = #{updateTime},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteTbAnalysePersonalInfoById" parameterType="Long">
        DELETE FROM tb_analyse_personal_info WHERE id = #{id}
    </delete>

    <delete id="deleteTbAnalysePersonalInfoByIds" parameterType="Long[]">
        DELETE FROM tb_analyse_personal_info WHERE id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 