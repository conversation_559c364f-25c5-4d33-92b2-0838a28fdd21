<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.talentbase.mapper.TbAnalysePersonalFileMapper">

    <resultMap id="TbAnalysePersonalFileResult" type="TbAnalysePersonalFile">
        <result column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="personal_id" property="personalId"/>
        <result column="file_name" property="fileName"/>
        <result column="pinyin" property="pinyin"/>
        <result column="file_size" property="fileSize"/>
        <result column="file_url" property="fileUrl"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, personal_id, file_name, pinyin, file_size, file_url, upload_time, status, create_by, create_time,
        update_by, update_time, remark
    </sql>

    <select id="selectTbAnalysePersonalFileList" parameterType="TbAnalysePersonalFile"
            resultMap="TbAnalysePersonalFileResult">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_analyse_personal_file
        <where>
            <if test="personalId!=null">AND personal_id = #{personalId}</if>
            <if test="fileName!=null and fileName!=''">AND file_name LIKE concat('%',#{fileName},'%')</if>
            <if test="status!=null">AND status = #{status}</if>
            <if test="taskId!=null">AND task_id = #{taskId}</if>
        </where>
        ORDER BY upload_time DESC
    </select>

    <select id="selectTbAnalysePersonalFileById" parameterType="Long" resultMap="TbAnalysePersonalFileResult">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_analyse_personal_file WHERE id = #{id}
    </select>
    <select id="selectTbAnalysePersonalFileByPid"
            resultType="com.ruoyi.common.entity.domain.talent.TbAnalysePersonalFile">
        select * from tb_analyse_personal_file where personal_id = #{personalId}

    </select>
    <select id="selectCount" resultType="com.ruoyi.talentbase.domain.vo.TbAnalyseFileStateCount">
        select
        task_id as id,
        count(*) as resumeCount,
        if (sum(case when status = 0 then 1 else 0 end) is null,0, sum(case when status = 0 then 1 else 0 end)) as
        resumeSuccessCount,
        if (sum(case when status = 2 then 1 else 0 end) is null,0, sum(case when status = 2 then 1 else 0 end)) as
        resumeFailCount
        from tb_analyse_personal_file
        where task_id = #{taskId}
    </select>

    <insert id="insertTbAnalysePersonalFile" parameterType="TbAnalysePersonalFile" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO tb_analyse_personal_file (task_id, personal_id, file_name, pinyin, file_size, file_url, upload_time,
        status, create_by, create_time)
        VALUES (#{taskId}, #{personalId}, #{fileName}, #{pinyin}, #{fileSize}, #{fileUrl}, #{uploadTime}, #{status},
        #{createBy}, #{createTime})
    </insert>

    <update id="updateTbAnalysePersonalFile" parameterType="TbAnalysePersonalFile">
        UPDATE tb_analyse_personal_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="personalId!=null">personal_id = #{personalId},</if>
            <if test="fileName!=null">file_name = #{fileName},</if>
            <if test="pinyin!=null">pinyin = #{pinyin},</if>
            <if test="fileUrl!=null">file_url = #{fileUrl},</if>
            <if test="fileSize!=null">file_size = #{fileSize},</if>
            <if test="status!=null">status = #{status},</if>
            <if test="updateBy!=null">update_by = #{updateBy},</if>
            <if test="updateTime!=null">update_time = #{updateTime},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteTbAnalysePersonalFileById" parameterType="Long">
        DELETE FROM tb_analyse_personal_file WHERE id = #{id}
    </delete>

    <delete id="deleteTbAnalysePersonalFileByIds" parameterType="Long[]">
        DELETE FROM tb_analyse_personal_file WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">#{id}</foreach>
    </delete>

    <update id="batchUpdateStatus">
        UPDATE tb_analyse_personal_file
        SET status = #{status}, update_time = NOW()
        WHERE id IN
        <foreach collection="fileIds" item="id" open="(" separator="," close=")">#{id}</foreach>
    </update>
</mapper> 