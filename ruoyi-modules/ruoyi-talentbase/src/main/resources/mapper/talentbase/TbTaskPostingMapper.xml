<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbTaskPostingMapper">

    <resultMap type="TbTaskPosting" id="TbTaskPostingResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="baseId" column="base_id"/>
        <result property="taskName" column="task_name"/>
        <result property="channel" column="channel"/>
        <result property="queryModules" column="query_modules"/>
        <result property="positionType" column="position_type"/>
        <result property="positionName" column="position_name"/>
        <result property="screeningCount" column="screening_count"/>
        <result property="finishCount" column="finish_count"/>
        <result property="minimumEducation" column="minimum_education"/>
        <result property="experienceLowerBound" column="experience_lower_bound"/>
        <result property="experienceUpperBound" column="experience_upper_bound"/>
        <result property="jobHoppingYears" column="job_hopping_years"/>
        <result property="jobHoppingCount" column="job_hopping_count"/>
        <result property="salaryRangeLowerBound" column="salary_range_lower_bound"/>
        <result property="salaryRangeUpperBound" column="salary_range_upper_bound"/>
        <result property="screeningConditions" column="screening_conditions"/>
        <result property="status" column="status"/>
        <result property="educationWeight" column="education_weight"/>
        <result property="workExperienceWeight" column="work_experience_weight"/>
        <result property="jobHoppingRateWeight" column="job_hopping_rate_weight"/>
        <result property="salaryRangeWeight" column="salary_range_weight"/>
        <result property="failureReason" column="failure_reason"/>
        <result property="passScore" column="pass_score"/>
        <result property="startTime" column="start_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTbTaskPostingVo">
        select id,
        user_id,
        base_id,
        task_name,
        channel,
        query_modules,
        position_type,
        position_name,
        screening_count,
        finish_count,
        minimum_education,
        experience_lower_bound,
        experience_upper_bound,
        job_hopping_years,
        job_hopping_count,
        salary_range_lower_bound,
        salary_range_upper_bound,
        screening_conditions,
        status,
        education_weight,
        work_experience_weight,
        job_hopping_rate_weight,
        salary_range_weight,
        failure_reason,
        pass_score,
        start_time,
        del_flag,
        create_by,
        create_time,
        update_by,
        update_time
        from tb_task_posting
    </sql>

    <select id="selectTbTaskPostingList" parameterType="TbTaskPosting" resultMap="TbTaskPostingResult">
        <include refid="selectTbTaskPostingVo"/>
        <where>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="baseId != null">and base_id = #{baseId}</if>
            <if test="taskName != null  and taskName != ''">and task_name like concat('%', #{taskName}, '%')</if>
            <if test="channel != null ">and channel = #{channel}</if>
            <if test="positionType != null  and positionType != ''">and position_type = #{positionType}</if>
            <if test="positionName != null  and positionName != ''">and position_name like concat('%', #{positionName},
                '%')
            </if>
            <if test="status != null ">and status = #{status}</if>
            <if test="queryStartTime != null">and create_time &gt;= #{queryStartTime}</if>
            <if test="queryEndTime != null">and create_time &lt;= #{queryEndTime}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectTbTaskPostingById" parameterType="Long" resultMap="TbTaskPostingResult">
        <include refid="selectTbTaskPostingVo"/>
        where id = #{id}
    </select>
    <select id="selectTbTaskPostingCount" parameterType="TbTaskPosting" resultType="java.lang.Integer">
        select count(1) from tb_task_posting
        <where>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="baseId != null">and base_id = #{baseId}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="delFlag != null">and del_flag = #{delFlag}</if>
        </where>
    </select>
    <select id="selectTbTaskPostingByBaseId" resultMap="TbTaskPostingResult">
        select * from tb_task_posting
        <where>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="baseId != null">and base_id = #{baseId}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="delFlag != null">and del_flag = #{delFlag}</if>
        </where>
        order by create_time desc limit 1
    </select>

    <insert id="insertTbTaskPosting" parameterType="TbTaskPosting" useGeneratedKeys="true" keyProperty="id">
        insert into tb_task_posting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="baseId != null">base_id,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="channel != null">channel,</if>
            <if test="queryModules != null">query_modules,</if>
            <if test="positionType != null">position_type,</if>
            <if test="positionName != null">position_name,</if>
            <if test="screeningCount != null">screening_count,</if>
            <if test="finishCount != null">finish_count,</if>
            <if test="screeningConditions != null">screening_conditions,</if>
            <if test="status != null">status,</if>
            <if test="minimumEducation != null">minimum_education,</if>
            <if test="experienceLowerBound != null">experience_lower_bound,</if>
            <if test="experienceUpperBound != null">experience_upper_bound,</if>
            <if test="jobHoppingYears!= null">job_hopping_years,</if>
            <if test="jobHoppingCount!= null">job_hopping_count,</if>
            <if test="salaryRangeLowerBound != null">salary_range_lower_bound,</if>
            <if test="salaryRangeUpperBound != null">salary_range_upper_bound,</if>
            <if test="educationWeight != null">education_weight,</if>
            <if test="workExperienceWeight != null">work_experience_weight,</if>
            <if test="salaryRangeWeight != null">salary_range_weight,</if>
            <if test="jobHoppingRateWeight != null">job_hopping_rate_weight,</if>
            <if test="failureReason != null">failure_reason,</if>
            <if test="passScore != null">pass_score,</if>
            <if test="startTime != null">start_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="baseId != null">#{baseId},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="channel != null">#{channel},</if>
            <if test="queryModules != null">#{queryModules},</if>
            <if test="positionType != null">#{positionType},</if>
            <if test="positionName != null">#{positionName},</if>
            <if test="screeningCount != null">#{screeningCount},</if>
            <if test="finishCount != null">#{finishCount},</if>
            <if test="screeningConditions != null">#{screeningConditions},</if>
            <if test="status != null">#{status},</if>
            <if test="minimumEducation != null">#{minimumEducation},</if>
            <if test="experienceLowerBound != null">#{experienceLowerBound},</if>
            <if test="experienceUpperBound != null">#{experienceUpperBound},</if>
            <if test="jobHoppingYears != null">#{jobHoppingYears},</if>
            <if test="jobHoppingCount != null">#{jobHoppingCount},</if>
            <if test="salaryRangeLowerBound != null">#{salaryRangeLowerBound},</if>
            <if test="salaryRangeUpperBound != null">#{salaryRangeUpperBound},</if>
            <if test="educationWeight != null">#{educationWeight},</if>
            <if test="workExperienceWeight != null">#{workExperienceWeight},</if>
            <if test="salaryRangeWeight != null">#{salaryRangeWeight},</if>
            <if test="jobHoppingRateWeight != null">#{jobHoppingRateWeight},</if>
            <if test="failureReason != null">#{failureReason},</if>
            <if test="passScore != null">#{passScore},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTbTaskPosting" parameterType="TbTaskPosting">
        update tb_task_posting
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="baseId != null">base_id = #{baseId},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="channel != null">channel = #{channel},</if>
            <if test="queryModules != null">query_modules = #{queryModules},</if>
            <if test="positionType != null">position_type = #{positionType},</if>
            <if test="positionName != null">position_name = #{positionName},</if>
            <if test="screeningCount != null">screening_count = #{screeningCount},</if>
            <if test="finishCount != null">finish_count = #{finishCount},</if>
            <if test="status != null">status = #{status},</if>
            screening_conditions = #{screeningConditions},
            minimum_education = #{minimumEducation},
            experience_lower_bound = #{experienceLowerBound},
            experience_upper_bound = #{experienceUpperBound},
            job_hopping_years = #{jobHoppingYears},
            job_hopping_count = #{jobHoppingCount},
            salary_range_lower_bound = #{salaryRangeLowerBound},
            salary_range_upper_bound = #{salaryRangeUpperBound},
            <if test="educationWeight != null">education_weight = #{educationWeight},</if>
            <if test="workExperienceWeight != null">work_experience_weight = #{workExperienceWeight},</if>
            <if test="salaryRangeWeight != null">salary_range_weight = #{salaryRangeWeight},</if>
            <if test="jobHoppingRateWeight != null">job_hopping_rate_weight = #{jobHoppingRateWeight},</if>
            <if test="failureReason != null">failure_reason = #{failureReason},</if>
            <if test="passScore != null">pass_score = #{passScore},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateTbTaskPostingStatus">
        update tb_task_posting
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="baseId != null">base_id = #{baseId},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="channel != null">channel = #{channel},</if>
            <if test="queryModules != null">query_modules = #{queryModules},</if>
            <if test="positionType != null">position_type = #{positionType},</if>
            <if test="positionName != null">position_name = #{positionName},</if>
            <if test="screeningCount != null">screening_count = #{screeningCount},</if>
            <if test="finishCount != null">finish_count = #{finishCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="failureReason != null">failure_reason = #{failureReason},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbTaskPostingById" parameterType="Long">
        delete from tb_task_posting where id = #{id}
    </delete>

    <delete id="deleteTbTaskPostingByIds" parameterType="String">
        delete from tb_task_posting where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>