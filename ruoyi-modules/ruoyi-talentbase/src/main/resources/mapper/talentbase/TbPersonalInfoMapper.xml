<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbPersonalInfoMapper">

    <resultMap type="TbPersonalInfo" id="TbPersonalInfoResult">
        <result property="id" column="id"/>
        <result property="taskPersonalId" column="task_personal_id"/>
        <result property="personalIdSource" column="personal_id_source"/>
        <result property="userName" column="user_name"/>
        <result property="sex" column="sex"/>
        <result property="age" column="age"/>
        <result property="baseId" column="base_id"/>
        <result property="pinyin" column="pinyin"/>
        <result property="attentionFlag" column="attention_flag"/>
        <result property="suitableFlag" column="suitable_flag"/>
        <result property="readFlag" column="read_flag"/>
        <result property="taskId" column="task_id"/>
        <result property="idCard" column="id_card"/>
        <result property="ethnicity" column="ethnicity"/>
        <result property="marriageStatus" column="marriage_status"/>
        <result property="education" column="education"/>
        <result property="position" column="position"/>
        <result property="phone" column="phone"/>
        <result property="skills" column="skills"/>
        <result property="certificate" column="certificate"/>
        <result property="schoolName" column="school_name"/>
        <result property="major" column="major"/>
        <result property="email" column="email"/>
        <result property="avatar" column="avatar"/>
        <result property="currentAddress" column="current_address"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="yearsOfExperience" column="years_of_experience"/>
        <result property="employmentStatus" column="employment_status"/>
        <result property="oldWorkStatus" column="old_work_status"/>
        <result property="introduction" column="introduction"/>
        <result property="foreignProficiency" column="foreign_proficiency"/>
        <result property="professionalLevel" column="professional_level"/>
        <result property="jobIntent" column="job_intent"/>
        <result property="salaryExpectation" column="salary_expectation"/>
        <result property="recruitmentChannel" column="recruitment_channel"/>
        <result property="minimumEducationScore" column="minimum_education_score"/>
        <result property="workExperienceScore" column="work_experience_score"/>
        <result property="jobHoppingRateScore" column="job_hopping_rate_score"/>
        <result property="salaryRangeScore" column="salary_range_score"/>
        <result property="totalScore" column="total_score"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="storageDisplay" column="storage_display"/>
        <result property="filedFlag" column="filed_flag"/>
    </resultMap>

    <sql id="selectTbPersonalInfoListVo">
        SELECT tpi.*,
        twe.company_name as companyNames
        FROM tb_personal_info tpi
        LEFT JOIN (select personal_id, GROUP_CONCAT(company_name) as company_name
        from tb_work_experience
        where company_name is not null and company_name != ''
        group by personal_id) as twe ON tpi.id = twe.personal_id
    </sql>

    <select id="selectTbPersonalInfoList" parameterType="TbPersonalInfo" resultMap="TbPersonalInfoResult">
        <include refid="selectTbPersonalInfoListVo"/>
        <where>
            <if test="storageDisplay!= null">and tpi.storage_display = #{storageDisplay}</if>
            <if test="taskPersonalId != null ">and tpi.task_personal_id = #{taskPersonalId}</if>
            <if test="personalIdSource != null ">and tpi.personal_id_source = #{personalIdSource}</if>
            <if test="userName != null  and userName != ''">and tpi.user_name like concat('%', #{userName}, '%')</if>
            <if test="sex != null  and sex != ''">and tpi.sex = #{sex}</if>
            <if test="education != null  and education != ''">and tpi.education = #{education}</if>
            <if test="age != null ">and tpi.age = #{age}</if>
            <if test="position != null  and position != ''">and tpi.position like concat('%', #{position}, '%')</if>
            <if test="jobIntent != null  and jobIntent != ''">and tpi.job_intent like concat('%', #{jobIntent}, '%')
            </if>
            <if test="employmentStatus != null  and employmentStatus != ''">and tpi.employment_status =
                #{employmentStatus}
            </if>
            <if test="delFlag != null">and tpi.del_flag = #{delFlag}</if>
            <if test="startTime != null">and tpi.create_time &gt;= #{startTime}</if>
            <if test="endTime != null">and tpi.create_time &lt;= #{endTime}</if>
            <if test="companyNames!= null and companyNames!= ''">and twe.company_name like concat('%', #{companyNames},
                '%')
            </if>
            <if test="workExpStart!= null">and tpi.years_of_experience &gt; #{workExpStart}</if>
            <if test="workExpEnd!= null">and tpi.years_of_experience &lt;= #{workExpEnd}</if>
        </where>
        order by tpi.create_time desc
    </select>
    <select id="selectTbPersonalInfoVoList" resultType="com.ruoyi.talentbase.domain.vo.TbPersonalInfoVo">
        SELECT id,
        task_personal_id,
        task_id,
        base_id,
        pinyin,
        attention_flag,
        suitable_flag,
        read_flag,
        user_name,
        sex,
        avatar,
        age,
        filed_flag,
        storage_display,
        years_of_experience,
        total_score,
        education,
        salary_expectation,
        recruitment_channel,
        job_intent,
        employment_status,
        personal_id_source
        FROM tb_personal_info
        <where>
            <if test="taskId != null ">and task_id = #{taskId}</if>
            <if test="baseId != null ">and base_id = #{baseId}</if>
            <if test="attentionFlag != null ">and attention_flag = #{attentionFlag}</if>
            <if test="suitableFlag != null ">and suitable_flag = #{suitableFlag}</if>
            <if test="readFlag != null ">and read_flag = #{readFlag}</if>
            <if test="userName != null  and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="sex != null  and sex != ''">and sex = #{sex}</if>
            <if test="education != null">and education = #{education}</if>
            <if test="storageDisplay!= null">and storage_display = #{storageDisplay}</if>
            <if test="filedFlag != null ">and filed_flag = #{filedFlag}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectVOList" resultType="com.ruoyi.talentbase.domain.vo.ResumeListVO">
        SELECT
        tpi.id,
        tpi.task_personal_id,
        tpi.task_id,
        tpi.base_id,
        tpi.pinyin,
        tpi.attention_flag,
        tpi.suitable_flag,
        tpi.read_flag,
        tpi.user_name,
        tpi.sex,
        tpi.age,
        tpi.avatar,
        tpi.filed_flag,
        tpi.years_of_experience,
        tpi.total_score,
        tpi.old_work_status as workStatus,
        tpi.education,
        tpi.salary_expectation,
        tpi.recruitment_channel,
        tpi.job_intent,
        tpi.employment_status,
        tpi.personal_id_source,
        taf.id as fileId,
        taf.task_id as taskId,
        taf.file_name as fileName,
        taf.pinyin as filePinyin,
        taf.file_size as fileSize,
        taf.file_url as fileUrl,
        if(taf.status is null, 0, taf.status) as parseStatus
        FROM tb_personal_info tpi left join tb_analyse_personal_file taf on tpi.id = taf.personal_id
        <where>
            <if test="taskId != null ">and tpi.task_id = #{taskId}</if>
            <if test="baseId != null ">and tpi.base_id = #{baseId}</if>
            <if test="personalIdSource != null ">and tpi.personal_id_source = #{personalIdSource}</if>
            <if test="attentionFlag != null ">and tpi.attention_flag = #{attentionFlag}</if>
            <if test="suitableFlag != null ">and tpi.suitable_flag = #{suitableFlag}</if>
            <if test="readFlag != null ">and tpi.read_flag = #{readFlag}</if>
            <if test="userName != null  and userName != ''">and tpi.user_name like concat('%', #{userName}, '%')</if>
            <if test="sex != null  and sex != ''">and tpi.sex = #{sex}</if>
            <if test="education != null">and tpi.education = #{education}</if>
            <if test="storageDisplay!= null">and tpi.storage_display = #{storageDisplay}</if>
            <if test="filedFlag != null ">and tpi.filed_flag = #{filedFlag}</if>
            <if test="parseStatus != null and parseStatus != 1">and taf.status = #{parseStatus}</if>
            <if test="parseStatus != null and parseStatus == 1">and (taf.status = #{parseStatus} or taf.status = 3 )</if>
            <if test="ageLowerBound != null">and tpi.age &gt;= #{ageLowerBound}</if>
            <if test="ageUpperBound != null">and tpi.age &lt;= #{ageUpperBound}</if>
            <if test="workExperienceLowerBound!= null">and tpi.years_of_experience &gt;= #{workExperienceLowerBound}
            </if>
            <if test="workExperienceUpperBound!= null">and tpi.years_of_experience &lt; #{workExperienceUpperBound}</if>
        </where>
        <if test="orderField != null">
            <choose>
                <when test="orderField == 0">order by tpi.total_score</when>
                <when test="orderField == 1">order by tpi.years_of_experience</when>
                <when test="orderField == 2">order by tpi.age</when>
                <when test="orderField == 3">order by tpi.pinyin</when>
                <when test="orderField == 4">order by tpi.create_time</when>
            </choose>
            <choose>
                <when test="sort == 0">asc</when>
                <otherwise>desc</otherwise>
            </choose>
        </if>
    </select>

    <select id="selectTopicCount" resultType="com.ruoyi.talentbase.domain.vo.TbPersonalTopicCount">
        SELECT
        count(distinct if(if(taf.status is null, 0, taf.status) = 0, tpi.id, null)) as allCount,
        sum(if(tpi.attention_flag = 0 and if(taf.status is null, 0, taf.status) = 0 , 1, 0)) as attentionCount,
        sum(if(tpi.attention_flag = 1 and if(taf.status is null, 0, taf.status) = 0 , 1, 0)) as unattentionCount,
        sum(if(tpi.suitable_flag = 1 and if(taf.status is null, 0, taf.status) = 0 , 1, 0)) as unsuitableCount
        FROM tb_personal_info tpi left join tb_analyse_personal_file taf on tpi.id = taf.personal_id
        <where>
            <if test="taskId != null ">and tpi.task_id = #{taskId}</if>
            <if test="personalIdSource != null ">and tpi.personal_id_source = #{personalIdSource}</if>
            <if test="baseId != null ">and tpi.base_id = #{baseId}</if>
            <if test="attentionFlag != null ">and tpi.attention_flag = #{attentionFlag}</if>
            <if test="suitableFlag != null ">and tpi.suitable_flag = #{suitableFlag}</if>
            <if test="readFlag != null ">and tpi.read_flag = #{readFlag}</if>
            <if test="userName != null  and userName != ''">and tpi.user_name like concat('%', #{userName}, '%')</if>
            <if test="sex != null  and sex != ''">and tpi.sex = #{sex}</if>
            <if test="education != null">and tpi.education = #{education}</if>
            <if test="storageDisplay!= null">and tpi.storage_display = #{storageDisplay}</if>
            <if test="filedFlag != null ">and tpi.filed_flag = #{filedFlag}</if>
            <if test="parseStatus != null">and taf.status = #{parseStatus}</if>
            <if test="workExperienceLowerBound!= null">and tpi.years_of_experience &gt;= #{workExperienceLowerBound}</if>
            <if test="workExperienceUpperBound!= null">and tpi.years_of_experience &lt; #{workExperienceUpperBound}</if>
        </where>
    </select>


    <delete id="deleteTbPersonalInfoById" parameterType="Long">
        update tb_personal_info set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteTbPersonalInfoByIds" parameterType="String">
        update tb_personal_info set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>