<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.talentbase.mapper.TbAnalyseTaskMapper">

    <!-- 结果映射 -->
    <resultMap id="TbAnalyseTaskResult" type="TbAnalyseTask">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="baseId" column="base_id"/>
        <result property="taskName" column="task_name"/>
        <result property="positionName" column="position_name"/>
        <result property="positionRequirement" column="position_requirement"/>
        <result property="passScore" column="pass_score"/>
        <result property="educationWeight" column="education_weight"/>
        <result property="workExperienceWeight" column="work_experience_weight"/>
        <result property="jobHoppingRateWeight" column="job_hopping_rate_weight"/>
        <result property="salaryRangeWeight" column="salary_range_weight"/>
        <result property="status" column="status"/>
        <result property="resumeCount" column="resume_count"/>
        <result property="resumeSuccessCount" column="resume_success_count"/>
        <result property="resumeFailCount" column="resume_fail_count"/>
        <result property="resumeExcellentCount" column="resume_excellent_count"/>
        <result property="resumeGoodCount" column="resume_good_count"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectTbAnalyseTaskVo">
        SELECT id,
        user_id,
        base_id,
        task_name,
        position_name,
        position_requirement,
        pass_score,
        education_weight,
        work_experience_weight,
        job_hopping_rate_weight,
        salary_range_weight,
        status,
        resume_count,
        resume_success_count,
        resume_fail_count,
        resume_excellent_count,
        resume_good_count,
        create_by,
        create_time,
        update_by,
        update_time,
        remark
        FROM tb_analyse_task
    </sql>

    <!-- 列表查询 -->
    <select id="selectTbAnalyseTaskList" parameterType="TbAnalyseTask" resultMap="TbAnalyseTaskResult">
        <include refid="selectTbAnalyseTaskVo"/>
        <where>
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="baseId != null">AND base_id = #{baseId}</if>
            <if test="taskName != null and taskName != ''">AND task_name LIKE concat('%', #{taskName}, '%')</if>
            <if test="status != null">AND status = #{status}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询 -->
    <select id="selectTbAnalyseTaskById" parameterType="Long" resultMap="TbAnalyseTaskResult">
        <include refid="selectTbAnalyseTaskVo"/>
        WHERE id = #{id}
    </select>

    <!-- 新增 -->
    <insert id="insertTbAnalyseTask" parameterType="TbAnalyseTask" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_analyse_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="baseId != null">base_id,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="positionName != null and positionName != ''">position_name,</if>
            <if test="positionRequirement != null">position_requirement,</if>
            <if test="passScore != null">pass_score,</if>
            <if test="educationWeight != null">education_weight,</if>
            <if test="workExperienceWeight != null">work_experience_weight,</if>
            <if test="jobHoppingRateWeight != null">job_hopping_rate_weight,</if>
            <if test="salaryRangeWeight != null">salary_range_weight,</if>
            <if test="status != null">status,</if>
            <if test="resumeCount != null">resume_count,</if>
            <if test="resumeSuccessCount != null">resume_success_count,</if>
            <if test="resumeFailCount != null">resume_fail_count,</if>
            <if test="resumeExcellentCount != null">resume_excellent_count,</if>
            <if test="resumeGoodCount != null">resume_good_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="baseId != null">#{baseId},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="positionName != null and positionName != ''">#{positionName},</if>
            <if test="positionRequirement != null">#{positionRequirement},</if>
            <if test="passScore != null">#{passScore},</if>
            <if test="educationWeight != null">#{educationWeight},</if>
            <if test="workExperienceWeight != null">#{workExperienceWeight},</if>
            <if test="jobHoppingRateWeight != null">#{jobHoppingRateWeight},</if>
            <if test="salaryRangeWeight != null">#{salaryRangeWeight},</if>
            <if test="status != null">#{status},</if>
            <if test="resumeCount != null">#{resumeCount},</if>
            <if test="resumeSuccessCount != null">#{resumeSuccessCount},</if>
            <if test="resumeFailCount != null">#{resumeFailCount},</if>
            <if test="resumeExcellentCount != null">#{resumeExcellentCount},</if>
            <if test="resumeGoodCount != null">#{resumeGoodCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <!-- 更新 -->
    <update id="updateTbAnalyseTask" parameterType="TbAnalyseTask">
        UPDATE tb_analyse_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="baseId != null">base_id = #{baseId},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="positionName != null and positionName != ''">position_name = #{positionName},</if>
            <if test="positionRequirement != null">position_requirement = #{positionRequirement},</if>
            <if test="passScore != null">pass_score = #{passScore},</if>
            <if test="educationWeight != null">education_weight = #{educationWeight},</if>
            <if test="workExperienceWeight != null">work_experience_weight = #{workExperienceWeight},</if>
            <if test="jobHoppingRateWeight != null">job_hopping_rate_weight = #{jobHoppingRateWeight},</if>
            <if test="salaryRangeWeight != null">salary_range_weight = #{salaryRangeWeight},</if>
            <if test="status != null">status = #{status},</if>
            <if test="resumeCount != null">resume_count = #{resumeCount},</if>
            <if test="resumeSuccessCount != null">resume_success_count = #{resumeSuccessCount},</if>
            <if test="resumeFailCount != null">resume_fail_count = #{resumeFailCount},</if>
            <if test="resumeExcellentCount != null">resume_excellent_count = #{resumeExcellentCount},</if>
            <if test="resumeGoodCount != null">resume_good_count = #{resumeGoodCount},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <!-- 删除单条 -->
    <delete id="deleteTbAnalyseTaskById" parameterType="Long">
        DELETE FROM tb_analyse_task WHERE id = #{id}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteTbAnalyseTaskByIds" parameterType="Long[]">
        DELETE FROM tb_analyse_task WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 列表查询（DTO） -->
    <select id="selectTbAnalyseTaskListByQuery" resultMap="TbAnalyseTaskResult">
        <include refid="selectTbAnalyseTaskVo"/>
        <where>
            <if test="query.taskName != null and query.taskName != ''">AND task_name like concat('%', #{query.taskName},
                '%')
            </if>
            <if test="query.positionName != null and query.positionName != ''">AND position_name like concat('%',
                #{query.positionName}, '%')
            </if>
            <if test="query.createByStart != null">AND create_time &gt;= #{query.createByStart}</if>
            <if test="query.createByEnd != null">AND create_time &lt;= #{query.createByEnd}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 查询简历列表（统一接口，先查询文件表，再关联简历信息） -->
    <select id="selectResumeList" resultType="com.ruoyi.talentbase.domain.vo.ResumeListVO">
        SELECT
        f.id as fileId,
        p.id as resumeId,
        f.task_id as taskId,
        f.file_name as fileName,
        f.pinyin as filePinyin,
        f.file_size as fileSize,
        CASE
        WHEN f.file_size &lt; 1024 THEN CONCAT(f.file_size, ' B')
        WHEN f.file_size &lt; 1024 * 1024 THEN CONCAT(ROUND(f.file_size / 1024.0, 2), ' KB')
        WHEN f.file_size &lt; 1024 * 1024 * 1024 THEN CONCAT(ROUND(f.file_size / (1024.0 * 1024.0), 2), ' MB')
        ELSE CONCAT(ROUND(f.file_size / (1024.0 * 1024.0 * 1024.0), 2), ' GB')
        END as fileSizeDesc,
        f.file_url as fileUrl,
        f.personal_id as personalId,
        p.user_name as userName,
        p.pinyin as pinyin,
        p.sex,
        CASE
        WHEN p.sex = '0' THEN '男'
        WHEN p.sex = '1' THEN '女'
        ELSE '未知'
        END as sexDesc,
        p.age,
        p.education,
        p.years_of_experience as yearsOfExperience,
        p.work_status as workStatus,
        p.job_intent as jobIntent,
        p.salary_expectation as salaryExpectation,
        p.total_score as totalScore,
        f.status as parseStatus,
        CASE
        WHEN f.status = 0 THEN '成功'
        WHEN f.status = 1 THEN '解析中'
        WHEN f.status = 2 THEN '失败'
        ELSE '未知'
        END as parseStatusDesc,
        p.filed_flag as filedFlag,
        CASE
        WHEN p.filed_flag = 0 THEN '未建档'
        WHEN p.filed_flag = 1 THEN '已建档'
        ELSE '未知'
        END as filedFlagDesc,
        p.talent_pool_status as talentPoolStatus,
        CASE
        WHEN p.talent_pool_status = 0 THEN '未入库'
        WHEN p.talent_pool_status = 1 THEN '已入库'
        ELSE '未知'
        END as talentPoolStatusDesc,
        DATE_FORMAT(f.upload_time, '%Y-%m-%d %H:%i:%s') as uploadTime,
        DATE_FORMAT(f.create_time, '%Y-%m-%d %H:%i:%s') as createTime
        FROM tb_analyse_personal_file f
        LEFT JOIN tb_analyse_personal_info p ON f.personal_id = p.id
        <where>
            <if test="query.taskId != null">AND f.task_id = #{query.taskId}</if>
            <if test="query.nameOrFileName != null and query.nameOrFileName != ''">
                AND (f.file_name like concat('%', #{query.nameOrFileName}, '%')
                OR p.user_name like concat('%', #{query.nameOrFileName}, '%'))
            </if>
            <if test="query.sex != null and query.sex != ''">AND p.sex = #{query.sex}</if>
            <if test="query.ageMin != null">AND p.age &gt;= #{query.ageMin}</if>
            <if test="query.ageMax != null">AND p.age &lt;= #{query.ageMax}</if>
            <if test="query.education != null and query.education != ''">AND p.education = #{query.education}</if>
            <if test="query.experienceCode != null and query.experienceCode != ''">
                <choose>
                    <when test="query.experienceCode == 'lt_1'">
                        AND p.years_of_experience &lt; 1
                    </when>
                    <when test="query.experienceCode == '1_3'">
                        AND p.years_of_experience &gt;= 1 AND p.years_of_experience &lt; 3
                    </when>
                    <when test="query.experienceCode == '3_5'">
                        AND p.years_of_experience &gt;= 3 AND p.years_of_experience &lt; 5
                    </when>
                    <when test="query.experienceCode == '5_10'">
                        AND p.years_of_experience &gt;= 5 AND p.years_of_experience &lt;= 10
                    </when>
                    <when test="query.experienceCode == 'gt_10'">
                        AND p.years_of_experience &gt; 10
                    </when>
                </choose>
            </if>
            <if test="query.jobIntent != null and query.jobIntent != ''">
                AND p.job_intent like concat('%', #{query.jobIntent}, '%')
            </if>
            <if test="query.parseStatus != null">AND f.status = #{query.parseStatus}</if>
        </where>
        <choose>
            <when test="query.sortField != null and query.sortField != '' and query.sortOrder != null and query.sortOrder != ''">
                <choose>
                    <when test="query.sortField == 'userName'">
                        ORDER BY f.status ASC,
                        CASE WHEN f.status IN (1, 2) THEN f.pinyin ELSE COALESCE(p.pinyin, p.user_name) END
                        ${query.sortOrder},
                        f.create_time DESC
                    </when>
                    <when test="query.sortField == 'yearsOfExperience'">
                        ORDER BY f.status ASC, p.years_of_experience ${query.sortOrder}, f.create_time DESC
                    </when>
                    <when test="query.sortField == 'totalScore'">
                        ORDER BY f.status ASC, p.total_score ${query.sortOrder}, f.create_time DESC
                    </when>
                    <when test="query.sortField == 'age'">
                        ORDER BY f.status ASC, p.age ${query.sortOrder}, f.create_time DESC
                    </when>
                    <otherwise>
                        ORDER BY f.status ASC,
                        CASE WHEN f.status IN (1, 2) THEN f.pinyin ELSE f.create_time END ASC,
                        f.create_time DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY f.status ASC,
                CASE WHEN f.status IN (1, 2) THEN f.pinyin ELSE f.create_time END ASC,
                f.create_time DESC
            </otherwise>
        </choose>
    </select>
    <select id="selectTbAnalyseTaskCount" resultType="java.lang.Integer">
        select count(1) from tb_analyse_task
        <where>
            <if test="baseId != null">and base_id = #{baseId}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
    </select>
    <select id="selectTbAnalyseTaskByBaseId" resultMap="TbAnalyseTaskResult">
        select * from tb_analyse_task
        <where>
            <if test="baseId != null">and base_id = #{baseId}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
        order by create_time desc limit 1
    </select>
</mapper> 