<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbOfflineInterviewMapper">
    <resultMap id="TbOfflineInterviewResultMap" type="com.ruoyi.talentbase.domain.TbOfflineInterview">
        <id property="id" column="id"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="avatarRedDot" column="avatar_red_dot"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="education" column="education"/>
        <result property="age" column="age"/>
        <result property="jobIntention" column="job_intention"/>
        <result property="talentSource" column="talent_source"/>
        <result property="interviewRound" column="interview_round"/>
        <result property="operator" column="operator"/>
        <result property="interviewStatus" column="interview_status"/>
        <result property="interviewTime" column="interview_time"/>
        <result property="interviewEndTime" column="interview_end_time"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="offlineInterviewSource" column="offline_interview_source"/>
        <result property="offlineInterviewSourceId" column="offline_interview_source_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="companyId" column="company_id"/>
        <result property="depts" column="depts"/>
        <result property="jobDesc" column="job_desc"/>
    </resultMap>

    <select id="selectTbOfflineInterviewById" resultMap="TbOfflineInterviewResultMap">
        SELECT * FROM tb_offline_interview WHERE id = #{id}
    </select>

    <select id="selectTbOfflineInterviewList" parameterType="com.ruoyi.talentbase.domain.dto.OfflineInterviewQueryDTO"
            resultMap="TbOfflineInterviewResultMap">
        SELECT * FROM tb_offline_interview
        <where>
            <if test="name != null and name != ''">AND name LIKE CONCAT('%', #{name}, '%')</if>
            <if test="jobIntention != null and jobIntention != ''">AND job_intention LIKE CONCAT('%', #{jobIntention},
                '%')
            </if>
            <if test="interviewStatus != null">AND interview_status = #{interviewStatus}</if>
            <if test="gender != null and gender != ''">AND gender = #{gender}</if>
            <if test="education != null and education != ''">AND education = #{education}</if>
            <if test="ageStart != null">AND age &gt;= #{ageStart}</if>
            <if test="ageEnd != null">AND age &lt;= #{ageEnd}</if>
            <if test="interviewTimeStart != null and interviewTimeStart != ''">AND interview_time &gt;=
                #{interviewTimeStart}
            </if>
            <if test="interviewTimeEnd != null and interviewTimeEnd != ''">AND interview_time &lt;=
                #{interviewTimeEnd}
            </if>
            <if test="offlineInterviewSource != null">AND offline_interview_source = #{offlineInterviewSource}</if>
            <if test="offlineInterviewSourceId != null">AND offline_interview_source_id = #{offlineInterviewSourceId}
            </if>
            <if test="deptId != null">AND dept_id = #{deptId}</if>
            <if test="companyId != null">AND company_id = #{companyId}</if>
            <if test="depts != null and depts != ''">AND depts LIKE CONCAT('%', #{depts}, '%')</if>
            <if test="jobDesc != null and jobDesc != ''">AND job_desc LIKE CONCAT('%', #{jobDesc}, '%')</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <insert id="insertTbOfflineInterview" parameterType="com.ruoyi.talentbase.domain.TbOfflineInterview"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_offline_interview
        (avatar_url, avatar_red_dot, name, gender, education, age, job_intention, talent_source, interview_round,
        operator, interview_status, interview_time, interview_end_time, offline_interview_source,
        offline_interview_source_id, dept_id, company_id, depts, job_desc, create_by, create_time, update_by, update_time, remark)
        VALUES
        (#{avatarUrl}, #{avatarRedDot}, #{name}, #{gender}, #{education}, #{age}, #{jobIntention}, #{talentSource},
        #{interviewRound}, #{operator}, #{interviewStatus}, #{interviewTime}, #{interviewEndTime},
        #{offlineInterviewSource}, #{offlineInterviewSourceId}, #{deptId}, #{companyId}, #{depts}, #{jobDesc}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime},
        #{remark})
    </insert>

    <update id="updateTbOfflineInterview" parameterType="com.ruoyi.talentbase.domain.TbOfflineInterview">
        UPDATE tb_offline_interview
        <set>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="avatarRedDot != null">avatar_red_dot = #{avatarRedDot},</if>
            <if test="name != null">name = #{name},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="education != null">education = #{education},</if>
            <if test="age != null">age = #{age},</if>
            <if test="jobIntention != null">job_intention = #{jobIntention},</if>
            <if test="talentSource != null">talent_source = #{talentSource},</if>
            <if test="interviewRound != null">interview_round = #{interviewRound},</if>
            <if test="operator != null">operator = #{operator},</if>
            <if test="interviewStatus != null">interview_status = #{interviewStatus},</if>
            <if test="interviewTime != null">interview_time = #{interviewTime},</if>
            <if test="interviewEndTime != null">interview_end_time = #{interviewEndTime},</if>
            <if test="offlineInterviewSource != null">offline_interview_source = #{offlineInterviewSource},</if>
            <if test="offlineInterviewSourceId != null">offline_interview_source_id = #{offlineInterviewSourceId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="depts != null">depts = #{depts},</if>
            <if test="jobDesc != null">job_desc = #{jobDesc},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteTbOfflineInterviewById" parameterType="Long">
        DELETE FROM tb_offline_interview WHERE id = #{id}
    </delete>

    <delete id="deleteTbOfflineInterviewByIds" parameterType="Long">
        DELETE FROM tb_offline_interview WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByPInfoId">
        DELETE FROM tb_offline_interview WHERE offline_interview_source = #{code} AND offline_interview_source_id =
        #{infoId}
    </delete>

    <!-- 批量更新为已过期 -->
    <update id="expireOfflineInterviewsBySource" parameterType="TbOfflineInterview">
        UPDATE tb_offline_interview
        SET interview_status = #{interviewStatus},
        update_by = #{updateBy},
        update_time = #{updateTime}
        WHERE offline_interview_source = #{offlineInterviewSource}
        AND offline_interview_source_id = #{offlineInterviewSourceId}
        AND interview_status = 0
    </update>

    <!-- 统计指定人才的正在进行和已完成的线下面试次数 -->
    <select id="countActiveAndCompletedInterviews" resultType="int">
        SELECT COUNT(*)
        FROM tb_offline_interview
        WHERE offline_interview_source_id = #{sourceId}
        AND offline_interview_source = #{sourceType}
        AND interview_status IN (1, 2)
    </select>
    <select id="selectInterviewListByPerId" resultType="com.ruoyi.talentbase.domain.TbOfflineInterview">
        SELECT * FROM tb_offline_interview
        WHERE offline_interview_source_id = #{infoId} and offline_interview_source = #{talentCode}
    </select>

    <!-- 查询当前用户正在进行的线下面试 -->
    <select id="selectInProgressInterviewsByOperator" resultMap="TbOfflineInterviewResultMap">
        SELECT * FROM tb_offline_interview
        WHERE operator = #{operator} AND update_by = #{updateBy}
        AND interview_status = 1
        AND id != #{excludeId}
        ORDER BY interview_time DESC
    </select>
</mapper> 