<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.talentbase.mapper.TbpPositionBaseMapper">

    <resultMap type="TbpPositionBase" id="TbpPositionBaseResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="openFlag" column="open_flag"/>
        <result property="description" column="description"/>
        <result property="sort" column="sort"/>
        <result property="sortTime" column="sort_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTbpPositionBaseVo">
        select id, name, open_flag, description,
        sort, sort_time, create_by, create_time, update_by, update_time from tbp_position_base
    </sql>

    <select id="selectTbpPositionBaseList" parameterType="TbpPositionBase" resultMap="TbpPositionBaseResult">
        <include refid="selectTbpPositionBaseVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="openFlag != null ">and open_flag = #{openFlag}</if>
            <if test="description != null  and description != ''">and description like concat('%', #{description},
                '%')
            </if>
        </where>
        order by open_flag asc, sort asc, sort_time desc
    </select>

    <select id="selectTbpPositionBaseById" parameterType="Long" resultMap="TbpPositionBaseResult">
        <include refid="selectTbpPositionBaseVo"/>
        where id = #{id}
    </select>
    <select id="getMaxSortCode" resultType="java.lang.Integer">
        select max(sort) from tbp_position_base
        <where>
            <if test="openFlag != null">open_flag = #{openFlag}</if>
        </where>
    </select>
    <select id="getPositionBaseCount" resultType="com.ruoyi.talentbase.domain.vo.TbpPositionBaseCount">
        select
            count(*) as total,
            sum(case when open_flag = 0 then 1 else 0 end) as openCount,
            sum(case when open_flag = 1 then 1 else 0 end) as closeCount
        from tbp_position_base
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
        </where>
    </select>

    <insert id="insertTbpPositionBase" parameterType="com.ruoyi.talentbase.domain.TbpPositionBase"
            useGeneratedKeys="true" keyProperty="id">
        insert into tbp_position_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="openFlag != null">open_flag,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="sort != null">sort,</if>
            <if test="sortTime != null">sort_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="openFlag != null">#{openFlag},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="sort != null">#{sort},</if>
            <if test="sortTime != null">#{sortTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTbpPositionBase" parameterType="TbpPositionBase">
        update tbp_position_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="openFlag != null">open_flag = #{openFlag},</if>
            description = #{description},
            <if test="sort != null">sort = #{sort},</if>
            <if test="sortTime != null">sort_time = #{sortTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateSort">
        update tbp_position_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="reduce == 0">sort = sort-1,</if>
            <if test="reduce == 1">sort = sort+1,</if>
        </trim>
        <where>
            <if test="reduce == 0">
                id != #{id} and sort &lt;= #{targetSeqNo} and sort &gt; #{oldSeqNo}
            </if>
            <if test="reduce == 1">
                id != #{id} and sort &gt;= #{targetSeqNo} and sort &lt; #{oldSeqNo}
            </if>
            and open_flag = #{openFlag}
        </where>
    </update>

    <delete id="deleteTbpPositionBaseById" parameterType="Long">
        delete from tbp_position_base where id = #{id}
    </delete>

    <delete id="deleteTbpPositionBaseByIds" parameterType="Long">
        delete from tbp_position_base where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>