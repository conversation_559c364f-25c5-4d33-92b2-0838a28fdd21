package com.ruoyi.common.security.service;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.ip.AddressUtils;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.entity.domain.system.LoginUser;
import com.ruoyi.common.redis.service.RedisCache;
import com.ruoyi.common.security.utils.UserRedisUtils;
import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService {
    private static final Logger log = LoggerFactory.getLogger(TokenService.class);

    // 令牌自定义标识
    @Value("${token.header}")
    private String header;

    // 令牌秘钥
    @Value("${token.secret}")
    private String secret;

    // 令牌有效期（默认30分钟）
    @Value("${token.expireTime}")
    private int expireTime;

    protected static final long MILLIS_SECOND = 1000L;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    protected static final long MILLIS_DAY = 24 * 60 * MILLIS_MINUTE;

    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;

    @Autowired
    private RedisCache redisCache;

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request) {
        log.info("[SECURITY-DEBUG] getLoginUser(HttpServletRequest)方法被调用");
        String token = getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            try {
                log.info("[SECURITY-DEBUG] 请求Token: {}", token);
                Claims claims = parseToken(token);
                log.info("[SECURITY-DEBUG] 解析Claims: {}", claims);
                String uuid = (String) claims.get(SecurityConstants.USER_KEY);
                log.info("[SECURITY-DEBUG] 解析uuid: {}", uuid);
                String userKey = getTokenKey(uuid);
                log.info("[SECURITY-DEBUG] Redis Key: {}", userKey);
                LoginUser loginUser = redisCache.getCacheObject(userKey);
                log.info("[SECURITY-DEBUG] Redis 查找结果: {}", (loginUser != null ? "命中" : "未命中"));
                return loginUser;
            } catch (Exception e) {
                log.info("[SECURITY-DEBUG] Token解析异常: {}", e.getMessage());
            }
        } else {
            log.info("[SECURITY-DEBUG] 未获取到Token");
        }
        return null;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(String token) {
        log.info("[SECURITY-DEBUG] getLoginUser(String)方法被调用");
        if (StringUtils.isNotEmpty(token)) {
            try {
                log.info("[SECURITY-DEBUG] 请求Token: {}", token);
                Claims claims = parseToken(token);
                log.info("[SECURITY-DEBUG] 解析Claims: {}", claims);
                String uuid = (String) claims.get(SecurityConstants.USER_KEY);
                log.info("[SECURITY-DEBUG] 解析uuid: {}", uuid);
                String userKey = getTokenKey(uuid);
                log.info("[SECURITY-DEBUG] Redis Key: {}", userKey);
                LoginUser loginUser = redisCache.getCacheObject(userKey);
                log.info("[SECURITY-DEBUG] Redis 查找结果: {}", (loginUser != null ? "命中" : "未命中"));
                return loginUser;
            } catch (Exception e) {
                log.info("[SECURITY-DEBUG] Token解析异常: {}", e.getMessage());
            }
        } else {
            log.info("[SECURITY-DEBUG] 未获取到Token");
        }
        return null;
    }


    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户身份信息
     */
    public void delLoginUser(String token) {
        if (StringUtils.isNotEmpty(token) && token.contains(".")) {
            try {
                Claims claims = parseToken(token);
                String uuid = (String) claims.get(SecurityConstants.USER_KEY);
                String userKey = getTokenKey(uuid);
                redisCache.deleteObject(userKey);
            } catch (Exception e) {
                log.warn("[SECURITY-DEBUG] delLoginUser解析token异常: {}", e.getMessage());
            }
        } else {
            log.warn("[SECURITY-DEBUG] delLoginUser收到非法token: {}", token);
        }
    }

    /**
     * 创建令牌
     *
     * @param loginUser 用户信息
     * @return 令牌
     */
    public String createToken(LoginUser loginUser) {
        UserRedisUtils.removeRedisUser(loginUser.getUser().getUserId(), loginUser.getUser().getClass());
        String uuid = IdUtils.fastUUID();
        Long userId = loginUser.getUser().getUserId();
        String userName = loginUser.getUser().getUserName();
        Map<String, Object> claims = new HashMap<>();
        claims.put(SecurityConstants.USER_KEY, uuid);
        claims.put(SecurityConstants.DETAILS_USER_ID, userId);
        claims.put(SecurityConstants.DETAILS_USERNAME, userName);
        loginUser.setToggleCompanyId(null);
        loginUser.setToken(uuid);
        setUserAgent(loginUser);
        refreshToken(loginUser);
        return encryptToken(claims);
    }

    public String encryptToken(Map<String, Object> claims) {
        return createToken(claims);
    }

    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     *
     * @param loginUser
     * @return 令牌
     */
    public void verifyToken(LoginUser loginUser) {
        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
            refreshToken(loginUser);
        }
    }

    /**
     * 测试Redis连接
     */
    private void testRedisConnection() {
        try {
            System.out.println("=== REDIS CONNECTION TEST ===");
            // 测试基本连接
            redisCache.setCacheObject("test_key", "test_value", 1, TimeUnit.MINUTES);
            Object testValue = redisCache.getCacheObject("test_key");
            System.out.println("Redis connection test - Test value: " + testValue);
            
            // 测试删除
            redisCache.deleteObject("test_key");
            System.out.println("Redis connection test - Delete test key completed");
            System.out.println("=== REDIS CONNECTION TEST END ===");
        } catch (Exception e) {
            System.err.println("Redis connection test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_DAY);
        String userKey = getTokenKey(loginUser.getToken());
        redisCache.setCacheObject(userKey, loginUser, expireTime, TimeUnit.DAYS);
    }

    /**
     * 设置用户代理信息
     *
     * @param loginUser 登录信息
     */
    public void setUserAgent(LoginUser loginUser) {
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        loginUser.setIpaddr(ip);
        loginUser.setLoginLocation(AddressUtils.getRealAddressByIP(ip));
        loginUser.setBrowser(userAgent.getBrowser().getName());
        loginUser.setOs(userAgent.getOperatingSystem().getName());
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private String createToken(Map<String, Object> claims) {
        String token = Jwts.builder()
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS512, secret).compact();
        return token;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.getSubject();
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(header);
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }

    private String getTokenKey(String uuid) {
        return Constants.LOGIN_TOKEN_KEY + uuid;
    }

    public void messageKey(String userKey, String message, Integer expireTime) {
        redisCache.setCacheObject(userKey, message, expireTime, TimeUnit.MINUTES);
    }

    public void messageKeyBySecond(String userKey, String message, Integer expireTime) {
        redisCache.setCacheObject(userKey, message, expireTime, TimeUnit.SECONDS);
    }

    public Object getVale(String key) {
        return redisCache.getCacheObject(key);
    }
}
