package com.ruoyi.common.security.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * 跨域安全配置
 * 为私有化部署提供严格的跨域防护
 * 
 * <AUTHOR>
 */
@Configuration
public class CorsSecurityConfig implements WebMvcConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(CorsSecurityConfig.class);

    /**
     * 允许的跨域域名列表
     */
    @Value("${cors.allowed-origins:}")
    private String[] allowedOrigins;

    /**
     * 是否启用跨域
     */
    @Value("${cors.enabled:true}")
    private boolean corsEnabled;

    /**
     * 是否允许凭证
     */
    @Value("${cors.allow-credentials:true}")
    private boolean allowCredentials;

    /**
     * 允许的HTTP方法
     */
    @Value("${cors.allowed-methods:GET,POST,PUT,DELETE,OPTIONS}")
    private String[] allowedMethods;

    /**
     * 允许的请求头
     */
    @Value("${cors.allowed-headers:Authorization,Content-Type,X-Requested-With,X-Token}")
    private String[] allowedHeaders;

    /**
     * 暴露的响应头
     */
    @Value("${cors.exposed-headers:Content-Disposition,Content-Length,X-Total-Count}")
    private String[] exposedHeaders;

    /**
     * 预检请求缓存时间（秒）
     */
    @Value("${cors.max-age:3600}")
    private long maxAge;

    /**
     * 需要跨域支持的路径
     */
    @Value("${cors.paths:/**}")
    private String[] corsPaths;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        logger.info("=== CORS SECURITY CONFIG START ===");
        logger.info("CORS Enabled: {}", corsEnabled);
        
        if (!corsEnabled) {
            logger.info("CORS is disabled, skipping CORS mappings");
            logger.info("=== CORS SECURITY CONFIG END ===");
            return;
        }

        String[] origins = getAllowedOrigins();
        logger.info("Allowed Origins: {}", Arrays.toString(origins));
        logger.info("Allowed Methods: {}", Arrays.toString(allowedMethods));
        logger.info("Allowed Headers: {}", Arrays.toString(allowedHeaders));
        logger.info("Allow Credentials: {}", allowCredentials);
        logger.info("CORS Paths: {}", Arrays.toString(corsPaths));

        // 为每个配置的路径添加跨域支持
        for (String path : corsPaths) {
            registry.addMapping(path)
                    .allowedOrigins(origins)
                    // .allowedOriginPatterns("*") // 移除临时通配符配置
                    .allowedMethods(allowedMethods)
                    .allowedHeaders(allowedHeaders)
                    .exposedHeaders(exposedHeaders)
                    .allowCredentials(allowCredentials)
                    .maxAge(maxAge);
            
            logger.info("Added CORS mapping for path: {}", path);
        }
        
        logger.info("=== CORS SECURITY CONFIG END ===");
    }

    /**
     * 获取允许的跨域域名
     * 私有化部署时，根据环境动态配置
     */
    private String[] getAllowedOrigins() {
        // 添加调试日志
        logger.info("Raw allowed origins from config: {}", Arrays.toString(allowedOrigins));
        
        // 检查是否为空或包含空字符串
        if (allowedOrigins == null || allowedOrigins.length == 0 || 
            (allowedOrigins.length == 1 && (allowedOrigins[0] == null || allowedOrigins[0].trim().isEmpty()))) {
            // 默认只允许本地访问
            logger.warn("No allowed origins configured, using default localhost origins");
            return new String[]{"http://localhost:3000", "http://127.0.0.1:3000"};
        }
        
        // 过滤掉空字符串
        String[] filteredOrigins = Arrays.stream(allowedOrigins)
                .filter(origin -> origin != null && !origin.trim().isEmpty())
                .toArray(String[]::new);
        
        if (filteredOrigins.length == 0) {
            logger.warn("All configured origins are empty, using default localhost origins");
            return new String[]{"http://localhost", "http://127.0.0.1"};
        }
        
        logger.info("Using configured origins: {}", Arrays.toString(filteredOrigins));
        return filteredOrigins;
    }

    /**
     * 为Spring Security提供CORS配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        logger.info("=== CREATING CORS CONFIGURATION SOURCE FOR SPRING SECURITY ===");
        
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 设置允许的域名
        String[] origins = getAllowedOrigins();
        List<String> originsList = Arrays.asList(origins);
        configuration.setAllowedOrigins(originsList);
        
        // 临时允许所有来源（仅用于调试）
        // configuration.setAllowedOriginPatterns(Arrays.asList("*")); // 移除临时通配符配置
        
        // 设置允许的方法
        configuration.setAllowedMethods(Arrays.asList(allowedMethods));
        
        // 设置允许的请求头
        configuration.setAllowedHeaders(Arrays.asList(allowedHeaders));
        
        // 设置暴露的响应头
        configuration.setExposedHeaders(Arrays.asList(exposedHeaders));
        
        // 设置是否允许凭证
        configuration.setAllowCredentials(allowCredentials);
        
        // 设置预检请求缓存时间
        configuration.setMaxAge(maxAge);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        for (String path : corsPaths) {
            source.registerCorsConfiguration(path, configuration);
            logger.info("Registered CORS configuration for Spring Security path: {}", path);
        }
        
        logger.info("Spring Security CORS Configuration Source created with origins: {}", originsList);
        // logger.info("Spring Security CORS Configuration Source created with origin patterns: *"); // 移除临时日志
        logger.info("=== CORS CONFIGURATION SOURCE CREATION COMPLETE ===");
        
        return source;
    }
} 