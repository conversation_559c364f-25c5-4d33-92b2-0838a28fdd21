package com.ruoyi.common.security.config;

import com.ruoyi.common.security.interceptor.HeaderInterceptor;
import com.ruoyi.common.security.interceptor.LoggingInterceptor;
import com.ruoyi.common.security.interceptor.SqlInjectionInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import com.ruoyi.common.security.interceptor.GlobalLoggingFilter;

/**
 * 拦截器配置
 *
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer
{
    private static final Logger logger = LoggerFactory.getLogger(WebMvcConfig.class);
    
    @Autowired
    private LoggingInterceptor loggingInterceptor;
    
    @Autowired
    private SqlInjectionInterceptor sqlInjectionInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        logger.info("=== WEB MVC INTERCEPTOR REGISTRATION START ===");
        
        // SQL注入防护拦截器（最高优先级：-30）
        registry.addInterceptor(sqlInjectionInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(SecurityConstants.ALL_PERMIT_URLS)
                .order(-30);
        
        logger.info("SQL Injection Interceptor registered - Order: -30");
        logger.info("SQL Injection Interceptor exclude patterns: {}", 
            String.join(", ", SecurityConstants.ALL_PERMIT_URLS));
        
        // 注意：CORS 处理现在由 Spring Security 和 CorsSecurityConfig 负责
        logger.info("CORS handling is managed by Spring Security and CorsSecurityConfig");

        // 请求头拦截器（优先级：-10）
        registry.addInterceptor(getHeaderInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(SecurityConstants.ALL_PERMIT_URLS)
                .order(-10);
        
        logger.info("Header Interceptor registered - Order: -10");
        logger.info("Header Interceptor exclude patterns: {}", 
            String.join(", ", SecurityConstants.ALL_PERMIT_URLS));
        
        // 全局日志拦截器，优先级最低
        registry.addInterceptor(loggingInterceptor)
                .addPathPatterns("/**")
                .order(Integer.MAX_VALUE);
        
        logger.info("=== WEB MVC INTERCEPTOR REGISTRATION END ===");
    }

    /**
     * 自定义请求头拦截器
     */
    public HeaderInterceptor getHeaderInterceptor()
    {
        return new HeaderInterceptor();
    }

    @Bean
    public FilterRegistrationBean<GlobalLoggingFilter> globalLoggingFilter() {
        FilterRegistrationBean<GlobalLoggingFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new GlobalLoggingFilter());
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(Integer.MIN_VALUE); // 最早执行
        return registrationBean;
    }
}
