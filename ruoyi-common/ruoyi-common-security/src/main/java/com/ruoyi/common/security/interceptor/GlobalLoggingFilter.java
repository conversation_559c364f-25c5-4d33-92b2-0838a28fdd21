package com.ruoyi.common.security.interceptor;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

public class GlobalLoggingFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        System.out.println("[FILTER LOG] 请求接口: " + req.getMethod() + " " + req.getRequestURI());
        if (req.getQueryString() != null) {
            System.out.println("[FILTER LOG] QueryString: " + req.getQueryString());
        }
        chain.doFilter(request, response);
    }
} 