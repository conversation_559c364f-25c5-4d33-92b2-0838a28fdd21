package com.ruoyi.common.security.config;

/**
 * 安全配置常量
 *
 * <AUTHOR>
 */
public class SecurityConstants {

    /**
     * 公开接口白名单 - 不需要认证和token处理
     */
    public static final String[] PUBLIC_URLS = {
            "/auth/login",
            "/auth/logout",
            "/refresh",
            "/captchaImage",
            "/prod-api/auth/login",
            "/prod-api/auth/logout",
            "/prod-api/refresh",
            "/prod-api/captchaImage",
            "/talentbase/agentManage/**",
            "/talentbase/agentManagePool/**",
            "/talentbase/conversation/audio",
            "/talentbase/agentManageAnalyse/**",
            "/talentbase/offlineInterviewEvaluation/submit/**",
            "/talentbase/template/getFormat/**",
            "/version","/version/**","/talentbase/agentTodo/**",
            "/knowledgebase/base/getIdsByUserId/**",
    };

    /**
     * Swagger相关接口白名单
     */
    public static final String[] SWAGGER_URLS = {
            "/swagger-ui/**",
            "/v2/api-docs",
            "/swagger-resources/**",
            "/webjars/**"
    };

    /**
     * 静态资源白名单
     */
    public static final String[] STATIC_URLS = {
            "/static/**",
            "/favicon.ico",
            "/robots.txt"
    };

    /**
     * 所有不需要认证的URL
     */
    public static final String[] ALL_PERMIT_URLS = {
            "/auth/login",
            "/auth/logout",
            "/refresh",
            "/captchaImage",
            "/prod-api/auth/login",
            "/prod-api/auth/logout",
            "/prod-api/refresh",
            "/prod-api/captchaImage",
            "/swagger-ui/**",
            "/v2/api-docs",
            "/swagger-resources/**",
            "/webjars/**",
            "/static/**",
            "/favicon.ico",
            "/robots.txt",
            "/system/versionInfo",
            "/talentbase/versionInfo",
            "/talentbase/agentManageAnalyse/updateFileStatus",
            "/talentbase/agentManage/**",
            "/talentbase/agentManagePool/**",
            "/talentbase/conversation/audio",
            "/talentbase/agentManageAnalyse/**",
            "/talentbase/offlineInterviewEvaluation/submit/**",
            "/talentbase/template/getFormat/**",
            "/agentService/api/talent/improveInfo",
            "/talentbase/offlineInterview/finish/**",
            "/version","/version/**","/talentbase/agentTodo/**",
            "/knowledgebase/base/getIdsByUserId/**"
    };
} 