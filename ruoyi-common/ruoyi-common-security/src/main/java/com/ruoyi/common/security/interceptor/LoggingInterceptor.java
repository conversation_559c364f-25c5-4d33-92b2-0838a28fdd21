package com.ruoyi.common.security.interceptor;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class LoggingInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        System.out.println("[LOG] 请求接口: " + request.getMethod() + " " + request.getRequestURI());
        if (request.getQueryString() != null) {
            System.out.println("[LOG] QueryString: " + request.getQueryString());
        }
        return true;
    }
} 