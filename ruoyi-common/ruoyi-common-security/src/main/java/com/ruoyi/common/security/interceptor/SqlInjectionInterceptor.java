package com.ruoyi.common.security.interceptor;

import com.ruoyi.common.core.utils.SqlInjectionUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * SQL注入防护拦截器
 * 
 * <AUTHOR>
 */
@Component
public class SqlInjectionInterceptor implements HandlerInterceptor {
    
    private static final Logger log = LoggerFactory.getLogger(SqlInjectionInterceptor.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        String clientIp = getClientIp(request);
        
        log.debug("SQL注入防护拦截器检查 - URI: {}, Method: {}, Client IP: {}", requestURI, method, clientIp);
        
        // 检查URL参数
        if (checkUrlParameters(request, response)) {
            return false;
        }
        
        // 检查请求头参数
//        if (checkHeaderParameters(request, response)) {
//            return false;
//        }
        
        // 检查查询参数
        if (checkQueryParameters(request, response)) {
            return false;
        }
        
        // 检查POST参数（仅对POST请求）
        if ("POST".equalsIgnoreCase(method) && checkPostParameters(request, response)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查URL路径参数
     */
    private boolean checkUrlParameters(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String requestURI = request.getRequestURI();
        String[] pathSegments = requestURI.split("/");
        
        for (String segment : pathSegments) {
            if (SqlInjectionUtils.isSqlInjectionAttempt(segment)) {
                log.warn("检测到URL路径中的SQL注入尝试 - URI: '{}', 可疑段: '{}', 客户端IP: '{}'", 
                        requestURI, segment, getClientIp(request));
                sendErrorResponse(response, "请求路径包含非法字符");
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查请求头参数
     */
    private boolean checkHeaderParameters(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Enumeration<String> headerNames = request.getHeaderNames();
        
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            
            if (SqlInjectionUtils.isSqlInjectionAttempt(headerName) || 
                SqlInjectionUtils.isSqlInjectionAttempt(headerValue)) {
                log.warn("检测到请求头中的SQL注入尝试 - Header: '{}' = '{}', 客户端IP: '{}'", 
                        headerName, headerValue, getClientIp(request));
                sendErrorResponse(response, "请求头包含非法字符");
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查查询参数
     */
    private boolean checkQueryParameters(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String queryString = request.getQueryString();
        if (queryString != null && SqlInjectionUtils.isSqlInjectionAttempt(queryString)) {
            log.warn("检测到查询字符串中的SQL注入尝试 - Query: '{}', 客户端IP: '{}'", 
                    queryString, getClientIp(request));
            sendErrorResponse(response, "查询参数包含非法字符");
            return true;
        }
        
        // 检查单个查询参数
        Map<String, String[]> parameterMap = request.getParameterMap();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String paramName = entry.getKey();
            String[] paramValues = entry.getValue();
            
            if (SqlInjectionUtils.isSqlInjectionAttempt(paramName)) {
                log.warn("检测到参数名中的SQL注入尝试 - ParamName: '{}', 客户端IP: '{}'", 
                        paramName, getClientIp(request));
                sendErrorResponse(response, "参数名包含非法字符");
                return true;
            }
            
            for (String paramValue : paramValues) {
                if (SqlInjectionUtils.isSqlInjectionAttempt(paramValue)) {
                    log.warn("检测到参数值中的SQL注入尝试 - ParamName: '{}', ParamValue: '{}', 客户端IP: '{}'", 
                            paramName, paramValue, getClientIp(request));
                    sendErrorResponse(response, "参数值包含非法字符");
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 检查POST参数
     */
    private boolean checkPostParameters(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 对于POST请求，主要检查Content-Type为application/x-www-form-urlencoded的情况
        String contentType = request.getContentType();
        if (contentType != null && contentType.contains("application/x-www-form-urlencoded")) {
            // 表单参数已经在checkQueryParameters中检查过了
            return false;
        }
        
        // 对于JSON请求，可以在这里添加JSON内容的检查
        // 这里暂时跳过，因为JSON内容通常由Controller处理
        
        return false;
    }
    
    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json;charset=UTF-8");
        
        AjaxResult result = AjaxResult.error(400, message);
        String jsonResponse = objectMapper.writeValueAsString(result);
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(jsonResponse);
            writer.flush();
        }
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // 多次反向代理后会有多个IP值，第一个IP为真实IP
            int index = xForwardedFor.indexOf(",");
            if (index != -1) {
                return xForwardedFor.substring(0, index);
            } else {
                return xForwardedFor;
            }
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
} 