package com.ruoyi.common.security.aspect;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.annotation.RunningTime;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * 计算运行时间
 * TODO: 增加异常捕获，抛出具体异常并在控制台打印error
 * <AUTHOR>
 */
@Aspect
@Slf4j
@Component
public class RunningTimeAspect {


    @Pointcut("@annotation(runningTime)")
    public void cut(RunningTime runningTime) {

    }


    @Around("cut(runningTime)")
    public Object timeAround(ProceedingJoinPoint point, RunningTime runningTime) throws Throwable {
        long begin = System.currentTimeMillis();
        Object proceed = point.proceed();
        long end = System.currentTimeMillis();
        long time = end - begin;
        // 判断是否要携带执行时间返回
        if (runningTime.isResult()) {
            AjaxResult result = (AjaxResult) proceed;
            AjaxResult ajaxResult = new AjaxResult(result, time);
            return ajaxResult;
        }else{
            log.error(point.getSignature().getName() + "方法耗时：" + (end - begin) + " ms");
        }
        return proceed;
    }

}
