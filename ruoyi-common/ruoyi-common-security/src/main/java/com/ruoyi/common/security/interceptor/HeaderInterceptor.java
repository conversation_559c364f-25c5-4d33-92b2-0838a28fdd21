package com.ruoyi.common.security.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.entity.domain.system.LoginUser;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.context.SecurityContextHolder;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 *
 * <AUTHOR>
 */
public class HeaderInterceptor implements AsyncHandlerInterceptor
{
    private static final Logger logger = LoggerFactory.getLogger(HeaderInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        String clientIp = request.getRemoteAddr();
        
        logger.info("=== HEADER INTERCEPTOR CALLED ===");
        logger.info("Request URI: {}, Method: {}, Client IP: {}", requestURI, method, clientIp);
        logger.info("Handler type: {}", handler.getClass().getSimpleName());
        
        if (!(handler instanceof HandlerMethod))
        {
            logger.info("Not a HandlerMethod, skipping header processing");
            return true;
        }

        logger.info("Processing headers for HandlerMethod");
        
        SecurityContextHolder.setUserId(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USER_ID));
        SecurityContextHolder.setUserName(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USERNAME));
        SecurityContextHolder.setUserKey(ServletUtils.getHeader(request, SecurityConstants.USER_KEY));

        String token = SecurityUtils.getToken();
        logger.info("Token found: {}", StringUtils.isNotEmpty(token) ? "YES" : "NO");
        
        if (StringUtils.isNotEmpty(token))
        {
            LoginUser loginUser = AuthUtil.getLoginUser(token);
            if (StringUtils.isNotNull(loginUser))
            {
                logger.info("LoginUser found, verifying expiration");
                AuthUtil.verifyLoginUserExpire(loginUser);
                SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
                logger.info("LoginUser set in SecurityContext");
            } else {
                logger.info("No LoginUser found for token");
            }
        }
        
        logger.info("=== HEADER INTERCEPTOR COMPLETED ===");
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception
    {
        logger.info("=== HEADER INTERCEPTOR AFTER COMPLETION ===");
        logger.info("Cleaning up SecurityContext");
        SecurityContextHolder.remove();
        logger.info("=== HEADER INTERCEPTOR CLEANUP COMPLETED ===");
    }
}
