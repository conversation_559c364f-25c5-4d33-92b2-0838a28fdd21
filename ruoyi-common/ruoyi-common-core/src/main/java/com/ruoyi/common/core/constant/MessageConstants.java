package com.ruoyi.common.core.constant;

public class MessageConstants {

    // 企云巅项目前缀
    public static final String QYD_PROJECT_PREFIX = "qydTopic_";

    /**
     * 拼接项目前缀
     * @param constants
     * @return
     */
    public static String msgTopicConstants(String constants){
        return constants.toString();
    }

    //生产者-集群消息主题
    public static String CLUSTER_SINGLE_NOTICE_MESSAGE_OUTPUT= msgTopicConstants("singleNoticeMessage-out-0");
    //生产者-集群消息主题
    public static String CLUSTER_SINGLE_SMS_MESSAGE_OUTPUT= msgTopicConstants("singleSmsMessage-out-0");
    //生产者-集群消息主题
    public static String CLUSTER_MULTIPLE_NOTICE_MESSAGE_OUTPUT= msgTopicConstants("multipleNoticeMessage-out-0");
    /**
     * 生产者-人员申请绑定公司待办消息主题
     */
    public static String CLUSTER_COMPANY_BIND_USER_MESSAGE_OUTPUT= msgTopicConstants("companyBindUserMessage-out-0");

    /**
     * 生产者-人员申请绑定公司完成待办消息主题
     */
    public static String CLUSTER_COMPANY_BOUND_USER_MESSAGE_OUTPUT= msgTopicConstants("companyBoundUserMessage-out-0");

    /**
     * 生产者-测评人工批阅待办消息主题
     */
    public static String CLUSTER_EXAM_PENDING_REVIEW_MESSAGE_OUTPUT= msgTopicConstants("examPendingReviewMessage-out-0");

    /**
     * 生产者-测评人工批阅待办完成待办消息主题
     */
    public static String CLUSTER_EXAM_REVIEW_COMPLETED_MESSAGE_OUTPUT= msgTopicConstants("examReviewCompletedMessage-out-0");

    /**
     * 消费者-测评人工批阅待办消息主题
     */
    public static String CLUSTER_EXAM_PENDING_REVIEW_MESSAGE_INPUT= msgTopicConstants("examPendingReviewMessage-in-0");

    /**
     * 消费者-测评人工批阅待办完成待办消息主题
     */
    public static String CLUSTER_EXAM_REVIEW_COMPLETED_MESSAGE_INPUT= msgTopicConstants("examReviewCompletedMessage-in-0");

    /**
     * 消费者-待支付订单待办消息主题
     */
    public static String CLUSTER_COMPANY_BOUND_USER_MESSAGE_INPUT= msgTopicConstants("companyBoundUserMessage-in-0");

    /**
     * 消费者--已关闭订单待办消息主题
     */
    public static String CLUSTER_COMPANY_BIND_USER_MESSAGE_INPUT= msgTopicConstants("companyBindUserMessage-in-0");

    /**
     * 生产者-测评待完成消息主题
     */
    public static String CLUSTER_USER_EXAM_PENDING_MESSAGE_OUTPUT= msgTopicConstants("userExamPendingMessage-out-0");

    /**
     * 生产者-测评已完成消息主题
     */
    public static String CLUSTER_USER_EXAM_FINISH_MESSAGE_OUTPUT= msgTopicConstants("userExamFinishMessage-out-0");

    /**
     * 生产者-测评已过期消息主题
     */
    public static String CLUSTER_USER_EXAM_EXPIRE_MESSAGE_OUTPUT= msgTopicConstants("userExamExpireMessage-out-0");

    /**
     * 消费者-测评待完成消息主题
     */
    public static String CLUSTER_USER_EXAM_PENDING_MESSAGE_INPUT= msgTopicConstants("userExamPendingMessage-in-0");

    /**
     * 消费者-测评已完成消息主题
     */
    public static String CLUSTER_USER_EXAM_FINISH_MESSAGE_INPUT= msgTopicConstants("userExamFinishMessage-in-0");

    /**
     * 消费者-测评已过期消息主题
     */
    public static String CLUSTER_USER_EXAM_EXPIRE_MESSAGE_INPUT= msgTopicConstants("userExamExpireMessage-in-0");

    /**
     * 生产者-每日一题删除消息主题
     */
    public static String CLUSTER_QUESTION_ARRANGE_DELETE_MESSAGE_OUTPUT= msgTopicConstants("questionArrangeDeleteMessage-out-0");

    /**
     * 生产者-每日一题待完成消息主题
     */
    public static String CLUSTER_QUESTION_ARRANGE_PENDING_MESSAGE_OUTPUT= msgTopicConstants("questionArrangePendingMessage-out-0");

    /**
     * 生产者-每日一题已完成消息主题
     */
    public static String CLUSTER_QUESTION_ARRANGE_FINISH_MESSAGE_OUTPUT= msgTopicConstants("questionArrangeFinishMessage-out-0");

    /**
     * 生产者-每日一题已过期消息主题
     */
    public static String CLUSTER_QUESTION_ARRANGE_EXPIRE_MESSAGE_OUTPUT= msgTopicConstants("questionArrangeExpireMessage-out-0");

    /**
     * 消费者-每日一题删除消息主题
     */
    public static String CLUSTER_QUESTION_ARRANGE_DELETE_MESSAGE_INPUT= msgTopicConstants("questionArrangeDeleteMessage-in-0");

    /**
     * 消费者-每日一题待完成消息主题
     */
    public static String CLUSTER_QUESTION_ARRANGE_PENDING_MESSAGE_INPUT= msgTopicConstants("questionArrangePendingMessage-in-0");

    /**
     * 消费者-每日一题已完成消息主题
     */
    public static String CLUSTER_QUESTION_ARRANGE_FINISH_MESSAGE_INPUT= msgTopicConstants("questionArrangeFinishMessage-in-0");

    /**
     * 消费者-每日一题已过期消息主题
     */
    public static String CLUSTER_QUESTION_ARRANGE_EXPIRE_MESSAGE_INPUT= msgTopicConstants("questionArrangeExpireMessage-in-0");


    /**
     * 生产者-在线会议删除消息主题
     */
    public static String CLUSTER_MEETING_HIS_DELETE_MESSAGE_OUTPUT= msgTopicConstants("meetingHisDeleteMessage-out-0");

    /**
     * 生产者-在线会议待完成消息主题
     */
    public static String CLUSTER_MEETING_HIS_PENDING_MESSAGE_OUTPUT= msgTopicConstants("meetingHisPendingMessage-out-0");

    /**
     * 生产者-在线会议已完成消息主题
     */
    public static String CLUSTER_MEETING_HIS_FINISH_MESSAGE_OUTPUT= msgTopicConstants("meetingHisFinishMessage-out-0");

    /**
     * 生产者-在线会议已过期消息主题
     */
    public static String CLUSTER_MEETING_HIS_EXPIRE_MESSAGE_OUTPUT= msgTopicConstants("meetingHisExpireMessage-out-0");

    /**
     * 消费者-在线会议删除消息主题
     */
    public static String CLUSTER_MEETING_HIS_DELETE_MESSAGE_INPUT= msgTopicConstants("meetingHisDeleteMessage-in-0");

    /**
     * 消费者-在线会议待完成消息主题
     */
    public static String CLUSTER_MEETING_HIS_PENDING_MESSAGE_INPUT= msgTopicConstants("meetingHisPendingMessage-in-0");

    /**
     * 消费者-在线会议已完成消息主题
     */
    public static String CLUSTER_MEETING_HIS_FINISH_MESSAGE_INPUT= msgTopicConstants("meetingHisFinishMessage-in-0");

    /**
     * 消费者-在线会议已过期消息主题
     */
    public static String CLUSTER_MEETING_HIS_EXPIRE_MESSAGE_INPUT= msgTopicConstants("meetingHisExpireMessage-in-0");


    /**
     * 生产者-待支付订单待办消息主题
     */
    public static String CLUSTER_NOT_PAY_ORDER_MESSAGE_OUTPUT= msgTopicConstants("clusterNotPayOrderMessage-out-0");

    /**
     * 生产者-已关闭订单待办消息主题
     */
    public static String CLUSTER_CANCEL_ORDER_MESSAGE_OUTPUT= msgTopicConstants("clusterCancelOrderMessage-out-0");

    /**
     * 消费者-已关闭订单待办消息主题
     */
    public static String CLUSTER_CANCEL_ORDER_MESSAGE_INPUT= msgTopicConstants("clusterCancelOrderMessage-in-0");

    /**
     * 消费者-待支付订单待办消息主题
     */
    public static String CLUSTER_NOT_PAY_ORDER_MESSAGE_INPUT= msgTopicConstants("clusterNotPayOrderMessage-in-0");

    /**
     * 生产者-待办展示资源包
     */
    public static String CLUSTER_INSERT_DISPLAY_RESOURCE_MESSAGE_OUTPUT= msgTopicConstants("insertDisplayResourceMessage-out-0");

    /**
     * 生产者-待办取消展示资源包
     */
    public static String CLUSTER_CANCEL_DISPLAY_RESOURCE_MESSAGE_OUTPUT= msgTopicConstants("cancelDisplayResourceMessage-out-0");

    /**
     * 消费者-待办展示资源包
     */
    public static String CLUSTER_INSERT_DISPLAY_RESOURCE_MESSAGE_INPUT= msgTopicConstants("insertDisplayResourceMessage-in-0");

    /**
     * 消费者--待办取消展示资源包
     */
    public static String CLUSTER_CANCEL_DISPLAY_RESOURCE_MESSAGE_INPUT= msgTopicConstants("cancelDisplayResourceMessage-in-0");

    /**
     * 生产者-待办展示兑换券
     */
    public static String CLUSTER_INSERT_DISPLAY_VOUCHER_MESSAGE_OUTPUT= msgTopicConstants("insertDisplayVoucherMessage-out-0");

    /**
     * 生产者-待办取消展示兑换券
     */
    public static String CLUSTER_CANCEL_DISPLAY_VOUCHER_MESSAGE_OUTPUT= msgTopicConstants("cancelDisplayVoucherMessage-out-0");

    /**
     * 消费者-待办展示兑换券
     */
    public static String CLUSTER_INSERT_DISPLAY_VOUCHER_MESSAGE_INPUT= msgTopicConstants("insertDisplayVoucherMessage-in-0");

    /**
     * 消费者-待办取消展示兑换券
     */
    public static String CLUSTER_CANCEL_DISPLAY_VOUCHER_MESSAGE_INPUT= msgTopicConstants("cancelDisplayVoucherMessage-in-0");

    /**
     * 生产者-展示权益
     */
    public static String CLUSTER_INSERT_DISPLAY_INTERESTS_MESSAGE_OUTPUT= msgTopicConstants("insertDisplayInterestsMessage-out-0");

    /**
     * 生产者-取消展示权益
     */
    public static String CLUSTER_CANCEL_DISPLAY_INTERESTS_MESSAGE_OUTPUT= msgTopicConstants("cancelDisplayInterestsMessage-out-0");

    /**
     * 消费者-展示权益
     */
    public static String CLUSTER_INSERT_DISPLAY_INTERESTS_MESSAGE_INPUT= msgTopicConstants("insertDisplayInterestsMessage-in-0");
    /**
     * 消费者-取消展示权益
     */
    public static String CLUSTER_CANCEL_DISPLAY_INTERESTS_MESSAGE_INPUT= msgTopicConstants("cancelDisplayInterestsMessage-in-0");

    //生产者-广播消息主题
    public static String BROADCAST_MESSAGE_OUTPUT= msgTopicConstants("broadcast-out-0");
    //生产者-延时消息主题
    public static String DELAYED_MESSAGE_OUTPUT= msgTopicConstants("delayed-out-0");

    //消费者-集群消息主题
    public static String CLUSTER_SINGLE_NOTICE_MESSAGE_INPUT= msgTopicConstants("singleNoticeMessage-in-0");
    //消费者-集群消息主题
    public static String CLUSTER_SINGLE_SMS_MESSAGE_INPUT= msgTopicConstants("singleSmsMessage-in-0");
    //消费者-集群消息主题
    public static String CLUSTER_MULTIPLE_NOTICE_MESSAGE_INPUT= msgTopicConstants("multipleNoticeMessage-in-0");
    //消费者-广播消息主题
    public static String BROADCAST_MESSAGE_INPUT= msgTopicConstants("broadcast-in-0");
    //消费者-延时消息主题
    public static String DELAYED_MESSAGE_INPUT= msgTopicConstants("delayed-in-0");
}
