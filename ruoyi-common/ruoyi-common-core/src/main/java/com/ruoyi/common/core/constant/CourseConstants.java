package com.ruoyi.common.core.constant;

/**
 * <AUTHOR>
 * 课程相关的redisKey
 */
public class CourseConstants {
    /**
     * 课程组 一级key
     */
    public static final String COURSE_GROUP = "course_group:";

    /**
     * 课程组数据对象 二级key
     */
    public static final String COURSE_GROUP_DATA = "course_group_data:";


    /**
     * 冗余订单课程组信息
     */
    public static final String ORDER_COURSE_GROUP_REDUNDANCY = "order_course_group:";

    /**
     * 冗余订单兑换券信息
     */
    public static final String ORDER_VOUCHER_PAY_REDUNDANCY = "order_voucher_pay:";

    /**
     * 课程组数据Redis冗余时长 (分钟)
     */
    public static final Long ORDER_GROUP_REDUNDANCY_DURATION = 30L;

    /**
     * 课程组类型
     */
    public static final String GROUP_CLASSIFY = "classify";

    /**
     * 课程兑换卷
     */
    public static final String COURSE_GROUP_VOUCHER = "course_group_voucher:";

    /**
     * 课程推荐 二级key
     */
    public static final String COURSE_GROUP_RECOMMEND = "course_group_recommend:";

    /**
     * 课程 二级key
     */
    public static final String COURSE_GROUP_COURSE = "course";


}
