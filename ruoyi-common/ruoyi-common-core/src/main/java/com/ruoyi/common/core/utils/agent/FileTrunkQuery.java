package com.ruoyi.common.core.utils.agent;

import lombok.Data;

/**
 * 文件分块查询参数
 */
@Data
public class FileTrunkQuery {
    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 分块状态
     */
    private String trunkState;

    /**
     * 页码
     */
    private String pageNumber;

    /**
     * 每页大小
     */
    private String pageSize;

    /**
     * 构建查询参数
     */
    public static FileTrunkQuery build(String fileId, String trunkState, String pageNumber, String pageSize) {
        FileTrunkQuery query = new FileTrunkQuery();
        query.setFileId(fileId);
        query.setTrunkState(trunkState);
        query.setPageNumber(pageNumber);
        query.setPageSize(pageSize);
        return query;
    }
} 