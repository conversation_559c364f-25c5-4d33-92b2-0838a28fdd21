package com.ruoyi.common.entity.annotation;

import com.ruoyi.common.entity.domain.SysDictDataEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.FIELD) // 仅适用于字段
@Retention(RetentionPolicy.RUNTIME) // 在运行时可以获取注解信息
public @interface Dict {

    SysDictDataEnum type(); // 字典类型

    String field(); // 指定字段名称
}
