package com.ruoyi.common.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 待办任务信息对象 pd_task_info
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Data
public class PdTaskInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    private Long id;

    /**
     * 类型0:待办 1:任务
     */
    @Excel(name = "类型0:待办 1:任务")
    private Integer type;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 用户Id (合同到期统计数据是 负数)
     */
    @Excel(name = "用户Id")
    private Long userId;

    /**
     * 部门Id
     */
    @Excel(name = "部门Id")
    private Long deptId;

    /**
     * 图标
     */
    @Excel(name = "图标")
    private String icon;

    /**
     * 待办的状态 0成功 1失败
     * 如果是任务的话：
     * 状态 0:草稿 1:进行中 2:已完成 3:暂停 4:失败
     */
    @Excel(name = "状态 0成功 1失败")
    private Integer status;

    /**
     * 已读标识
     */
    @Excel(name = "已读标识")
    private Integer readFlag;

    /**
     * 事件Id
     */
    @Excel(name = "事件Id")
    private String eventId;

    /**
     * 事件类型
     */
    @Excel(name = "事件类型")
    private Integer eventType;

    /**
     * 事件记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "事件记录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    private Date eventTime;

    /**
     * 事件组Id
     */
    @Excel(name = "事件组Id")
    private String groupId;

    /**
     * 是否展示续期
     */
    @TableField(exist = false)
    private Integer showRenewalFlag;
}