package com.ruoyi.common.entity.domain.talent;

public enum InterviewStatusEnum {
    NOT_STARTED(0, "未开始"),
    IN_PROGRESS(1, "进行中"),
    COMPLETED(2, "已完成"),
    EXPIRED(3, "已过期");

    private final int code;
    private final String desc;

    InterviewStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static InterviewStatusEnum fromCode(int code) {
        for (InterviewStatusEnum value : InterviewStatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
} 