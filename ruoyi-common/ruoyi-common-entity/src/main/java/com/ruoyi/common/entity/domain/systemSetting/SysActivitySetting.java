package com.ruoyi.common.entity.domain.systemSetting;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 活动信息设置对象 sys_activity_setting
 * 
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
public class SysActivitySetting extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 设置Id */
    private Long id;

    /** 活动Id */
    @Excel(name = "活动Id")
    private Long activityId;

    /** 活动终端(字典) */
    @Excel(name = "活动终端(字典)")
    private String activityTerminal;

    /** 活动位置(字典) */
    @Excel(name = "活动位置(字典)")
    private String activityPosition;

    /** 图片地址 */
    @Excel(name = "图片地址")
    private String imgSrc;

    /** 展示时长，单位：秒 */
    @Excel(name = "展示时长，单位：秒")
    private Long duration;
}
