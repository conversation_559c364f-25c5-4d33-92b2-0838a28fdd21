package com.ruoyi.common.entity.domain.talent;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 个人档案表 tb_analyse_personal_file
 */
@Data
public class TbAnalysePersonalFile extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long personalId;
    private String fileName;
    private String pinyin;
    private Long fileSize;
    private String fileUrl;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /** 解析结果（0成功，1解析中，2失败） */
    private Integer status;

    /** 简历分析任务ID */
    private Long taskId;
} 