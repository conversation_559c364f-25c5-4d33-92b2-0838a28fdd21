package com.ruoyi.common.entity.domain.systemSetting;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 平台活动信息对象 sys_activity
 * 
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
public class SysActivity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 活动Id */
    private Long id;

    /** 活动名称 */
    @Excel(name = "活动名称")
    private String activityName;

    /** 优先级 */
    @Excel(name = "优先级")
    private Long activityPriority;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 状态：0:正常 1:禁用 */
    @Excel(name = "状态：0:正常 1:禁用")
    private Integer activityStatus;

    /** 跳转类型: 0:无跳转 1:链接 2:课程组 3:公告 */
    @Excel(name = "跳转类型: 0:链接 4:关联课程组 2:关联公告 -1:无跳转")
    private Integer redirectType;

    /** 跳转链接 */
    @Excel(name = "跳转链接")
    private String redirectLink;

    /** 跳转业务ID */
    @Excel(name = "跳转业务ID")
    private Long redirectId;

    /** 活动终端(字典) */
    @TableField(exist = false)
    private String activityTerminal;

    /** 活动位置(字典) */
    @TableField(exist = false)
    private String activityPosition;

    /**
     * 阶段：0：未开始，1：进行中，2：已结束
     */
    @TableField(exist = false)
    private Integer activityStage;
}
