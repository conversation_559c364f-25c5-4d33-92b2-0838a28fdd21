package com.ruoyi.common.entity.domain.shopping;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 商品优惠管理对象 sh_discount
 * 
 * <AUTHOR>
 * @date 2024-08-12
 */
@Data
public class ShDiscount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 优惠名称 */
    @Excel(name = "优惠名称")
    private String discountName;

    /** 折扣 */
    @Excel(name = "企业折扣")
    private Integer discountNum;

    /** 折扣 */
    @Excel(name = "个人折扣")
    private Integer discountPersonNum;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 是否允许叠加，0：否，1：是 */
    @Excel(name = "是否允许叠加，0：否，1：是")
    private Integer appendFlag;

    /** 折扣状态，0：草稿，1：已生效，2：已禁用，3：已失效 */
    @Excel(name = "折扣状态，0：草稿，1：已生效，2：已禁用，3：已失效")
    private Integer discountStatus;

    /** 商品类型，1：课程组，2：知享资源包 */
    @Excel(name = "商品类型，1：课程组，2：知享资源包")
    private Integer goodsType;

    @TableField(exist = false)
    private List<ShDiscountGoods> shDiscountGoodsList;

    /** 定时任务Id */
    @Excel(name = "定时任务Id")
    private Long jobId;
}
