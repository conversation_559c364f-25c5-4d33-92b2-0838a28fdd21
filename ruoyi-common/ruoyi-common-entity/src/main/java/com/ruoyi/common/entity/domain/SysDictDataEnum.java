package com.ruoyi.common.entity.domain;


/**
 * 试题类型枚举类
 */
public enum SysDictDataEnum {

    // 用户性别
    SYS_USER_SEX("sys_user_sex", "用户性别"),
    // 菜单状态
    SYS_SHOW_HIDE("sys_show_hide", "菜单状态"),
    // 系统开关
    SYS_NORMAL_DISABLE("sys_normal_disable", "系统开关"),
    // 任务状态
    SYS_JOB_STATUS("sys_job_status", "任务状态"),
    // 任务分组
    SYS_JOB_GROUP("sys_job_group", "任务分组"),
    // 系统是否
    SYS_YES_NO("sys_yes_no", "系统是否"),
    // 通知类型
    SYS_NOTICE_TYPE("sys_notice_type", "通知类型"),
    // 通知状态
    SYS_NOTICE_STATUS("sys_notice_status", "通知状态"),
    // 操作类型
    SYS_OPER_TYPE("sys_oper_type", "操作类型"),
    // 系统状态
    SYS_COMMON_STATUS("sys_common_status", "系统状态"),
    // 设备型号
    EQUIP("equip", "设备型号"),
    // 应用程序类型
    SYS_TERMINAL_TYPE("sys_terminal_type", "应用程序类型"),
    // 应用程序强制更新
    SYS_TERMINAL_UPDATE("sys_terminal_update", "应用程序强制更新"),
    // 学习分类
    STUDY_CLASSIFY_LIST("study_classify_list", "学习分类"),
    // 题库类别
    EXAM_BANK_TYPE("exam_bank_type", "题库类别"),
    // 考题类型
    EXAM_ITEM_TYPE("exam_item_type", "考题类型"),
    // 课程类型
    TR_COURSE_TYPE("tr_course_type", "课程类型"),
    // 课类型
    STUDY_LESSON_TYPE("study_lesson_type", "课类型"),
    // 计划内容类型
    PLAN_CONTENT_TYPE("plan_content_type", "计划内容类型"),
    // 协议类型
    SYS_AGREEMENT_TYPE("sys_agreement_type", "协议类型"),
    // 广告类型
    ADVERT_CONTENT_TYPE("advert_content_type", "广告类型"),
    // 轮播图类型
    SYS_ROTATION_TYPE("sys_rotation_type", "轮播图类型"),
    // 购买类型
    ORDER_BUY_TYPE("order_buy_type", "购买类型"),
    // 购买途径
    ORDER_BUY_WAY("order_buy_way", "购买途径"),
    // 支付方式
    ORDER_PAY_TYPE("order_pay_type", "支付方式"),
    // 兑换券分配类型
    VOUCHER_TRIGGER("voucher_trigger", "兑换券分配类型"),
    // 支付状态
    ORDER_PAY_STATE("order_pay_state", "支付状态"),
    // 订单状态
    ORDER_ALLOCATION_STATE("order_allocation_state", "订单状态"),
    // 人员审核状态
    SYS_EXAMINEPEOPLE_STATUS("sys_examinepeople_status", "人员审核状态"),
    // 系统通知管理类型
    SYS_NOTIFICATION_TYPE("sys_notification_type", "系统通知管理类型"),
    // 套餐是否可以购买
    TRAIN_PACKAGE_CANBUY("train_package_canBuy", "套餐是否可以购买"),
    // 会议状态
    SYS_MEETINGHIS_STATUS("sys_meetingHis_status", "会议状态"),
    // App用户类别管理
    APP_USER_CATEGORY("app_user_category", "App用户类别管理"),
    // 法务讲堂类型
    LAW_FORUM_TYPE("law_forum_type", "法务讲堂类型"),
    // 律师擅长领域
    LAWYER_FIELD("lawyer_field", "律师擅长领域"),
    // 企业服务功能类别
    ENS_FUNC_TYPE("ens_func_type", "企业服务功能类别"),
    // 区域管理开关
    SYS_DISTRICT_MANAGE("sys_district_manage", "区域管理开关"),
    // 客户经理可用状态
    SYS_CLIENTHANAGER_STATUS("sys_clientHanager_status", "客户经理可用状态"),
    // 企业行业
    INDUSTRY_TYPE("industry_type", "企业行业"),
    // 企业规模
    SCALE_TYPE("scale_type", "企业规模"),
    // 企业订单状态
    COMPANY_ORDER_STATUS_TYPE("company_order_status_type", "企业订单状态"),
    // 企业订单事件
    COMPANY_ORDER_EVENT_TYPE("company_order_event_type", "企业订单事件"),
    // 投诉来源
    COMPLAINT_SOURCE_TYPE("complaint_source_type", "投诉来源"),
    // 投诉类型
    COMPLAINT_TYPE("complaint_type", "投诉类型"),
    // 处罚方式
    PUNISH_TYPE("punish_type", "处罚方式"),
    // 素材类型
    MATERIAL_TYPE("material_type", "素材类型"),
    // 公司类型
    SYS_DEPT_TYPE("sys_dept_type", "公司类型"),
    // 时长规格
    DURATION_SPECS("duration_specs", "时长规格"),
    // 有效期
    RESOURCE_VALIDITY("resource_validity", "有效期"),
    // 存储规格
    STORAGE_SPECS("storage_specs", "存储规格"),
    // 资源包类型
    RESOURCE_PACK_TYPE("resource_pack_type", "资源包类型"),
    // 资源包名称
    RESOURCE_PACKAGE_NAME("resource_package_name", "资源包名称"),
    // 套餐时长
    PACKAGE_DURATION("package_duration", "套餐时长"),
    // 付费类型
    PAY_TYPE("pay_type", "付费类型"),
    // 订单-套餐时长
    ORDER_PACKAGE_TIME("order_package_time", "订单-套餐时长"),
    // 订单-服务平台
    ORDER_SERVICE_PLATFORM("order_service_platform", "订单-服务平台"),
    // 订单-服务内容
    ORDER_SERVICE_CONTENT("order_service_content", "订单-服务内容"),
    // 订单-设备型号
    ORDER_EQUIPMENT_MODEL("order_equipment_model", "订单-设备型号"),
    // 订单-发货方式
    ORDER_DELIVERIES("order_deliveries", "订单-发货方式"),
    // 订单-订单状态
    ORDER_STATE("order_state", "订单-订单状态"),
    // 订单-服务资源
    ORDER_SERVICE_RESOURCE("order_service_resource", "订单-服务资源"),
    // 订单-实施服务满意度
    ORDER_IMP_SATISFACTION("order_imp_Satisfaction", "订单-实施服务满意度"),
    // 推荐展示位置
    RECOMMEND_DISPLAY_LOCATION("recommend_display_location", "推荐展示位置"),
    // 账户-交易渠道
    TRADING_CHANNELS("trading_channels", "账户-交易渠道"),
    // 充值规格
    RECHARGE_SPECS("recharge_specs", "充值规格"),
    // 课来源标签
    LESSON_SOURCE("lesson_source", "课来源标签"),
    // 活动终端类型
    ACTIVITY_TERMINAL("activity_terminal", "活动终端类型"),
    // 活动位置展示
    ACTIVITY_POSITION("activity_position", "活动位置展示"),
    // 问卷试题类型
    SURVEY_ITEM_TYPE("survey_item_type", "问卷试题类型"),
    // 知识库类型
    KNOWLEDGE_BASE_TYPE("knowledge_base_type", "知识库类型"),
    // 企业资源配置
    COMPANY_RES_CONFIG("company_res_config", "企业资源配置"),
    // 招聘渠道信息
    RECRUITMENT_CHANNELS("recruitment_channels", "招聘渠道信息"),
    // 学历信息
    EDUCATION("education", "学历信息"),
    // 就业状态
    EMPLOYMENT_STATUS("employment_status", "就业状态"),
    // 合同类型
    CONTRACT_TYPE("contract_type", "合同类型");


    private final String name;
    private final String desc;

    SysDictDataEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static SysDictDataEnum getByName(String name) {
        for (SysDictDataEnum dictType : SysDictDataEnum.values()) {
            if (dictType.getName().equals(name)) {
                return dictType;
            }
        }
        return null;
    }

}
