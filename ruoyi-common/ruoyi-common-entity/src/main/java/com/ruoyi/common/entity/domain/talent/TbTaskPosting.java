package com.ruoyi.common.entity.domain.talent;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.common.entity.annotation.Dict;
import com.ruoyi.common.entity.domain.SysDictDataEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 查找人才任务对象 tb_task_posting
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
public class TbTaskPosting extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 任务Id
     */
    private Long id;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 职位库Id
     */
    private Long baseId;

    /**
     * 任务名称
     */
    @Excel(name = "任务名称")
    private String taskName;

    /**
     * 渠道
     */
    @Excel(name = "渠道字典")
    private Long channel;

    /**
     * 职位类型
     */
    @Excel(name = "职位类型")
    private Integer positionType;

    /**
     * 职位名称
     */
    @Excel(name = "职位名称")
    private String positionName;

    /**
     * 筛选人数
     */
    @Excel(name = "筛选人数")
    private Long screeningCount;

    /**
     * 完成人数
     */
    @Excel(name = "完成人数")
    private Long finishCount;

    /**
     * 最低学历
     */
    @Excel(name = "最低学历")
    private Integer minimumEducation;

    /**
     * 工作经验下限
     */
    private Integer experienceLowerBound;

    /**
     * 工作经验上限
     */
    private Integer experienceUpperBound;
    /**
     * 跳槽频率年限范围
     */
    private Integer jobHoppingYears;
    /**
     * 跳槽频率次数限制
     */
    private Integer jobHoppingCount;
    /**
     * 薪资范围下限
     */
    private Integer salaryRangeLowerBound;
    /**
     * 薪资范围上限
     */
    private Integer salaryRangeUpperBound;

    /**
     * 附加条件
     */
    @Excel(name = "附加条件")
    private String screeningConditions;

    /**
     * 状态 0:草稿 1:进行中 2:已完成 3:暂停 4:失败
     */
    @Excel(name = "状态 0:草稿 1:进行中 2:已完成 3:暂停 4:失败")
    private Integer status;

    /**
     * 学历评分侧重
     */
    @Excel(name = "学历评分侧重")
    private Long educationWeight;

    /**
     * 工作经验评分侧重
     */
    @Excel(name = "工作经验评分侧重")
    private Long workExperienceWeight;

    /**
     * 跳槽频率评分侧重
     */
    @Excel(name = "跳槽频率评分侧重")
    private Long jobHoppingRateWeight;

    /**
     * 薪资范围评分侧重
     */
    @Excel(name = "薪资范围评分侧重")
    private Long salaryRangeWeight;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 及格分
     */
    @Excel(name = "及格分")
    private Double passScore;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 查询模块
     */
    private Integer queryModules;

    /**
     * 查询开始时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date queryStartTime;

    /**
     * 查询结束时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date queryEndTime;

    // 筛选条件
    @TableField(exist = false)
    private String searchCondition;

    // 学历字段
    @TableField(exist = false)
    @Dict(type = SysDictDataEnum.EDUCATION, field = "minimumEducation")
    private String educationName;

    @TableField(exist = false)
    private Integer openFlag;

    // 拼接附加条件
    public void makeSearchCondition() {
        List<String> list = new ArrayList<String>();
        if (this.getMinimumEducation() != null) {
            list.add("最低学历：" + this.getEducationName());
        }
        if (this.getExperienceLowerBound() != null) {
            list.add("工作经验下限：" + this.getExperienceLowerBound() + "年");
        }
        if (this.getExperienceUpperBound() != null) {
            list.add("工作经验上限：" + this.getExperienceUpperBound() + "年");
        }
        if (this.getJobHoppingYears() != null) {
            list.add("跳槽频率年限范围：" + this.getJobHoppingYears() + "年");
        }
        if (this.getJobHoppingCount() != null) {
            list.add("跳槽频率次数限制：" + this.getJobHoppingCount() + "次");
        }
        if (this.getSalaryRangeLowerBound() != null) {
            list.add("薪资范围下限：" + this.getSalaryRangeLowerBound() + "k");
        }
        if (this.getSalaryRangeUpperBound() != null) {
            list.add("薪资范围上限：" + this.getSalaryRangeUpperBound() + "k");
        }
        if (!list.isEmpty()) {
            Object[] array = list.toArray();
            this.setSearchCondition(StringUtils.join(array, "\n"));
        }
    }
}
