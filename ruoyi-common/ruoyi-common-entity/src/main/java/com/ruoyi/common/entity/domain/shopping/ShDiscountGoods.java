package com.ruoyi.common.entity.domain.shopping;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.entity.domain.TrCourseGroup;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 商品优惠关联商品对象 sh_discount_goods
 * 
 * <AUTHOR>
 * @date 2024-08-12
 */
@Data
public class ShDiscountGoods extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 商品优惠ID */
    @Excel(name = "商品优惠ID")
    private Long discountId;

    /** 商品ID */
    @Excel(name = "商品ID")
    private Long goodsId;

    /** 商品类型，1：课程组，2：知享资源包 */
    @Excel(name = "商品类型，1：课程组，2：知享资源包")
    private Integer goodsType;

    /**
     * 课程组
     */
    @TableField(exist = false)
    private TrCourseGroup trCourseGroup;
}
