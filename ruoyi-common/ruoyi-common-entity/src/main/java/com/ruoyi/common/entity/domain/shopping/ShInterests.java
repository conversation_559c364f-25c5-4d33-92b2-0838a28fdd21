package com.ruoyi.common.entity.domain.shopping;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 权益管理对象 sh_interests
 * 
 * <AUTHOR>
 * @date 2024-08-12
 */

@Data
public class ShInterests extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 权益名称 */
    @Excel(name = "权益名称")
    private String interestsName;

    /** 折扣 */
    @Excel(name = "企业折扣")
    private Integer interestsNum;

    /** 折扣 */
    @Excel(name = "个人折扣")
    private Integer interestsPersonNum;

    /** 有效期（单位：天） */
    @Excel(name = "有效期", readConverterExp = "单=位：天")
    private Long validDays;

    /** 是否允许叠加，0：否，1：是 */
    @Excel(name = "是否允许叠加，0：否，1：是")
    private Integer appendFlag;

    /** 折扣状态，0：已禁用，1：已生效 */
    @Excel(name = "折扣状态，0：已禁用，1：已生效")
    private Integer interestsStatus;

    /** 商品类型，1：课程组，2：知享资源包 */
    @Excel(name = "商品类型，1：课程组，2：知享资源包")
    private Integer goodsType;

    @TableField(exist = false)
    private List<ShInterestsGoods> shInterestsGoodsList;

    @TableField(exist = false)
    private List<ShInterestsCompany> shInterestsCompanyList;
}
