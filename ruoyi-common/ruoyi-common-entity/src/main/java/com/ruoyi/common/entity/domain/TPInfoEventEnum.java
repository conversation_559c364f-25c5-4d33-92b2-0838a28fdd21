package com.ruoyi.common.entity.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

public enum TPInfoEventEnum {

    KNOWLEDGE               (0, "知识库"),
    TALENT                  (1, "人才库"),
    CONTRACT                (2, "合同解析"),
    ONLINE_INTERVIEW        (3, "线上面试"),
    OFFLINE_INTERVIEW       (4, "线下面试"),
    CONTRACT_EXPIRED        (5, "合同到期"),
    TALENT_TASK_PROGRESS    (6, "寻才任务进度"),
    RESUME_ANALYSIS_PROGRESS(7, "简历分析进度");

    private final int code;
    @Getter
    private final String desc;

    TPInfoEventEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue          // 序列化时返回 code
    public int getCode() {
        return code;
    }

    @JsonCreator        // 反序列化时把 int 转成枚举
    public static TPInfoEventEnum fromCode(int code) {
        for (TPInfoEventEnum e : values()) {
            if (e.code == code) {
                return e;
            }
        }
        throw new IllegalArgumentException("Unknown TPInfoEventEnum code: " + code);
    }
}