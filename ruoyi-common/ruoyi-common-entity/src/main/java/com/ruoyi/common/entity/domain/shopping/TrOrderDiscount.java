package com.ruoyi.common.entity.domain.shopping;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 订单折扣信息对象 tr_order_discount
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
public class TrOrderDiscount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 订单Id */
    @Excel(name = "订单Id")
    private Long orderId;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNum;

    /** 折扣ID */
    @Excel(name = "折扣ID")
    private Long discountId;

    /** 优惠类型，1：商品优惠折扣；2：权益优惠折扣； */
    @Excel(name = "优惠类型，1：商品优惠折扣；2：权益优惠折扣；")
    private Integer discountType;

    /** 折扣名称 */
    @Excel(name = "折扣名称")
    private String discountName;

    /** 折扣 */
    @Excel(name = "企业折扣")
    private Integer discountNum;

    /** 折扣 */
    @Excel(name = "个人折扣")
    private Integer discountPersonNum;

    /** 开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 过期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 是否允许叠加，0：否，1：是 */
    @Excel(name = "是否允许叠加，0：否，1：是")
    private Integer appendFlag;

    /** 商品ID */
    @Excel(name = "商品ID")
    private Long goodsId;

    /** 商品类型，1：课程组，2：知享资源包 */
    @Excel(name = "商品类型，1：课程组，2：知享资源包")
    private Integer goodsType;
}
