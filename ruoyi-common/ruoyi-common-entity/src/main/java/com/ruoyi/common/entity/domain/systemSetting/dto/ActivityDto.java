package com.ruoyi.common.entity.domain.systemSetting.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.entity.domain.systemSetting.SysActivitySetting;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 活动管理对象
 * <AUTHOR>
 * @Date 2024-11-25
 */
@Data
public class ActivityDto {

    /** 活动Id */
    private Long id;

    /** 活动名称 */
    private String activityName;

    /** 优先级 */
    private Long activityPriority;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date startTime;

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
        updateActivityStage();
    }

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date endTime;

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
        updateActivityStage();
    }

    /***
     * 活动阶段
     */
    private Integer activityStage;

    /***
     * 活动阶段显示
     */
    private String activityStageDisplay;

    /**
     * 根据当前时间更新活动阶段
     */
    public void updateActivityStage() {
        Date now = new Date(); // 获取当前时间
        if (this.startTime.after(now)) {
            this.activityStage = 0;
            // 如果当前时间在开始时间之前，则活动未开始
            this.activityStageDisplay = "未开始";
        } else {
            // 如果当前时间在开始时间之后或等于开始时间
            if (this.endTime == null || this.endTime.after(now)) {
                this.activityStage = 1;
                // 如果结束时间为空或者当前时间在结束时间之前，则活动进行中
                this.activityStageDisplay = "进行中";
            } else {
                this.activityStage = 2;
                // 如果当前时间在结束时间之后，则活动已结束
                this.activityStageDisplay = "已结束";
            }
        }
    }

    /** 状态：0:正常 1:禁用 */
    private Integer activityStatus;

    /** 跳转类型: 0:无跳转 1:链接 2:课程组 3:公告 */
    private Integer redirectType;

    /** 跳转链接 */
    private String redirectLink;

    /** 跳转业务ID */
    private Long redirectId;

    /**
     * 活动设置列表
     */
    private List<SysActivitySetting> activitySettings;

    /**
     * 业务平台数量
     */
    private Integer platformCount;

    /**
     * 创建时间
     */
    @JsonIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
