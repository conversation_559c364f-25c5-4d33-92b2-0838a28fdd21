package com.ruoyi.auth.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户注册对象
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("用户注册对象")
public class RegisterBody
{
    @ApiModelProperty("用户名")
    private String nickname;
    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String telephone;
    /**
     * 用户密码
     */
    @ApiModelProperty("用户密码")
    private String password;
    /**
     * 手机验证码
     */
    @ApiModelProperty("验证码")
    private String veriCode;
}
