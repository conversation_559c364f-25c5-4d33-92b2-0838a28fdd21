# Multipart请求调试修复

## 问题分析

文件上传又出现了 `422 Unprocessable Entity: "Field required"` 错误，说明文件参数没有正确传递给Python服务。

## 修复方案

### 1. 创建专门的multipart RestTemplate

为multipart请求创建专门的RestTemplate，确保消息转换器配置正确：

```java
private RestTemplate createMultipartRestTemplate() {
    RestTemplate restTemplate = new RestTemplate();
    restTemplate.getMessageConverters().clear();
    
    // 添加表单消息转换器，支持multipart/form-data
    AllEncompassingFormHttpMessageConverter formConverter = 
        new AllEncompassingFormHttpMessageConverter();
    restTemplate.getMessageConverters().add(formConverter);
    
    // 添加字符串和JSON转换器
    restTemplate.getMessageConverters().add(new StringHttpMessageConverter(UTF_8));
    restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());
    
    return restTemplate;
}
```

### 2. 增强调试日志

添加详细的调试日志来跟踪文件处理过程：

```java
log.info("[{}] 检测到文件数量: {}", requestId, fileMap.size());

for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
    MultipartFile file = entry.getValue();
    log.info("[{}] 处理文件参数: {} = {}, 是否为空: {}", 
        requestId, entry.getKey(), 
        file != null ? file.getOriginalFilename() : "null", 
        file == null || file.isEmpty());
}
```

## 测试步骤

1. **无需重启应用** - 这个修复会立即生效
2. **测试Excel导入**：
   ```
   POST /agentService/api/kbUselessKeywords/importExcel
   Content-Type: multipart/form-data
   ```
3. **查看日志输出**：确认文件是否正确处理

## 预期日志输出

**成功的情况**：
```
[REQ_xxx] 检测到文件数量: 1
[REQ_xxx] 处理文件参数: file = example.xlsx, 是否为空: false
[REQ_xxx] 成功添加文件: file = example.xlsx, 大小: 12345 bytes
[REQ_xxx] 发送multipart请求到: http://localhost:8000/agentService/api/kbUselessKeywords/importExcel
[REQ_xxx] multipart请求成功 - 状态: 200 OK
```

**失败的情况**：
```
[REQ_xxx] 检测到文件数量: 0
[REQ_xxx] 跳过空文件: file
```

## 可能的问题和解决方案

### 问题1：文件参数名不匹配
- **现象**：日志显示文件数量为0或文件为空
- **原因**：前端发送的文件参数名与后端期望的不一致
- **解决**：检查前端FormData中的文件参数名

### 问题2：文件内容丢失
- **现象**：文件参数存在但大小为0
- **原因**：文件读取或传输过程中丢失
- **解决**：检查文件读取和ByteArrayResource创建过程

### 问题3：消息转换器配置错误
- **现象**：发送请求时出现转换错误
- **原因**：RestTemplate的消息转换器不支持multipart
- **解决**：使用专门的multipart RestTemplate

## 调试技巧

### 1. 检查前端请求
```javascript
// 确保FormData正确构建
const formData = new FormData();
formData.append('file', file); // 参数名必须与后端期望一致

// 检查文件是否正确添加
console.log('FormData entries:');
for (let [key, value] of formData.entries()) {
    console.log(key, value);
}
```

### 2. 检查后端日志
- 查看文件数量检测日志
- 查看文件参数处理日志
- 查看请求发送日志

### 3. 直接测试Python服务
使用Postman或curl直接测试Python服务，确认接口本身是否正常：
```bash
curl -X POST \
  http://localhost:8000/api/kbUselessKeywords/importExcel \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@example.xlsx'
```

## 如果仍有问题

如果这个修复还不能解决问题，可能需要：

1. **检查Spring Boot配置**：
   ```yaml
   spring:
     servlet:
       multipart:
         max-file-size: 100MB
         max-request-size: 200MB
   ```

2. **检查Python服务接口定义**：
   - 确认参数名是否为 `file`
   - 确认接口是否正确处理multipart请求

3. **使用更简单的代理方式**：
   - 考虑使用Spring Cloud Gateway
   - 或者直接转发原始请求流

这个修复应该能解决multipart请求的问题！
